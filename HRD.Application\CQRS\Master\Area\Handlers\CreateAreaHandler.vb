Imports System.Threading
Imports HRD.Application
Imports HRD.Domain
Imports MediatR

Public Class CreateAreaHandler
    Implements IRequestHandler(Of CreateAreaCommand, Boolean)

    Private ReadOnly _uow As IUnitOfWork

    Public Sub New(uow As IUnitOfWork)
        _uow = uow
    End Sub



    Public Async Function Handle(request As CreateAreaCommand, cancellationToken As CancellationToken) As Task(Of Boolean) Implements IRequestHandler(Of CreateAreaCommand, Boolean).Handle
        Dim area As New tm_area With {
            .KodeArea = request.KodeArea,
            .NamaArea = request.NamaArea,
            .CreatedBy = request.CreatedBy,
            .CreatedDate = request.CreatedDate,
            .ModifiedBy = request.ModifiedBy,
            .ModifiedDate = request.ModifiedDate,
            .KodeNIK_Tetap = request.KodeNIK_Tetap,
            .KodeNIK_Kontrak = request.KodeNIK_Kontrak,
            .NoRekeningBank = request.NoRekeningBank,
            .TglCustOffCuti = request.TglCustOffCuti
        }

        Await _uow.Repository(Of tm_area).AddAsync(area)
        Return _uow.Save()



    End Function
End Class
