﻿Imports HRD.Application
Imports HRD.Domain
Imports StructureMap
Imports HRD.Helpers
Public Class clsRPT
    Public Function GetRpt_SlipTHR(cabang_id As String, periode As Date, Optional karyawan_id As String = Nothing) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of ThrService)()
        Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tr_thr) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            Dim o = os.Where(Function(f) f.Cabang_id = cabang_id And f.PeriodeDate.Year = periode.Year And f.PeriodeDate.Month = periode.Month).FirstOrDefault
            If o Is Nothing Then
                Return Nothing
            End If
            If karyawan_id <> Nothing Then
                Dim line = o.tr_thr_line.Where(Function(t) t.Karyawan_id = karyawan_id).FirstOrDefault
                o.tr_thr_line.Clear()
                If line IsNot Nothing Then
                    o.tr_thr_line.Add(line)
                End If
            End If

            Dim rpt As New rpt_slip_thr
            rpt.ObjectDataSource1.DataSource = o

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_LapPajakTahunan(cabang_id As String, tahun As DateTime) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()

        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal >= startDate And f.Tanggal <= endDate And f.Cuti_b = True)

            os = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Berhenti_b <> True)

            Dim rpt As New rpt_pajaktahunan
            rpt.ObjectDataSource1.DataSource = os.ToList

            Dim cabang = myFunction.tm_cabang(cabang_id)

            rpt.Periode = tahun
            rpt.txt_periode.Text = Format(tahun, "yyyy")
            rpt.txt_perusahaan.Text = $"{cabang.tm_area.NamaArea} : {cabang.NamaCabang}"
            'rpt.pTahun.Value = Format(tahun, "yyyy").ToString

            Return rpt
        End If




    End Function
    Public Function GetRpt_LapPajakBulanan(cabang_id As String, bulan As DateTime) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()

        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal >= startDate And f.Tanggal <= endDate And f.Cuti_b = True)

            os = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Berhenti_b <> True)

            Dim rpt As New rpt_pajakbulanan
            rpt.ObjectDataSource1.DataSource = os.ToList

            Dim cabang = myFunction.tm_cabang(cabang_id)

            rpt.Periode = bulan
            rpt.txt_periode.Text = Format(bulan, "MMM yyyy")
            rpt.txt_perusahaan.Text = $"{cabang.tm_area.NamaArea} : {cabang.NamaCabang}"
            'rpt.pTahun.Value = Format(tahun, "yyyy").ToString

            Return rpt
        End If




    End Function
    Public Function GetRpt_LapPajakPerKaryawan(cabang_id As String, tahun As DateTime, Optional karyawan_id As String = Nothing) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()

        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal >= startDate And f.Tanggal <= endDate And f.Cuti_b = True)
            If karyawan_id <> Nothing Then
                os = os.Where(Function(f) f.Id = karyawan_id)
            End If
            os = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Berhenti_b <> True)

            Dim rpt As New rpt_PajakPerKaryawan
            rpt.ObjectDataSource1.DataSource = os.ToList

            rpt.txt_periode.Text = Format(tahun, "yyyy")

            rpt.pTahun.Value = Format(tahun, "yyyy").ToString

            Return rpt
        End If




    End Function
    Public Function GetRpt_RekapPinjaman(cabang_id As String, endDate As Date) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult

        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            Dim filteredKaryawan = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso Not f.Berhenti_b).
                Select(Function(k) New With {
                .Karyawan = k,
                .TotalPinjaman = k.tr_register_pinjaman.Sum(Function(p) p.Amount),
                .SaldoPinjaman = k.tr_register_pinjaman.Sum(Function(p) p.tr_pinjaman_move.Sum(Function(m) m.Amount))
            }).
            Where(Function(k) k.SaldoPinjaman < k.TotalPinjaman).
            Select(Function(k) k.Karyawan).
            ToList()

            Dim rpt As New rpt_rekap_pinjaman()
            rpt.ObjectDataSource1.DataSource = filteredKaryawan
            rpt.pEndDate.Value = endDate

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_LapCutiPerKaryawan(cabang_id As String, startDate As DateTime, endDate As DateTime, Optional karyawan_id As String = Nothing, Optional bGenerateAbsensi As Boolean = True) As DevExpress.XtraReports.UI.XtraReport
        Dim tgl1 As DateTime = Format(startDate, "yyyy-MM-dd 00:00:00")
        Dim tgl2 As DateTime = Format(endDate, "yyyy-MM-dd 23:59:59")
        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()

        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal >= startDate And f.Tanggal <= endDate And f.Cuti_b = True)
            If karyawan_id <> Nothing Then
                os = os.Where(Function(f) f.Id = karyawan_id)
            End If
            os = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Berhenti_b <> True)

            Dim rpt As New rpt_cuti_perkaryawan
            rpt.ObjectDataSource1.DataSource = os.ToList


            rpt.startDate = tgl1
            rpt.endDate = tgl2

            rpt.txt_startDate.Text = Format(tgl1, "dd MMM yyyy")
            rpt.txt_endDate.Text = Format(tgl2, "dd MMM yyyy")

            Return rpt
        End If




    End Function
    Public Function GetRpt_LapAbsensiPerKaryawan(cabang_id As String, startDate As DateTime, endDate As DateTime, Optional karyawan_id As String = Nothing, Optional bGenerateAbsensi As Boolean = True) As DevExpress.XtraReports.UI.XtraReport
        Dim tgl1 As DateTime = Format(startDate, "yyyy-MM-dd 00:00:00")
        Dim tgl2 As DateTime = Format(endDate, "yyyy-MM-dd 23:59:59")
        Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()

        If bGenerateAbsensi Then
            Serv.GenerateAbsensi(cabang_id, startDate, endDate).GetAwaiter.GetResult()
        End If


        Dim r = Serv.GetAbsensisAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tr_absensi) = r.Output
            os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal >= startDate And f.Tanggal <= endDate)
            If karyawan_id <> Nothing Then
                os = os.Where(Function(f) f.karyawan_id = karyawan_id)
            End If

            'Dim _alldays As Integer = myFunction.HitungJumlahHariKerja(cabang_id, 1, tgl1.Date, tgl2.Date)
            Dim _alldays As Integer = 0
            'Dim jmlHariKerja = myFunction.HitungJumlahHariKerja(cabang_id, 0, tgl1.Date, tgl2.Date) 'myFunction.CalculateWeekdays(cabang_id, tgl1, tgl2, _alldays)
            Dim jmlHariKerja = myFunction.CalculateWeekdays(cabang_id, tgl1.Date, tgl2.Date, _alldays) 'myFunction.CalculateWeekdays(cabang_id, tgl1, tgl2, _alldays)

            Dim rpt As New rpt_absensi_perkaryawan
            rpt.ObjectDataSource1.DataSource = os.ToList



            rpt.pAlldays = _alldays
            rpt.phariKerja = jmlHariKerja

            rpt.startDate = tgl1
            rpt.endDate = tgl2

            rpt.txt_startDate.Text = Format(tgl1, "dd MMM yyyy")
            rpt.txt_endDate.Text = Format(tgl2, "dd MMM yyyy")

            Return rpt
        End If




    End Function
    Public Function GetRpt_RekapLembur(cabang_id As String, startDate As Date, endDate As Date) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            Dim o = os.Where(Function(f) f.Cabang_id = cabang_id And f.Berhenti_b <> True)
            If o Is Nothing Then
                Return Nothing
            End If

            Dim rpt As New rpt_rekap_lembur
            rpt.ObjectDataSource1.DataSource = o.ToList
            rpt.pStartDate.Value = startDate
            rpt.pEndDate.Value = endDate

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_RekapAbsen(cabang_id As String, startDate As Date, endDate As Date) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = Serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            Dim o = os.Where(Function(f) f.Cabang_id = cabang_id And f.Berhenti_b <> True)
            If o Is Nothing Then
                Return Nothing
            End If

            Dim rpt As New rpt_rekap_absen
            rpt.ObjectDataSource1.DataSource = o.ToList
            rpt.pStartDate.Value = startDate
            rpt.pEndDate.Value = endDate

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_SlipGaji(cabang_id As String, periode As Date, Optional karyawan_id As String = Nothing) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
        Dim r = Serv.GetGajisAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tr_gaji) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            Dim o = os.Where(Function(f) f.Cabang_id = cabang_id And f.Tanggal.Year = periode.Year And f.Tanggal.Month = periode.Month).FirstOrDefault
            If o Is Nothing Then
                Return Nothing
            End If
            If karyawan_id <> Nothing Then
                Dim line = o.tr_gaji_line.Where(Function(t) t.Karyawan_id = karyawan_id).FirstOrDefault
                o.tr_gaji_line.Clear()
                If line IsNot Nothing Then
                    o.tr_gaji_line.Add(line)
                End If
            End If

            Dim rpt As New rpt_slip_gaji
            rpt.ObjectDataSource1.DataSource = o

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_RekapGajiBank(periode As Date, area_id As String, Optional cabang_id As String = Nothing) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
        Dim r = Serv.GetGajisAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tr_gaji) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            os = os.Where(Function(f) f.Area_id = area_id And f.Tanggal.Year = periode.Year And f.Tanggal.Month = periode.Month)
            If cabang_id <> Nothing Then
                os = os.Where(Function(f) f.Cabang_id = cabang_id)
            End If
            Dim _lines As New List(Of tr_gaji_line)
            For Each x In os
                _lines.AddRange(x.tr_gaji_line)
            Next



            Dim rpt As New rpt_rekapGajiBank
            rpt.ObjectDataSource1.DataSource = _lines

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
    Public Function GetRpt_RekapTHRBank(periode As Date, area_id As String, Optional cabang_id As String = Nothing) As DevExpress.XtraReports.UI.XtraReport

        Dim Serv = ObjectFactory.GetInstance(Of ThrService)()
        Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tr_thr) = r.Output
            'Dim _user = AuthHelper.GetLoggedInUserInfo
            os = os.Where(Function(f) f.Area_id = area_id And f.PeriodeDate.Year = periode.Year And f.PeriodeDate.Month = periode.Month)
            If cabang_id <> Nothing Then
                os = os.Where(Function(f) f.Cabang_id = cabang_id)
            End If
            Dim _lines As New List(Of tr_thr_line)
            For Each x In os
                _lines.AddRange(x.tr_thr_line)
            Next



            Dim rpt As New rpt_rekapThrBank
            rpt.ObjectDataSource1.DataSource = _lines

            Return rpt
        Else
            'sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return Nothing
        End If


    End Function
End Class
