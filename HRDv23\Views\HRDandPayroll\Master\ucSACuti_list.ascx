﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucSACuti_list.ascx.vb" Inherits="HRDv23.ucSACuti_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>


<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="Filter Area" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem Caption="Perusahaan" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
	grd_cuti.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Cabang" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_cuti.Refresh();

}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Entri Saldo Awal Cuti" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_cuti" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" Width="100%">
                                <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_karyawan.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_karyawan.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_karyawan.PerformCallback('new'); 
break;
}

}" />
                                <SettingsAdaptivity HideDataCellsAtWindowInnerWidth="600">
                                </SettingsAdaptivity>
                                <SettingsPager AlwaysShowPager="True">
                                    <AllButton Visible="True">
                                    </AllButton>
                                    <PageSizeItemSettings Visible="True">
                                    </PageSizeItemSettings>
                                </SettingsPager>
                                <SettingsEditing Mode="Batch">
                                </SettingsEditing>
                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" HorizontalScrollBarMode="Auto" />
                                <SettingsBehavior AllowFocusedRow="True" />
                                <SettingsPopup>
                                    <FilterControl AutoUpdatePosition="False">
                                    </FilterControl>
                                </SettingsPopup>
                                <SettingsSearchPanel Visible="True" />
                                <SettingsExport EnableClientSideExportAPI="True">
                                </SettingsExport>
                                <Columns>
                                    <dx:GridViewCommandColumn ShowInCustomizationForm="True" VisibleIndex="0">
                                    </dx:GridViewCommandColumn>
                                    <dx:GridViewDataTextColumn Caption="NIK" FieldName="tm_karyawan.NIK" ShowInCustomizationForm="True" VisibleIndex="3" ReadOnly="True">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_department.NamaDepartemen" ShowInCustomizationForm="True" VisibleIndex="5" Caption="Department" ReadOnly="True">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_jabatan.Jabatan" ShowInCustomizationForm="True" VisibleIndex="6" Caption="Jabatan" ReadOnly="True">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataCheckColumn FieldName="Tetap" ShowInCustomizationForm="True" VisibleIndex="7" ReadOnly="True">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglTetap" ShowInCustomizationForm="True" VisibleIndex="8" ReadOnly="True">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglAwalKontrak" ShowInCustomizationForm="True" VisibleIndex="9" ReadOnly="True">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglAkhirKontrak" ShowInCustomizationForm="True" VisibleIndex="10" ReadOnly="True">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_area.NamaArea" ShowInCustomizationForm="True" VisibleIndex="1" Caption="Perusahaan" ReadOnly="True">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" ShowInCustomizationForm="True" VisibleIndex="2" Caption="Cabang" ReadOnly="True">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataSpinEditColumn FieldName="SaldoAwalCuti" ShowInCustomizationForm="True" VisibleIndex="11">
                                        <PropertiesSpinEdit AllowMouseWheel="False" DisplayFormatInEditMode="True" DisplayFormatString="n0" Increment="0" MaxValue="100" NumberFormat="Custom">
                                            <SpinButtons ShowIncrementButtons="False">
                                            </SpinButtons>
                                        </PropertiesSpinEdit>
                                    </dx:GridViewDataSpinEditColumn>
                                    <dx:GridViewDataTextColumn Caption="Nama" FieldName="tm_karyawan.Nama" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="4">
                                    </dx:GridViewDataTextColumn>
                                </Columns>
                                <Toolbars>
                                    <dx:GridViewToolbar>
                                        <Items>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem Command="ShowFilterRow">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                            </dx:ASPxGridView>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

                            <dx:EntityServerModeDataSource runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tm_karyawan_datadinas" EnableDelete="True" EnableInsert="True" EnableUpdate="True" ID="EntityServerModeDataSource1"></dx:EntityServerModeDataSource>

                        
