﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucBayarPinjaman_edit.ascx.vb" Inherits="HRDv23.ucBayarPinjaman_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Area" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_karyawan.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Kode Bayar" ColSpan="1" FieldName="KodeBayar">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E4" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Tanggal">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E5" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Karyawan" ColSpan="1" FieldName="Karyawan_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_karyawan" runat="server" CallbackPageSize="10" ClientInstanceName="cb_karyawan" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_regpinjaman.Refresh();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="nik">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="nama">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Memo">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxMemo ID="ASPxFormLayout1_E19" runat="server">
                                    </dx:ASPxMemo>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E13" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E14" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E15" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E16" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E12" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E11" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="NoVoucher">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E17" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>
        <dx:LayoutGroup Caption="Items Yang Dibayar" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxSplitter ID="ASPxSplitter1" runat="server">
                                <Panes>
                                    <dx:SplitterPane ScrollBars="Auto">
                                        <ContentCollection>
                                            <dx:SplitterContentControl runat="server">
                                                <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_regpinjaman" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id">
                                                    <ClientSideEvents EndCallback="function(s, e) {
	if(s.cpUpdate){
	grd_bayarpinjaman_line.Refresh();
}
}" />
                                                    <Templates>
                                                        <FooterRow>
                                                            <table style="width:100%;">
                                                                <tr>
                                                                    <td>&nbsp;</td>
                                                                    <td style="font-weight: bold; text-align: right; width: 150px">TOTAL BAYAR :</td>
                                                                    <td style="width: 170px">
                                                                        <dx:ASPxSpinEdit ID="txt_kurangbayar" runat="server" AllowMouseWheel="False" DisplayFormatString="n2" HorizontalAlign="Right" Increment="0" Number="0" OnLoad="txt_kurangbayar_Load" ReadOnly="True" Width="100%">
                                                                            <SpinButtons ShowIncrementButtons="False">
                                                                            </SpinButtons>
                                                                            <ReadOnlyStyle BackColor="#CCCCCC">
                                                                            </ReadOnlyStyle>
                                                                        </dx:ASPxSpinEdit>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>&nbsp;</td>
                                                                    <td style="font-weight: bold; text-align: right; width: 150px">&nbsp;</td>
                                                                    <td style="width: 170px">&nbsp;</td>
                                                                </tr>
                                                            </table>
                                                        </FooterRow>
                                                    </Templates>
                                                    <SettingsEditing EditFormColumnCount="1" Mode="PopupEditForm">
                                                    </SettingsEditing>
                                                    <Settings ShowFooter="True" />
                                                    <SettingsCommandButton>
                                                        <EditButton Text="Bayar">
                                                        </EditButton>
                                                    </SettingsCommandButton>
                                                    <SettingsDataSecurity PreventLoadClientValuesForReadOnlyColumns="False" />
                                                    <SettingsPopup>
                                                        <EditForm AllowResize="True" HorizontalAlign="WindowCenter" Modal="True" VerticalAlign="WindowCenter">
                                                            <SettingsAdaptivity Mode="OnWindowInnerWidth" VerticalAlign="WindowCenter" />
                                                        </EditForm>
                                                        <FilterControl AutoUpdatePosition="False">
                                                        </FilterControl>
                                                    </SettingsPopup>
                                                    <Columns>
                                                        <dx:GridViewCommandColumn ShowEditButton="True" ShowInCustomizationForm="True" VisibleIndex="0">
                                                        </dx:GridViewCommandColumn>
                                                        <dx:GridViewDataTextColumn FieldName="KodePinjaman" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="2">
                                                            <PropertiesTextEdit>
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataDateColumn FieldName="Tanggal" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="3">
                                                            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesDateEdit>
                                                        </dx:GridViewDataDateColumn>
                                                        <dx:GridViewDataTextColumn Caption="Jml Pinjaman" FieldName="Amount" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="6">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn FieldName="TotalBayar" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="10">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Sisa Pinjaman" FieldName="Saldo" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="11">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn FieldName="Status" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="19">
                                                            <PropertiesTextEdit>
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataSpinEditColumn Caption="Bayar" FieldName="xBayar" ShowInCustomizationForm="True" UnboundType="Decimal" VisibleIndex="20">
                                                            <PropertiesSpinEdit AllowMouseWheel="False" DisplayFormatInEditMode="True" DisplayFormatString="n2" Increment="0" NumberFormat="Custom">
                                                                <SpinButtons ShowIncrementButtons="False">
                                                                </SpinButtons>
                                                            </PropertiesSpinEdit>
                                                        </dx:GridViewDataSpinEditColumn>
                                                    </Columns>
                                                </dx:ASPxGridView>
                                            </dx:SplitterContentControl>
                                        </ContentCollection>
                                    </dx:SplitterPane>
                                    <dx:SplitterPane ScrollBars="Auto">
                                        <ContentCollection>
                                            <dx:SplitterContentControl runat="server">
                                                <dx:ASPxGridView ID="ASPxGridView2" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_bayarpinjaman_line" DataSourceID="EntityServerModeDataSource2" KeyFieldName="Id;RowGuid">
                                                    <ClientSideEvents EndCallback="function(s, e) {
	if(s.cpDeleted){
		grd_regpinjaman.Refresh();
		s.cpDeleted=false;
}
}" />
                                                    <Templates>
                                                        <FooterRow>
                                                            <table style="width:100%;">
                                                                <tr>
                                                                    <td>&nbsp;</td>
                                                                    <td style="font-weight: bold; text-align: right; width: 150px">TOTAL BAYAR :</td>
                                                                    <td style="width: 170px">
                                                                        <dx:ASPxSpinEdit ID="txt_totalbayar0" runat="server" AllowMouseWheel="False" DisplayFormatString="n2" HorizontalAlign="Right" Increment="0" Number="0" OnLoad="txt_totalbayar_Load" ReadOnly="True" Width="100%">
                                                                            <SpinButtons ShowIncrementButtons="False">
                                                                            </SpinButtons>
                                                                            <ReadOnlyStyle BackColor="#CCCCCC">
                                                                            </ReadOnlyStyle>
                                                                        </dx:ASPxSpinEdit>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>&nbsp;</td>
                                                                    <td style="font-weight: bold; text-align: right; width: 150px">&nbsp;</td>
                                                                    <td style="width: 170px">&nbsp;</td>
                                                                </tr>
                                                            </table>
                                                        </FooterRow>
                                                    </Templates>
                                                    <SettingsEditing EditFormColumnCount="1" Mode="PopupEditForm">
                                                    </SettingsEditing>
                                                    <Settings ShowFooter="True" />
                                                    <SettingsCommandButton>
                                                        <EditButton Text="Edit">
                                                        </EditButton>
                                                    </SettingsCommandButton>
                                                    <SettingsDataSecurity PreventLoadClientValuesForReadOnlyColumns="False" />
                                                    <SettingsPopup>
                                                        <EditForm AllowResize="True" HorizontalAlign="WindowCenter" Modal="True" VerticalAlign="WindowCenter">
                                                            <SettingsAdaptivity Mode="OnWindowInnerWidth" VerticalAlign="WindowCenter" />
                                                        </EditForm>
                                                        <FilterControl AutoUpdatePosition="False">
                                                        </FilterControl>
                                                    </SettingsPopup>
                                                    <Columns>
                                                        <dx:GridViewCommandColumn ShowEditButton="True" ShowInCustomizationForm="True" VisibleIndex="0" ShowDeleteButton="True">
                                                        </dx:GridViewCommandColumn>
                                                        <dx:GridViewDataTextColumn Caption="No Pinjaman" FieldName="tr_register_pinjaman.KodePinjaman" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="2">
                                                            <PropertiesTextEdit>
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Jml Pinjaman" FieldName="tr_register_pinjaman.Amount" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="6">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Total Bayar" FieldName="tr_register_pinjaman.TotalBayar" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="10">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Sisa Pinjaman" FieldName="tr_register_pinjaman.Saldo" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="11">
                                                            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Status" FieldName="tr_register_pinjaman.Status" ReadOnly="True" ShowInCustomizationForm="True" VisibleIndex="19">
                                                            <PropertiesTextEdit>
                                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                                </ReadOnlyStyle>
                                                            </PropertiesTextEdit>
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataSpinEditColumn Caption="Bayar" FieldName="Amount" ShowInCustomizationForm="True" UnboundType="Decimal" VisibleIndex="20">
                                                            <PropertiesSpinEdit AllowMouseWheel="False" DisplayFormatInEditMode="True" DisplayFormatString="n2" Increment="0" NumberFormat="Custom">
                                                                <SpinButtons ShowIncrementButtons="False">
                                                                </SpinButtons>
                                                            </PropertiesSpinEdit>
                                                        </dx:GridViewDataSpinEditColumn>
                                                    </Columns>
                                                </dx:ASPxGridView>
                                            </dx:SplitterContentControl>
                                        </ContentCollection>
                                    </dx:SplitterPane>
                                </Panes>
                            </dx:ASPxSplitter>
                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tr_register_pinjaman" />
                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource2" runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tr_pinjaman_bayar_line" />
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Action" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Save" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_matauang.PerformCallback('save');
		s.SetEnabled(false);
	};
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Back" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_matauang.PerformCallback('reset');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>