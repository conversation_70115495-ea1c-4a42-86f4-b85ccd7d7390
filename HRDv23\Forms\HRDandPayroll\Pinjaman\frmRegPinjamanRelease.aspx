﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmRegPinjamanRelease.aspx.vb" Inherits="HRDv23.frmRegPinjamanRelease" %>
<%@ Register Src="~/Views/HRDandPayroll/Pinjaman/ucRegPinjaman_edit.ascx" TagPrefix="uc1" TagName="ucRegPinjaman_edit" %>
<%@ Register Src="~/Views/HRDandPayroll/Pinjaman/ucRegPinjaman_release.ascx" TagPrefix="uc1" TagName="ucRegPinjaman_release" %>

<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_reg_pinjaman_release" Width="100%">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Caption="List" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucRegPinjaman_release runat="server" ID="ucRegPinjaman_release" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Edit" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucRegPinjaman_edit runat="server" id="ucRegPinjaman_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
