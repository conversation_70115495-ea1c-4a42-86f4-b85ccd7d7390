<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucCutiBesar_list.ascx.vb" Inherits="HRDv23.ucCutiBesar_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<table style="width: 100%;">
    <tr>
        <td style="width: 150px; font-weight: bold">Perusahaan</td>
        <td style="width: 350px">
            <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" Width="100%">
                <ClientSideEvents SelectedIndexChanged="function(s, e) { grd_cuti_besar.Refresh(); }" Init="onInitCB" />
                <Columns>
                    <dx:ListBoxColumn FieldName="KodeArea" Caption="Kode Perusahaan" />
                    <dx:ListBoxColumn FieldName="NamaArea" Caption="Nama Perusahaan" />
                </Columns>
                <ValidationSettings SetFocusOnError="True">
                    <RequiredField IsRequired="True" ErrorText="Required" />
                </ValidationSettings>
            </dx:ASPxComboBox>
        </td>
        <td>&nbsp;</td>
    </tr>
</table>
<dx:ASPxGridView ID="grd_cuti_besar" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_cuti_besar">
    <ClientSideEvents CustomButtonClick="function(s, e) { var rowKey = s.GetRowKey(s.GetFocusedRowIndex()); if(e.buttonID=='btn_delete'){ var b=confirm('Are you sure to delete this item?'); if(b){ cp_cuti_besar.PerformCallback(e.buttonID+';'+rowKey); } }else{ cp_cuti_besar.PerformCallback(e.buttonID+';'+rowKey); } }" ToolbarItemClick="function(s, e) { if (e.item.name === 'btn_new') { cp_cuti_besar.PerformCallback('new'); } }" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600" />
    <SettingsPager AlwaysShowPager="True">
        <AllButton Visible="True" />
        <PageSizeItemSettings Visible="True" />
    </SettingsPager>
    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
    <SettingsBehavior AllowFocusedRow="True" />
    <Columns>
        <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16" />
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16" />
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                    <Image IconID="iconbuilder_security_visibility_svg_16x16" />
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="1" Caption="Cabang" />
        <dx:GridViewDataTextColumn FieldName="NoCutiBesar" VisibleIndex="2" Caption="No Cuti Besar" />
        <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="3" Caption="Tanggal">
            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy" />
        </dx:GridViewDataDateColumn>
        <dx:GridViewDataTextColumn FieldName="Tahun" VisibleIndex="4" Caption="Tahun" />
        <dx:GridViewDataTextColumn FieldName="Status" VisibleIndex="5" Caption="Status" />
        <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="6" Caption="Keterangan" />
        <dx:GridViewDataTextColumn FieldName="Total" VisibleIndex="7" Caption="Total">
            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2" />
        </dx:GridViewDataTextColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem Name="btn_new" Text="New">
                    <Image IconID="actions_new_svg_16x16" />
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel" />
                <dx:GridViewToolbarItem Command="ShowFilterRow" />
                <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx" />
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
</dx:ASPxGridView>
<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_cuti_besar" />
