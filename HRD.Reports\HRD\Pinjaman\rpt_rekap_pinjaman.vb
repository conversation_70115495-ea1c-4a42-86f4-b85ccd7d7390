﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Public Class rpt_rekap_pinjaman
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Static i As Integer

        i += 1

        cell_no.Text = $"{i}."
    End Sub

    Private Sub fTotalPinjaman_GetValue(sender As Object, e As GetValueEventArgs) Handles fTotalPinjaman.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl As Date = Format(pEndDate.Value, "yyyy-MM-01")
        tgl = tgl.AddMonths(1)

        Dim totPinjaman = o.tr_register_pinjaman.SelectMany(Function(x) x.tr_pinjaman_move) _
                          .Where(Function(m) m.TransDate < tgl AndAlso m.Amount > 0) _
                          .Sum(Function(m) m.Amount)

        e.Value = totPinjaman
    End Sub

    Private Sub fSaldoBulanLalu_GetValue(sender As Object, e As GetValueEventArgs) Handles fSaldoBulanLalu.GetValue
        Dim o = CType(e.Row, tm_karyawan)
        Dim tgl As Date = Format(pEndDate.Value, "yyyy-MM-01")

        Dim saldoBulanLalu = o.tr_register_pinjaman.SelectMany(Function(x) x.tr_pinjaman_move) _
                          .Where(Function(m) m.TransDate < tgl) _
                          .Sum(Function(m) m.Amount)

        e.Value = saldoBulanLalu
    End Sub

    Private Sub fAngsuranBulanIni_GetValue(sender As Object, e As GetValueEventArgs) Handles fAngsuranBulanIni.GetValue
        Dim o = CType(e.Row, tm_karyawan)
        Dim tgl As Date = Format(pEndDate.Value, "yyyy-MM-dd")

        Dim angsuranBulanini = o.tr_register_pinjaman.SelectMany(Function(x) x.tr_pinjaman_move) _
                          .Where(Function(m) m.TransDate.Year = tgl.Year AndAlso m.TransDate.Month = tgl.Month AndAlso m.Amount < 0) _
                          .Sum(Function(m) m.Amount)

        e.Value = angsuranBulanini * (-1)
    End Sub

    Private Sub fSaldoBulanIni_GetValue(sender As Object, e As GetValueEventArgs) Handles fSaldoBulanIni.GetValue
        Dim o = CType(e.Row, tm_karyawan)
        Dim tgl As Date = Format(pEndDate.Value, "yyyy-MM-01")
        tgl = tgl.AddMonths(1)
        Dim saldoBulanIni = o.tr_register_pinjaman.SelectMany(Function(x) x.tr_pinjaman_move) _
                          .Where(Function(m) m.TransDate < tgl) _
                          .Sum(Function(m) m.Amount)

        e.Value = saldoBulanIni
    End Sub
End Class