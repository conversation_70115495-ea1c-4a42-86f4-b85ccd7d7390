'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Collections.Generic

Partial Public Class tm_area
    Public Property Id As Integer
    Public Property KodeArea As String
    Public Property NamaArea As String
    Public Property CreatedBy As String
    Public Property CreatedDate As Nullable(Of Date)
    Public Property ModifiedBy As String
    Public Property ModifiedDate As Nullable(Of Date)
    Public Property KodeNIK_Tetap As String
    Public Property KodeNIK_Kontrak As String
    Public Property NoRekeningBank As String
    Public Property TglCustOffCuti As Nullable(Of Date)
    Public Property FlatRateBPJS As Boolean

    Public Overridable Property tm_absensi_setting As ICollection(Of tm_absensi_setting) = New HashSet(Of tm_absensi_setting)
    Public Overridable Property tm_cabang As ICollection(Of tm_cabang) = New HashSet(Of tm_cabang)
    Public Overridable Property tm_user As ICollection(Of tm_user) = New HashSet(Of tm_user)
    Public Overridable Property tm_event_setting As ICollection(Of tm_event_setting) = New HashSet(Of tm_event_setting)
    Public Overridable Property tm_karyawan_datadinas As ICollection(Of tm_karyawan_datadinas) = New HashSet(Of tm_karyawan_datadinas)
    Public Overridable Property tm_karyawan As ICollection(Of tm_karyawan) = New HashSet(Of tm_karyawan)
    Public Overridable Property tm_libur_nasional As ICollection(Of tm_libur_nasional) = New HashSet(Of tm_libur_nasional)
    Public Overridable Property tm_off_karyawan As ICollection(Of tm_off_karyawan) = New HashSet(Of tm_off_karyawan)
    Public Overridable Property tm_user_area As ICollection(Of tm_user_area) = New HashSet(Of tm_user_area)
    Public Overridable Property tr_absensi As ICollection(Of tr_absensi) = New HashSet(Of tr_absensi)
    Public Overridable Property tr_gaji As ICollection(Of tr_gaji) = New HashSet(Of tr_gaji)
    Public Overridable Property tr_ijin As ICollection(Of tr_ijin) = New HashSet(Of tr_ijin)
    Public Overridable Property tr_kompensasi_kontrak As ICollection(Of tr_kompensasi_kontrak) = New HashSet(Of tr_kompensasi_kontrak)
    Public Overridable Property tr_pinjaman_bayar As ICollection(Of tr_pinjaman_bayar) = New HashSet(Of tr_pinjaman_bayar)
    Public Overridable Property tr_register_pinjaman As ICollection(Of tr_register_pinjaman) = New HashSet(Of tr_register_pinjaman)
    Public Overridable Property tr_spk_lembur As ICollection(Of tr_spk_lembur) = New HashSet(Of tr_spk_lembur)
    Public Overridable Property tr_thr As ICollection(Of tr_thr) = New HashSet(Of tr_thr)
    Public Overridable Property tr_tunjangan_onetime As ICollection(Of tr_tunjangan_onetime) = New HashSet(Of tr_tunjangan_onetime)
    Public Overridable Property tm_bonus_thr As ICollection(Of tm_bonus_thr) = New HashSet(Of tm_bonus_thr)

End Class
