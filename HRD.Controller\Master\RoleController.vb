﻿Imports HRD.Controller
Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Public Class RoleController
    Inherits Controller(Of tm_role)
    Implements IRoleListController, IRoleEditController

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New RoleManager(daoFactory)

    Public ReadOnly Property MenuList As IQueryable(Of tm_menu) Implements IRoleEditController.MenuList
        Get
            Dim os = mgr.GetMenuDao.GetAll
            Return os
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_role
    End Sub

    Public Overrides Sub SelectItem(id As String)
        mgr.DataAccess.ResetContext()
        SelectedItem = mgr.DataAccess.GetById(id)
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim o = mgr.DataAccess.GetById(id)
            mgr.GetMenuRoleDao.DeleteRange(o.tm_menu_role.ToList)
            mgr.DataAccess.Delete(o)
            mgr.DataAccess.Save()
            Reset()
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try

    End Sub

    Public Overrides Sub Saving()
        Try
            Dim o As New tm_role With {.CreatedBy = SelectedItem.CreatedBy, .CreatedDate = SelectedItem.CreatedDate,
                .RoleName = SelectedItem.RoleName, .Id = SelectedItem.Id, .ModifiedBy = SelectedItem.ModifiedBy, .ModifiedDate = SelectedItem.ModifiedDate}

            If o.Id <= 0 Then
                o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.CreatedDate = Now
                mgr.DataAccess.Add(o)
            Else
                o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.ModifiedDate = Now
                mgr.DataAccess.Edit(o)
            End If

            For Each x In SelectedItem.tm_menu_role.ToList
                Dim y As New tm_menu_role With {.Id = x.Id, .Deleting = x.Deleting, .Role_id = x.Role_id, .SiteMenu_id = x.SiteMenu_id}
                If y.Deleting Then
                    If y.Id > 0 Then
                        mgr.GetMenuRoleDao.Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tm_role = o
                        mgr.GetMenuRoleDao.Add(y)
                    Else
                        mgr.GetMenuRoleDao.Edit(y)
                    End If
                End If

            Next

            mgr.DataAccess.Save()
            Saved = True
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(o As tm_role)
        Throw New NotImplementedException()
    End Sub
End Class
Public Interface IRoleListController
    Inherits IControllerMain, IControllerList


End Interface
Public Interface IRoleEditController
    Inherits IControllerMain, IControllerEdit(Of tm_role)

    ReadOnly Property MenuList As IQueryable(Of tm_menu)
End Interface