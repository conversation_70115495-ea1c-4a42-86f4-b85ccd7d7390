<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="cfd7bddc113646479e828b6159331195" Name="Diagram1" ZoomLevel="94" >
        <EntityTypeShape EntityType="HRDModel.tm_menu_role" Width="1.5" PointX="14.5" PointY="69.75" />
        <EntityTypeShape EntityType="HRDModel.tm_role" Width="1.5" PointX="8.25" PointY="65.5" />
        <EntityTypeShape EntityType="HRDModel.tm_user" Width="1.5" PointX="5.25" PointY="55.625" />
        <EntityTypeShape EntityType="HRDModel.tm_user_role" Width="1.5" PointX="10.5" PointY="63.75" />
        <AssociationConnector Association="HRDModel.FK_SiteMenuRole_SiteRole" />
        <AssociationConnector Association="HRDModel.FK_SiteUserRole_SiteRole" />
        <AssociationConnector Association="HRDModel.FK_SiteUserRole_SiteUser" />
        <EntityTypeShape EntityType="HRDModel.tm_menu" Width="1.5" PointX="12.25" PointY="76.625" />
        <AssociationConnector Association="HRDModel.FK_SiteMenuRole_SiteMenu" />
        <EntityTypeShape EntityType="HRDModel.tm_jenis_kelamin" Width="1.5" PointX="12" PointY="31.375" />
        <EntityTypeShape EntityType="HRDModel.tm_jurusan" Width="1.5" PointX="12" PointY="38.75" />
        <EntityTypeShape EntityType="HRDModel.tm_pendidikan" Width="1.5" PointX="9.75" PointY="38.875" />
        <EntityTypeShape EntityType="HRDModel.tm_status_PTKP" Width="1.5" PointX="12" PointY="27.75" />
        <AssociationConnector Association="HRDModel.FK_tm_jurusan_tm_pendidikan" />
        <EntityTypeShape EntityType="HRDModel.tm_jabatan" Width="1.5" PointX="12" PointY="73" />
        <EntityTypeShape EntityType="HRDModel.tm_namabank" Width="1.5" PointX="12" PointY="66.75" />
        <EntityTypeShape EntityType="HRDModel.tr_ijin" Width="1.5" PointX="11.25" PointY="43.25" />
        <EntityTypeShape EntityType="HRDModel.tr_gaji" Width="1.5" PointX="8.25" PointY="44" />
        <EntityTypeShape EntityType="HRDModel.tm_status_perkawinan" Width="1.5" PointX="12" PointY="33.875" />
        <EntityTypeShape EntityType="HRDModel.tm_ijin_master" Width="1.5" PointX="9" PointY="35.5" />
        <AssociationConnector Association="HRDModel.FK_tr_ijin_tm_ijin_master" />
        <EntityTypeShape EntityType="HRDModel.tr_register_pinjaman" Width="1.5" PointX="14.25" PointY="49.75" />
        <EntityTypeShape EntityType="HRDModel.tm_shift_absensi" Width="1.5" PointX="20.25" PointY="34.75" />
        <EntityTypeShape EntityType="HRDModel.tm_off_karyawan" Width="1.5" PointX="5.25" PointY="50.75" />
        <EntityTypeShape EntityType="HRDModel.tm_off_karyawan_line" Width="1.5" PointX="22.5" PointY="37.5" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_off_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_jumat" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_kamis" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_minggu" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_rabu" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_sabtu" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_selasa" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_senin" />
        <EntityTypeShape EntityType="HRDModel.tm_ter_pajak" Width="1.5" PointX="17.25" PointY="56.625" />
        <EntityTypeShape EntityType="HRDModel.tm_event_setting_line" Width="1.5" PointX="16.5" PointY="44" />
        <EntityTypeShape EntityType="HRDModel.tm_event_setting" Width="1.5" PointX="8.25" PointY="50.375" />
        <AssociationConnector Association="HRDModel.FK_tm_event_setting_line_tm_event_setting" />
        <EntityTypeShape EntityType="HRDModel.tm_user_area" Width="1.5" PointX="7.5" PointY="61.875" />
        <AssociationConnector Association="HRDModel.FK_tm_user_area_tm_user" />
        <EntityTypeShape EntityType="HRDModel.tr_pinjaman_bayar" Width="1.5" PointX="19.5" PointY="39.75" />
        <EntityTypeShape EntityType="HRDModel.tr_pinjaman_bayar_line" Width="1.5" PointX="24.75" PointY="46.625" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_bayar_line_tr_register_pinjaman" />
        <EntityTypeShape EntityType="HRDModel.tr_pinjaman_move" Width="1.5" PointX="27" PointY="53.125" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_move_tr_pinjaman_bayar_line" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_move_tr_register_pinjaman" />
        <EntityTypeShape EntityType="HRDModel.tm_libur_nasional" Width="1.5" PointX="3" PointY="56.125" />
        <EntityTypeShape EntityType="HRDModel.tr_absensi" Width="1.5" PointX="22.5" PointY="43.625" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tm_libur_nasional" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tr_ijin" />
        <EntityTypeShape EntityType="HRDModel.tr_gaji_line" Width="1.5" PointX="19.5" PointY="51.75" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_line_tm_status_PTKP" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_line_tm_ter_pajak" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_line_tr_gaji" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_move_tr_gaji_line" />
        <EntityTypeShape EntityType="HRDModel.tm_absensi_setting" Width="1.5" PointX="5.625" PointY="44.25" />
        <EntityTypeShape EntityType="HRDModel.tm_agama" Width="1.5" PointX="12.375" PointY="24.875" />
        <EntityTypeShape EntityType="HRDModel.tm_cabang" Width="1.5" PointX="3.375" PointY="48" />
        <EntityTypeShape EntityType="HRDModel.tm_department" Width="1.5" PointX="12.375" PointY="69.5" />
        <EntityTypeShape EntityType="HRDModel.tr_spk_lembur" Width="1.5" PointX="11.625" PointY="57.25" />
        <EntityTypeShape EntityType="HRDModel.tr_spk_lembur_line" Width="1.5" PointX="16.875" PointY="37.625" />
        <EntityTypeShape EntityType="HRDModel.tr_thr" Width="1.5" PointX="11.625" PointY="49.5" />
        <EntityTypeShape EntityType="HRDModel.tr_tunjangan_onetime" Width="1.5" PointX="19.875" PointY="45.75" />
        <EntityTypeShape EntityType="HRDModel.vw_KaryawanAkanHabisKontrak" Width="1.5" PointX="0.75" PointY="0.75" />
        <AssociationConnector Association="HRDModel.FK_tm_absensi_setting_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_SiteUser_Cabang" />
        <AssociationConnector Association="HRDModel.FK_tm_event_setting_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_ijin_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_bayar_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_register_pinjaman_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_spk_lembur_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_tunjangan_onetime_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tr_spk_lembur_line" />
        <AssociationConnector Association="HRDModel.FK_tr_spk_lembur_line_tr_spk_lembur" />
        <EntityTypeShape EntityType="HRDModel.tr_kompensasi_kontrak" Width="1.5" PointX="7.625" PointY="56.125" />
        <EntityTypeShape EntityType="HRDModel.tr_kompensasi_kontrak_line" Width="1.5" PointX="16.5" PointY="33.375" />
        <AssociationConnector Association="HRDModel.FK_tr_kompensasi_kontrak_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak" />
        <EntityTypeShape EntityType="HRDModel.tm_karyawan" Width="1.5" PointX="25.625" PointY="32" />
        <EntityTypeShape EntityType="HRDModel.tm_karyawan_datadinas" Width="1.5" PointX="29.875" PointY="50" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_agama" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_datadinas_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_datadinas_tm_department" />
        <AssociationConnector Association="HRDModel.FK_tm_event_setting_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_datadinas_tm_jabatan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_jenis_kelamin" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_jurusan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_datadinas_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_namabank" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_pendidikan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_status_perkawinan" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_status_PTKP" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_ijin_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_kompensasi_kontrak_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_bayar_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_register_pinjaman_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_spk_lembur_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_tunjangan_onetime_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_line_tm_karyawan_datadinas" />
        <EntityTypeShape EntityType="HRDModel.tm_area" Width="1.5" PointX="0.75" PointY="44.875" />
        <AssociationConnector Association="HRDModel.FK_tm_absensi_setting_tm_area" />
        <AssociationConnector Association="HRDModel.FK_Cabang_Area" />
        <AssociationConnector Association="HRDModel.FK_SiteUser_Area" />
        <AssociationConnector Association="HRDModel.FK_tm_event_setting_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_datadinas_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_karyawan_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_libur_nasional_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_off_karyawan_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_user_area_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_absensi_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_gaji_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_ijin_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_kompensasi_kontrak_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_pinjaman_bayar_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_register_pinjaman_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_spk_lembur_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tr_tunjangan_onetime_tm_area" />
        <EntityTypeShape EntityType="HRDModel.tm_bonus_thr" Width="1.5" PointX="27.875" PointY="41.25" />
        <AssociationConnector Association="HRDModel.FK_tm_bonus_thr_tm_area" />
        <AssociationConnector Association="HRDModel.FK_tm_bonus_thr_tm_cabang" />
        <AssociationConnector Association="HRDModel.FK_tm_bonus_thr_tm_karyawan" />
        <EntityTypeShape EntityType="HRDModel.tr_thr_line" Width="1.5" PointX="32.125" PointY="43.875" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_line_tm_bonus_thr" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_line_tm_karyawan" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_line_tm_karyawan_datadinas" />
        <AssociationConnector Association="HRDModel.FK_tr_thr_line_tr_thr" />
        </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>