﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucBayarPinjaman_release.ascx.vb" Inherits="HRDv23.ucBayarPinjaman_release" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<table style="width: 100%;">
    <tr>
        <td style="font-weight: bold; width: 150px">Perusahaan</td>
        <td style="width: 350px">
            <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" Width="350px">
                <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_bayarpinjaman.Refresh();
}" Init="onInitCB">
                </ClientSideEvents>
                <Columns>
                    <dx:ListBoxColumn FieldName="KodeArea" Caption="Kode Perusahaan">
                    </dx:ListBoxColumn>
                    <dx:ListBoxColumn FieldName="NamaArea" Caption="Nama Perusahaan">
                    </dx:ListBoxColumn>
                </Columns>
                <ValidationSettings SetFocusOnError="True">
                    <RequiredField IsRequired="True" ErrorText="Required">
                    </RequiredField>
                </ValidationSettings>
            </dx:ASPxComboBox>
        </td>
        <td>&nbsp;</td>
    </tr>
    <tr>
        <td style="font-weight: bold; width: 150px">&nbsp;</td>
        <td style="width: 350px">&nbsp;</td>
        <td>&nbsp;</td>
    </tr>
</table>
<dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_bayarpinjaman">
    <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_bayar_pinjaman_release.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_bayar_pinjaman_release.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_bayar_pinjaman.PerformCallback('new'); 
break;
}

}" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <SettingsPager AlwaysShowPager="True">
        <AllButton Visible="True">
        </AllButton>
        <PageSizeItemSettings Visible="True">
        </PageSizeItemSettings>
    </SettingsPager>
    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
    <SettingsBehavior AllowFocusedRow="True" />
<SettingsPopup>
<FilterControl AutoUpdatePosition="False"></FilterControl>
</SettingsPopup>
    <SettingsSearchPanel Visible="True" />
    <SettingsExport EnableClientSideExportAPI="True">
    </SettingsExport>
    <Columns>
        <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_release" Text="Release">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_area.NamaArea" VisibleIndex="1" Caption="Perusahaan">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="2" Caption="Cabang">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="KodeBayar" VisibleIndex="5">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="6">
            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
            </PropertiesDateEdit>
        </dx:GridViewDataDateColumn>
        <dx:GridViewDataTextColumn FieldName="tm_karyawan.NIK" VisibleIndex="3" Caption="NIK">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Amount" VisibleIndex="7">
            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
            </PropertiesTextEdit>
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Memo" VisibleIndex="8">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn Caption="Nama" FieldName="tm_karyawan.Nama" VisibleIndex="4">
        </dx:GridViewDataTextColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ShowFilterRow">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                </dx:GridViewToolbarItem>
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
</dx:ASPxGridView>

<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_pinjaman_bayar">
</dx:EntityServerModeDataSource>
