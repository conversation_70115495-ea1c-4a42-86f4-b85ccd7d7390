﻿Imports HRD.Application
Imports HRD.Domain

Public Class SettingAbsenService
    Implements ISettingAbsenService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Data Dinas Service", method, msg)
    End Sub

    Public Async Function GetSettingAbsensAsync() As Task(Of ResponseModel) Implements ISettingAbsenService.GetSettingAbsensAsync
        Try
            Dim os = _unitOfWork.Repository(Of tm_absensi_setting)().TableNoTracking.OrderBy(Function(t) t.Id) '.ToListAsync()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetSettingAbsensAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetSettingAbsenByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ISettingAbsenService.GetSettingAbsenByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_absensi_setting)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetSettingAbsenByIdAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_absensi_setting) As Task(Of ResponseModel) Implements ISettingAbsenService.UpsertAsync
        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tm_absensi_setting)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tm_absensi_setting)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ISettingAbsenService.DeleteAsync
        Try
            Dim dn = Await _unitOfWork.Repository(Of tm_absensi_setting)().Get(Id)
            If dn IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_absensi_setting).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
