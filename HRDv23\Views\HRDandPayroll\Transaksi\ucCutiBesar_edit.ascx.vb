Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers

Public Class ucCutiBesar_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ICutiBesarEditController

    Private Property oSelectItem As Object
        Get
            Return ViewState("oSelectItem")
        End Get
        Set(value As Object)
            ViewState("oSelectItem") = value
        End Set
    End Property

    Private Property oAction As String
        Get
            Return ViewState("oAction")
        End Get
        Set(value As String)
            ViewState("oAction") = value
        End Set
    End Property

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        BindData()
        SetFormMode()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            oSelectItem = Nothing
            oAction = Nothing
        End If
    End Sub

    Private Sub BindData()
        If Controller Is Nothing Then Return
        Dim item = Controller.SelectedItem
        If item Is Nothing Then Return
        
        oSelectItem = item
        oAction = Controller.Action.ToString().ToLower()
        
        ' Bind directly to controls by ID
        cb_area.Value = item.Area_id
        cb_cabang.Value = item.Cabang_id
        txtNoCutiBesar.Text = item.NoCutiBesar
        deTanggal.Value = item.Tanggal
        txtTahun.Text = item.Tahun.ToString()
        txtStatus.Text = item.Status
        txtKeterangan.Text = item.Keterangan
        txtTotal.Text = item.Total.ToString()
        
        ' Bind audit info
        txtCreatedBy.Text = item.CreatedBy
        deCreatedDate.Value = item.CreatedDate
        txtModifiedBy.Text = item.ModifiedBy
        deModifiedDate.Value = item.ModifiedDate
        txtPostedBy.Text = item.PostedBy
        dePostedDate.Value = item.PostedDate
        
        ' Bind grid if exists
        Try
            grd_cuti_besar_line.DataBind()
        Catch ex As Exception
            ' Grid may not exist in all forms
        End Try
    End Sub

    Private Sub SetFormMode()
        If Controller Is Nothing Then Return
        
        Dim isReadOnly = (Controller.Action = Action.View)
        Dim isPosted = Controller.SelectedItem?.Posted = True
        Dim isDraft = Controller.SelectedItem?.Status = "DRAFT"
        
        cb_area.ReadOnly = isReadOnly
        cb_cabang.ReadOnly = isReadOnly
        txtNoCutiBesar.ReadOnly = True ' Always read-only (auto-generated)
        deTanggal.ReadOnly = isReadOnly
        txtTahun.ReadOnly = isReadOnly
        txtStatus.ReadOnly = True ' Always read-only (system managed)
        txtKeterangan.ReadOnly = isReadOnly
        txtTotal.ReadOnly = isReadOnly
        
        Try
            ' Save button: visible for add/edit mode and not posted
            btn_save.Visible = Not isReadOnly And Not isPosted
            
            ' Posting button: visible only for draft records and not in view mode
            btn_posting.Visible = Not isReadOnly And isDraft And Not isPosted
            
            ' Void button: visible only for posted records and not in view mode
            btn_void.Visible = Not isReadOnly And isPosted
            
            ' Back button: always visible
            btn_back.Visible = True
        Catch ex As Exception
            ' Buttons may not exist in all forms
        End Try
    End Sub

    Public Sub Saving()
        If Controller Is Nothing Then Return
        
        With Controller.SelectedItem
            .Area_id = cb_area.Value
            .Cabang_id = cb_cabang.Value
            .NoCutiBesar = txtNoCutiBesar.Text
            .Tanggal = deTanggal.Value
            If Integer.TryParse(txtTahun.Text, .Tahun) = False Then
                .Tahun = Now.Year
            End If
            .Status = txtStatus.Text
            .Keterangan = txtKeterangan.Text
            If Decimal.TryParse(txtTotal.Text, .Total) = False Then
                .Total = 0
            End If
        End With
        
        Controller.Saving(Controller.SelectedItem)
    End Sub
    
    Public Sub Posting()
        If Controller Is Nothing Then Return
        
        With Controller.SelectedItem
            .Area_id = cb_area.Value
            .Cabang_id = cb_cabang.Value
            .NoCutiBesar = txtNoCutiBesar.Text
            .Tanggal = deTanggal.Value
            If Integer.TryParse(txtTahun.Text, .Tahun) = False Then
                .Tahun = Now.Year
            End If
            .Status = txtStatus.Text
            .Keterangan = txtKeterangan.Text
            If Decimal.TryParse(txtTotal.Text, .Total) = False Then
                .Total = 0
            End If
        End With
        
        Controller.Posting(Controller.SelectedItem)
    End Sub
    
    Public Sub VoidTransaction()
        If Controller Is Nothing Then Return
        
        ' For void, we may need to add a reason - for now using default
        Dim reason As String = "User voided transaction"
        
        ' Void functionality will be implemented in controller if needed
        ' Controller.VoidTransaction(Controller.SelectedItem, reason)
    End Sub

    Protected Sub btn_save_Click(sender As Object, e As EventArgs) Handles btn_save.Click
        Saving()
    End Sub

    Protected Sub btn_back_Click(sender As Object, e As EventArgs) Handles btn_back.Click
        Controller.Reset()
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then Return
        e.DefaultSorting = "Id Desc"
        
        Try
            e.QueryableSource = Controller.DataListCutiBesarLine(Controller.SelectedItem?.Id.ToString()).AsQueryable
        Catch ex As Exception
            ' DataListCutiBesarLine may not be implemented yet
            e.QueryableSource = Nothing
        End Try
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub
End Class