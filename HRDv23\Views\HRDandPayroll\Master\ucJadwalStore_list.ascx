﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucJadwalStore_list.ascx.vb" Inherits="HRDv23.ucJadwalStore_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<table style="width: 100%;">
    <tr>
        <td style="font-weight: bold; width: 150px">Released</td>
        <td>
            <dx:ASPxCheckBox ID="chk_release" runat="server" CheckState="Unchecked">
                <ClientSideEvents ValueChanged="function(s, e) {
	grd_off_karyawan.Refresh();
}" />
            </dx:ASPxCheckBox>
        </td>
        <td>&nbsp;</td>
    </tr>
    <tr>
        <td style="font-weight: bold; width: 150px">&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
    </tr>
    </table>
<dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_off_karyawan">
    <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_jadwal_store.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_jadwal_store.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_jadwal_store.PerformCallback('new'); 
break;
}

}" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells">
    </SettingsAdaptivity>
    <SettingsPager AlwaysShowPager="True">
        <AllButton Visible="True">
        </AllButton>
        <PageSizeItemSettings Visible="True">
        </PageSizeItemSettings>
    </SettingsPager>
    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
    <SettingsBehavior AllowFocusedRow="True" />
<SettingsPopup>
<FilterControl AutoUpdatePosition="False"></FilterControl>
</SettingsPopup>
    <SettingsSearchPanel Visible="True" />
    <SettingsExport EnableClientSideExportAPI="True">
    </SettingsExport>
    <Columns>
        <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                    <Image IconID="iconbuilder_security_visibility_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="2" Caption="Perusahaan">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="3" Caption="Cabang">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataDateColumn FieldName="TglBerlaku" VisibleIndex="4">
            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
            </PropertiesDateEdit>
        </dx:GridViewDataDateColumn>
        <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="5">
        </dx:GridViewDataTextColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem Name="btn_new" Text="New">
                    <Image IconID="actions_new_svg_16x16">
                    </Image>
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ShowFilterRow">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                </dx:GridViewToolbarItem>
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
</dx:ASPxGridView>

<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_off_karyawan">
</dx:EntityServerModeDataSource>
