﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web
Imports DevExpress.Data.Linq

Public Class ucGaji_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IGajiEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If
            If Controller.Action <> Action.AddNew Then
                txt_periode.Value = $"{oSelectItem.Tahun}-{oSelectItem.Bulan}"

                cb_area.ReadOnly = True
                cb_cabang.ReadOnly = True
            End If
            If Controller.Action = Action.Posting Then
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_gaji_release.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_gaji_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_gaji.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_gaji.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction

        If Controller.Action = Action.Posting Then
            Controller.Posting(oSelectItem)
            Return
        End If

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tr_gaji
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tr_gaji)
        End Get
        Set(value As tr_gaji)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        If oSelectItem Is Nothing Then
            Return
        End If
        e.QueryableSource = Controller.DataListGajiLine(oSelectItem.Id).AsQueryable
    End Sub
End Class