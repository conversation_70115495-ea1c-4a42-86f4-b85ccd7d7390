﻿Partial Public Class tr_absensi
    Property potong1Jam As Decimal = 0
    ReadOnly Property JmlJamLemburHBiasa_1 As Decimal
        Get
            Dim jmlJam = Me.Overtime - Me.potong1Jam
            If jmlJam > (1 * 60) Then
                Return (1 * 60)
            Else
                Return jmlJam
            End If
        End Get
    End Property
    ReadOnly Property JmlJamLemburHBiasa_2 As Decimal
        Get
            Dim jmlJam = Me.Overtime - Me.potong1Jam
            If jmlJam <= (1 * 60) Then
                Return 0
            Else
                Return jmlJam - (1 * 60)
            End If
        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur5_1SD8 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur
            If jmlJam >= (8 * 60) Then
                jmlJam -= (1 * 60)
            End If
            If jmlJam > (8 * 60) Then
                Return (8 * 60)
            Else
                Return jmlJam
            End If
        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur5_9 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur
            jmlJam -= (9 * 60)
            If jmlJam < 0 Then
                jmlJam = 0
            End If
            If jmlJam > (1 * 60) Then
                Return 60
            Else
                Return jmlJam
            End If


        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur5_10SD11 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur
            jmlJam -= (10 * 60)
            If jmlJam < 0 Then
                jmlJam = 0
            End If

            Return jmlJam

        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur6_1SD7 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur - potong1Jam
            If jmlJam >= (8 * 60) Then
                'jmlJam -= (1 * 60)
            End If
            If jmlJam > (7 * 60) Then
                Return (7 * 60)
            Else
                Return jmlJam
            End If
        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur6_8 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur - potong1Jam
            jmlJam -= (7 * 60)
            If jmlJam < 0 Then
                jmlJam = 0
            End If
            If jmlJam > (1 * 60) Then
                Return 60
            Else
                Return jmlJam
            End If


        End Get
    End Property
    ReadOnly Property JmlJamLemburHLibur6_10SD11 As Decimal
        Get
            Dim jmlJam As Decimal = Me.OverTime_HLibur - potong1Jam
            jmlJam -= (8 * 60)
            If jmlJam < 0 Then
                jmlJam = 0
            End If

            Return jmlJam

        End Get
    End Property
End Class
