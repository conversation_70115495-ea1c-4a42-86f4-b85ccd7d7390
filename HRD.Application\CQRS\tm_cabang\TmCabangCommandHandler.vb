﻿
Imports HRD.Domain
Imports HRD.Application
Imports System.Threading

Public Class TmCabangCommandHandler


    Private ReadOnly _uow As IUnitOfWork

    Public Sub New(uow As IUnitOfWork)
        _uow = uow
    End Sub

    Public Async Function Handle(request As CreateTmCabangCommand, cancellationToken As CancellationToken) As Task
        Dim cabang As New tm_cabang() With {
            .Area_id = request.AreaId,
            .KodeCabang = request.KodeCabang,
            .NamaCabang = request.NamaCabang,
            .IsHeadOffice = request.IsHeadOffice,
            .Alamat = request.Alamat,
            .Telp = request.Telp,
            .Fax = request.Fax,
            .CreatedBy = request.CreatedBy,
            .CreatedDate = DateTime.Now,
            .UpahMinimum = request.UpahMinimum,
            .UpahMinimum_Kes = request.UpahMinimumKes,
            .SabtuLibur = request.SabtuLibur,
            .MingguLibur = request.MingguLibur
        }

        Await _uow.Repository(Of tm_cabang).AddAsync(cabang)
        Await _uow.SaveAsync()
        '_context.tm_cabang.Add(cabang)
        'Await _context.SaveChangesAsync(cancellationToken)
    End Function
    Public Async Function Handle(request As DeleteTmCabangCommand, cancellationToken As CancellationToken) As Task

        'Dim cabang = Await _context.tm_cabang.FindAsync(New Object() {command.Id}, cancellationToken)
        Dim cabang = Await _uow.Repository(Of tm_cabang).Get(request.Id)

        If cabang Is Nothing Then
            Throw New KeyNotFoundException($"Cabang with ID {request.Id} not found.")
        End If

        Await _uow.Repository(Of tm_cabang).DeleteAsync(request.Id)
        Await _uow.SaveAsync
        '_context.tm_cabang.Remove(cabang)
        'Await _context.SaveChangesAsync(cancellationToken)
    End Function
    Public Async Function Handle(request As UpdateTmCabangCommand, cancellationToken As CancellationToken) As Task

        Dim cabang = _uow.Repository(Of tm_cabang).Table.Where(Function(f) f.Id = request.Id).FirstOrDefault

        If cabang Is Nothing Then
            Throw New KeyNotFoundException($"Cabang with ID {request.Id} not found.")
        End If

        cabang.Area_id = request.AreaId
        cabang.KodeCabang = request.KodeCabang
        cabang.NamaCabang = request.NamaCabang
        cabang.IsHeadOffice = request.IsHeadOffice
        cabang.Alamat = request.Alamat
        cabang.Telp = request.Telp
        cabang.Fax = request.Fax
        cabang.ModifiedBy = request.ModifiedBy
        cabang.ModifiedDate = DateTime.Now
        cabang.UpahMinimum = request.UpahMinimum
        cabang.UpahMinimum_Kes = request.UpahMinimumKes
        cabang.SabtuLibur = request.SabtuLibur
        cabang.MingguLibur = request.MingguLibur

        Await _uow.SaveAsync()

        'Await _context.SaveChangesAsync(cancellationToken)
    End Function
End Class
