Imports HRD.Application
Imports HRD.Domain




Public Class BayarPinjamanService
    Implements IBayarPinjamanService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction
    Private ReadOnly _errorMessageLog As IErrorMessageLog


    Public Sub New(unitOfWork As IUnitOfWork, myFunction As IMyFunction, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        _myFunction = myFunction
        _errorMessageLog = errorMessageLog
    End Sub


    Private Sub Log(ByVal method As String, ByVal msg As String)
        _errorMessageLog.LogError("Application", "Bayar Pinjaman Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements IBayarPinjamanService.GetQueryableAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_pinjaman_bayar)().TableNoTracking
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, result)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IBayarPinjamanService.GetByIdAsync
        Try
            Dim result = Await _unitOfWork.Repository(Of tr_pinjaman_bayar)().Get(Id)
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, result)
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(TEntity As tr_pinjaman_bayar) As Task(Of ResponseModel) Implements IBayarPinjamanService.UpsertAsync
        _unitOfWork.BeginTransaction()
        Try
                ' Register mapper configuration once
                RegisterMapperConfiguration()

                ' Map and save main entity
                Dim o = ExpressMapper.Mapper.Map(Of tr_pinjaman_bayar, tr_pinjaman_bayar)(TEntity)

            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_pinjaman_bayar)().Update(o)
            Else
                o.KodeBayar = GetKodeBayar(o)
                Await _unitOfWork.Repository(Of tr_pinjaman_bayar)().AddAsync(o)
                End If

                ' Handle payment lines
                Await HandlePaymentLines(TEntity.tr_pinjaman_bayar_line, o)

            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Log(NameOf(Me.UpsertAsync), ex.Message)
                Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
            End Try

    End Function

    Private Sub RegisterMapperConfiguration()
        ExpressMapper.Mapper.Register(Of tr_pinjaman_bayar, tr_pinjaman_bayar)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_karyawan) _
            .Ignore(Function(x) x.tr_pinjaman_bayar_line)

        ExpressMapper.Mapper.Register(Of tr_pinjaman_bayar_line, tr_pinjaman_bayar_line)() _
            .Ignore(Function(x) x.tr_pinjaman_bayar) _
            .Ignore(Function(x) x.tr_register_pinjaman)

    End Sub

    Private Async Function HandlePaymentLines(lines As IEnumerable(Of tr_pinjaman_bayar_line), parent As tr_pinjaman_bayar) As Task
        Dim lineRepo = _unitOfWork.Repository(Of tr_pinjaman_bayar_line)()

        For Each line In lines
            If line.Deleting Then
                If line.Id > 0 Then
                    Await lineRepo.DeleteAsync(line.Id)
                End If
            Else
                ' Map line to avoid tracking issues
                Dim mappedLine = ExpressMapper.Mapper.Map(Of tr_pinjaman_bayar_line, tr_pinjaman_bayar_line)(line)

                If line.Id <= 0 Then
                    mappedLine.tr_pinjaman_bayar = parent

                    Await lineRepo.AddAsync(mappedLine)
                Else

                    lineRepo.Update(mappedLine)
                End If
            End If
        Next
    End Function

    Private Function GetKodeBayar(o As tr_pinjaman_bayar) As String
        Dim area = _myFunction.tm_area(o.Area_id)
        Dim sFormat As String = $"BPJ-{area.KodeArea}-{Format(o.Tanggal, "yy")}-"
        Dim pinj = _unitOfWork.Repository(Of tr_pinjaman_bayar).TableNoTracking.Where(Function(f) f.Area_id = o.Area_id And f.KodeBayar.StartsWith(sFormat)).OrderByDescending(Function(od) od.KodeBayar).Take(1).SingleOrDefault
        Dim i As Integer = 0
        If pinj IsNot Nothing Then
            i = pinj.KodeBayar.Replace(sFormat, "")
        End If
        i += 1

        Return sFormat & Format(i, "00000")
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IBayarPinjamanService.DeleteAsync
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_pinjaman_bayar)().Get(Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            Dim lineRepo = _unitOfWork.Repository(Of tr_pinjaman_bayar_line)()
            Dim lines = lineRepo.RemoveRange(entity.tr_pinjaman_bayar_line)

            Await _unitOfWork.Repository(Of tr_pinjaman_bayar)().Delete(entity)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, entity)
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function Posting(Tentity As tr_pinjaman_bayar) As Task(Of ResponseModel) Implements IBayarPinjamanService.Posting
        Try

            RegisterMapperConfiguration()

            Dim o = ExpressMapper.Mapper.Map(Of tr_pinjaman_bayar, tr_pinjaman_bayar)(Tentity)

            Dim moveRepo = _unitOfWork.Repository(Of tr_pinjaman_move)()
            For Each line In Tentity.tr_pinjaman_bayar_line
                Dim move As New tr_pinjaman_move With {
                    .Pinjaman_id = line.Pinjaman_id,
                    .Amount = -line.Amount,
                    .TransDate = o.Tanggal,
                    .TipeTrans = "BPJ",
                    .PinjBayarLine_id = line.Id,
                    .Memo = o.Memo,
                    .CreatedBy = o.PostedBy,
                    .CreatedDate = o.PostedDate
                }
                Await moveRepo.AddAsync(move)

                Dim pinjaman = Await _unitOfWork.Repository(Of tr_register_pinjaman)().Get(line.Pinjaman_id)
                If pinjaman IsNot Nothing Then
                    pinjaman.TotalBayar += line.Amount
                    pinjaman.Saldo = pinjaman.Amount - pinjaman.TotalBayar

                    ' Update status based on remaining balance
                    If pinjaman.Saldo = 0 Then
                        pinjaman.Status = "Completed"
                    Else
                        pinjaman.Status = "Partial"
                    End If

                    _unitOfWork.Repository(Of tr_register_pinjaman)().Update(pinjaman)
                End If
            Next


            _unitOfWork.Repository(Of tr_pinjaman_bayar)().Update(o)
            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.Posting), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
