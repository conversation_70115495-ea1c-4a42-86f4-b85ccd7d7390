﻿Imports HRD.Application
Imports HRD.Domain
Public Class OffKaryawanService
    Implements IOffKaryawanService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Data Dinas Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements IOffKaryawanService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tm_off_karyawan)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IOffKaryawanService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_off_karyawan)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_off_karyawan) As Task(Of ResponseModel) Implements IOffKaryawanService.UpsertAsync
        Try
            _unitOfWork.Reset()

            Dim y As New tm_off_karyawan With {.Area_id = o.Area_id, .Cabang_id = o.Cabang_id, .Createdby = o.Createdby, .CreatedDate = o.CreatedDate _
                , .Id = o.Id, .Keterangan = o.Keterangan, .ModifiedBy = o.ModifiedBy, .ModifiedDate = o.ModifiedDate, .TglBerlaku = o.TglBerlaku, .Version = o.Version _
                , .Posted = o.Posted, .PostedBy = o.PostedBy, .PostedDate = o.PostedDate}

            If y.Id > 0 Then
                _unitOfWork.Repository(Of tm_off_karyawan)().Update(y)
            Else
                Await _unitOfWork.Repository(Of tm_off_karyawan)().AddAsync(y)

            End If

            If y.Posted <> True Then
                UpSert_off_karyawan_line(y, o.tm_off_karyawan_line.ToList)
            End If


            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IOffKaryawanService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_off_karyawan)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_off_karyawan_line).Delete(Function(f) f.Off_id = o.Id)
                Await _unitOfWork.Repository(Of tm_off_karyawan).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Function UpSert_off_karyawan_line(ByRef tEntity As tm_off_karyawan, lines As IList(Of tm_off_karyawan_line)) As Boolean
        Try
            For Each x In lines
                Dim y As New tm_off_karyawan_line With {.Deleting = x.Deleting, .Id = x.Id, .Karyawan_id = x.Karyawan_id, .RowGuid = x.RowGuid, .Jumat = x.Jumat, .Kamis = x.Kamis, .Minggu = x.Minggu, .Off_id = x.Off_id, .Rabu = x.Rabu, .Sabtu = x.Sabtu, .Selasa = x.Selasa, .Senin = x.Senin}
                If y.Deleting Then
                    If y.Id > 0 Then
                        _unitOfWork.Repository(Of tm_off_karyawan_line).Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tm_off_karyawan = tEntity
                        _unitOfWork.Repository(Of tm_off_karyawan_line).AddAsync(y).GetAwaiter.GetResult()
                    Else
                        _unitOfWork.Repository(Of tm_off_karyawan_line).Update(y)
                    End If
                End If
            Next

            Return True
        Catch ex As Exception
            Return False
        End Try

    End Function
End Class
