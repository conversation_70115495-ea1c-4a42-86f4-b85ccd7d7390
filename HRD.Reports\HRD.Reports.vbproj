﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{27AE5973-D179-4406-87CB-544350A354D0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>HRD.Reports</RootNamespace>
    <AssemblyName>HRD.Reports</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>HRD.Reports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>HRD.Reports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Charts.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.Desktop.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataAccess.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v23.1.Export, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.SpellChecker.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v23.1.Core, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraSpellChecker.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="StructureMap">
      <HintPath>..\..\..\..\..\EBook\Professional-ASP.NET-Design-Patterns-master\Professional-ASP.NET-Design-Patterns-master\ASPPatterns.Chap8.MVP\Lib\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clsRPT.vb" />
    <Compile Include="HRD\Absensi\rpt_absensi_perkaryawan.Designer.vb">
      <DependentUpon>rpt_absensi_perkaryawan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Absensi\rpt_absensi_perkaryawan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Absensi\rpt_cuti_perkaryawan.Designer.vb">
      <DependentUpon>rpt_cuti_perkaryawan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Absensi\rpt_cuti_perkaryawan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Absensi\rpt_rekap_absen.Designer.vb">
      <DependentUpon>rpt_rekap_absen.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Absensi\rpt_rekap_absen.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Absensi\subRpt_cutiPerKaryawan.Designer.vb">
      <DependentUpon>subRpt_cutiPerKaryawan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Absensi\subRpt_cutiPerKaryawan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_pajakbulanan.Designer.vb">
      <DependentUpon>rpt_pajakbulanan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_pajakbulanan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_PajakPerKaryawan.Designer.vb">
      <DependentUpon>rpt_PajakPerKaryawan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_PajakPerKaryawan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_pajaktahunan.Designer.vb">
      <DependentUpon>rpt_pajaktahunan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_pajaktahunan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_rekapGajiBank.Designer.vb">
      <DependentUpon>rpt_rekapGajiBank.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_rekapGajiBank.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_rekapThrBank.Designer.vb">
      <DependentUpon>rpt_rekapThrBank.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_rekapThrBank.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_slip_gaji.Designer.vb">
      <DependentUpon>rpt_slip_gaji.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_slip_gaji.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_slip_thr.Designer.vb">
      <DependentUpon>rpt_slip_thr.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\rpt_slip_thr.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Gaji\subRpt_pajakPerKaryawan.Designer.vb">
      <DependentUpon>subRpt_pajakPerKaryawan.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Gaji\subRpt_pajakPerKaryawan.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HRD\Pinjaman\rpt_rekap_pinjaman.Designer.vb">
      <DependentUpon>rpt_rekap_pinjaman.vb</DependentUpon>
    </Compile>
    <Compile Include="HRD\Pinjaman\rpt_rekap_pinjaman.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HRD\Absensi\rpt_absensi_perkaryawan.resx">
      <DependentUpon>rpt_absensi_perkaryawan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Absensi\rpt_cuti_perkaryawan.resx">
      <DependentUpon>rpt_cuti_perkaryawan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Absensi\rpt_rekap_absen.resx">
      <DependentUpon>rpt_rekap_absen.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Absensi\subRpt_cutiPerKaryawan.resx">
      <DependentUpon>subRpt_cutiPerKaryawan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_pajakbulanan.resx">
      <DependentUpon>rpt_pajakbulanan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_PajakPerKaryawan.resx">
      <DependentUpon>rpt_PajakPerKaryawan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_pajaktahunan.resx">
      <DependentUpon>rpt_pajaktahunan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_rekapGajiBank.resx">
      <DependentUpon>rpt_rekapGajiBank.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_rekapThrBank.resx">
      <DependentUpon>rpt_rekapThrBank.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_slip_gaji.resx">
      <DependentUpon>rpt_slip_gaji.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\rpt_slip_thr.resx">
      <DependentUpon>rpt_slip_thr.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Gaji\subRpt_pajakPerKaryawan.resx">
      <DependentUpon>subRpt_pajakPerKaryawan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HRD\Pinjaman\rpt_rekap_pinjaman.resx">
      <DependentUpon>rpt_rekap_pinjaman.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="..\HRD.Application\HRD.Application.vbproj">
      <Project>{6db5b377-9f8e-4758-8a53-4bf13aaf4dcf}</Project>
      <Name>HRD.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Domain\HRD.Domain.vbproj">
      <Project>{93a2fb6b-4503-4da1-80f4-ba3ca6dbb7d8}</Project>
      <Name>HRD.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Helpers\HRD.Helpers.vbproj">
      <Project>{a149668f-9d7f-44e3-981b-56661a2d8d77}</Project>
      <Name>HRD.Helpers</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>