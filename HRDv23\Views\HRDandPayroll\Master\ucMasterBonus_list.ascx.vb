﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Public Class ucMasterBonus_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IBonusTHRListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.SelectedIndex = 0
            cb_cabang.SelectedIndex = 0
        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.QueryableSource = Controller.DataList(cb_cabang.Value, chk_release.Checked).AsQueryable
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim b As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Posted")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(b, 1, 0)
            Case "btn_view"
                e.Visible = If(b, 0, 1)


        End Select
    End Sub

    Protected Sub ASPxUploadControl1_FileUploadComplete(sender As Object, e As FileUploadCompleteEventArgs) Handles ASPxUploadControl1.FileUploadComplete
        Dim fileName As String = e.UploadedFile.FileName
        Dim fileExtension As String = System.IO.Path.GetExtension(fileName)
        Dim savePath As String = Server.MapPath("~/Content/Uploads/")
        Dim fileSavePath As String = savePath & fileName
        If Not System.IO.Directory.Exists(savePath) Then
            System.IO.Directory.CreateDirectory(savePath)
        End If
        e.UploadedFile.SaveAs(fileSavePath)
        Dim dt As DataTable = MyMethod.ReadExcelFile(fileSavePath)
        Session("_data") = dt
    End Sub

    Private Sub ASPxGridView1_CustomCallback(sender As Object, e As ASPxGridViewCustomCallbackEventArgs) Handles ASPxGridView1.CustomCallback
        If String.IsNullOrEmpty(e.Parameters) Then
            Return
        End If
        Select Case e.Parameters.ToString
            Case "import"
                Controller.SaveImport(Session("_data"))
                If Controller.Saved Then
                    Me.ASPxGridView1.DataBind()
                End If
        End Select
    End Sub
End Class