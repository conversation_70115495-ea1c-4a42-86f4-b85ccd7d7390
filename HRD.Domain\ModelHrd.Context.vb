﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Data.Entity
Imports System.Data.Entity.Infrastructure

Partial Public Class HRDEntities
    Inherits DbContext

    Public Sub New()
        MyBase.New("name=HRDEntities")
    End Sub

    Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
        Throw New UnintentionalCodeFirstException()
    End Sub

    Public Overridable Property tm_menu_role() As DbSet(Of tm_menu_role)
    Public Overridable Property tm_role() As DbSet(Of tm_role)
    Public Overridable Property tm_user() As DbSet(Of tm_user)
    Public Overridable Property tm_user_role() As DbSet(Of tm_user_role)
    Public Overridable Property tm_menu() As DbSet(Of tm_menu)
    Public Overridable Property tm_jenis_kelamin() As DbSet(Of tm_jenis_kelamin)
    Public Overridable Property tm_jurusan() As DbSet(Of tm_jurusan)
    Public Overridable Property tm_pendidikan() As DbSet(Of tm_pendidikan)
    Public Overridable Property tm_status_PTKP() As DbSet(Of tm_status_PTKP)
    Public Overridable Property tm_jabatan() As DbSet(Of tm_jabatan)
    Public Overridable Property tm_namabank() As DbSet(Of tm_namabank)
    Public Overridable Property tr_ijin() As DbSet(Of tr_ijin)
    Public Overridable Property tr_gaji() As DbSet(Of tr_gaji)
    Public Overridable Property tm_status_perkawinan() As DbSet(Of tm_status_perkawinan)
    Public Overridable Property tm_ijin_master() As DbSet(Of tm_ijin_master)
    Public Overridable Property tr_register_pinjaman() As DbSet(Of tr_register_pinjaman)
    Public Overridable Property tm_shift_absensi() As DbSet(Of tm_shift_absensi)
    Public Overridable Property tm_off_karyawan() As DbSet(Of tm_off_karyawan)
    Public Overridable Property tm_off_karyawan_line() As DbSet(Of tm_off_karyawan_line)
    Public Overridable Property tm_ter_pajak() As DbSet(Of tm_ter_pajak)
    Public Overridable Property tm_event_setting_line() As DbSet(Of tm_event_setting_line)
    Public Overridable Property tm_event_setting() As DbSet(Of tm_event_setting)
    Public Overridable Property tm_user_area() As DbSet(Of tm_user_area)
    Public Overridable Property tr_pinjaman_bayar() As DbSet(Of tr_pinjaman_bayar)
    Public Overridable Property tr_pinjaman_bayar_line() As DbSet(Of tr_pinjaman_bayar_line)
    Public Overridable Property tr_pinjaman_move() As DbSet(Of tr_pinjaman_move)
    Public Overridable Property tm_libur_nasional() As DbSet(Of tm_libur_nasional)
    Public Overridable Property tr_absensi() As DbSet(Of tr_absensi)
    Public Overridable Property tr_gaji_line() As DbSet(Of tr_gaji_line)
    Public Overridable Property tm_absensi_setting() As DbSet(Of tm_absensi_setting)
    Public Overridable Property tm_agama() As DbSet(Of tm_agama)
    Public Overridable Property tm_cabang() As DbSet(Of tm_cabang)
    Public Overridable Property tm_department() As DbSet(Of tm_department)
    Public Overridable Property tr_spk_lembur() As DbSet(Of tr_spk_lembur)
    Public Overridable Property tr_spk_lembur_line() As DbSet(Of tr_spk_lembur_line)
    Public Overridable Property tr_thr() As DbSet(Of tr_thr)
    Public Overridable Property tr_tunjangan_onetime() As DbSet(Of tr_tunjangan_onetime)
    Public Overridable Property vw_KaryawanAkanHabisKontrak() As DbSet(Of vw_KaryawanAkanHabisKontrak)
    Public Overridable Property tr_kompensasi_kontrak() As DbSet(Of tr_kompensasi_kontrak)
    Public Overridable Property tr_kompensasi_kontrak_line() As DbSet(Of tr_kompensasi_kontrak_line)
    Public Overridable Property tm_karyawan() As DbSet(Of tm_karyawan)
    Public Overridable Property tm_karyawan_datadinas() As DbSet(Of tm_karyawan_datadinas)
    Public Overridable Property tm_area() As DbSet(Of tm_area)
    Public Overridable Property tm_bonus_thr() As DbSet(Of tm_bonus_thr)
    Public Overridable Property tr_thr_line() As DbSet(Of tr_thr_line)

End Class
