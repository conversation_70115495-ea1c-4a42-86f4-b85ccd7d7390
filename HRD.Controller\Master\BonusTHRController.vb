﻿Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports StructureMap
Imports HRD.Controller

Public Class BonusTHRController
    Inherits Controller(Of tm_bonus_thr)
    Implements IBonusTHRListController, IBonusTHREditController

    Public ReadOnly Property DataList(cabang_id As String, bPosted As Boolean) As IQueryable(Of tm_bonus_thr) Implements IBonusTHRListController.DataList
        Get
            Dim serv = ObjectFactory.GetInstance(Of BonusTHRService)()
            Dim r = serv.GetBonusTHRAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os = CType(r.Output, IQueryable(Of tm_bonus_thr))
                os = os.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Posted = bPosted)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_bonus_thr With {.Periode = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of BonusTHRService)()
        Dim r = serv.GetBonusTHRByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)

        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of BonusTHRService)()
        Try
            Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult
            If r.Success Then
                Reset()
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving()
        Dim Serv = ObjectFactory.GetInstance(Of BonusTHRService)()
        Try
            Dim r = Serv.UpsertAsync(SelectedItem).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Saved = False
            End If
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try

    End Sub

    Public Overrides Sub Saving(TEntity As tm_bonus_thr)
        Dim Serv = ObjectFactory.GetInstance(Of BonusTHRService)()
        Try
            Dim r = Serv.UpsertAsync(TEntity).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Saved = False
            End If
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Sub SaveImport(dt As DataTable) Implements IBonusTHRListController.SaveImport
        Dim serv = ObjectFactory.GetInstance(Of BonusTHRService)
        AuthHelper.GetLoggedInUserInfo()

        Try

            Dim r = serv.SaveImport(dt, AuthHelper.GetLoggedInUserInfo).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                Saved = False
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If


        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try


    End Sub
End Class
Public Interface IBonusTHRListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(cabang_id As String, bPosted As Boolean) As IQueryable(Of tm_bonus_thr)
    Sub SaveImport(dt As DataTable)
End Interface
Public Interface IBonusTHREditController
    Inherits IControllerMain, IControllerEdit(Of tm_bonus_thr)

End Interface

