Imports HRD.Domain

Public Interface ICutiBesarService
    Function GetQueryableAsync() As Task(Of ResponseModel)
    Function GetByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_cuti_besar) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function PostingAsync(ByVal TEntity As tr_cuti_besar) As Task(Of ResponseModel)
    Function VoidAsync(ByVal Id As Integer, ByVal Reason As String) As Task(Of ResponseModel)
End Interface
