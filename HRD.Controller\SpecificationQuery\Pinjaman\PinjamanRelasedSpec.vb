﻿Imports System.Linq.Expressions
Imports HRD.Domain
Public Class PinjamanRelasedSpec
    Inherits CompositeSpecification(Of tr_register_pinjaman)

    Private ReadOnly _Posted As Boolean
    Sub New(bPosted As Boolean)
        _Posted = bPosted
    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of tr_register_pinjaman, Boolean))
        Get
            Return Function(f) f.Posted = _Posted
        End Get
    End Property

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of tr_register_pinjaman)) As IQueryable(Of tr_register_pinjaman)
        Return query.Where(Criteria)
    End Function


End Class
