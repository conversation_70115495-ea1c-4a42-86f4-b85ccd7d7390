﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucMasterBonus_list.ascx.vb" Inherits="HRDv23.ucMasterBonus_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<table style="width: 100%;">
    <tr>
        <td style="font-weight: bold; width: 150px">Released</td>
        <td style="width: 350px">
            <dx:ASPxCheckBox ID="chk_release" runat="server" CheckState="Unchecked">
                <ClientSideEvents ValueChanged="function(s, e) {
	grd_bonusthr.Refresh();
}" />
            </dx:ASPxCheckBox>
        </td>
        <td style="width: 150px; font-weight: bold">&nbsp;</td>
        <td>&nbsp;</td>
    </tr>
    <tr>
        <td style="font-weight: bold; width: 150px">Perusahaan</td>
        <td style="width: 350px">
            <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" Width="350px">
                <ClientSideEvents SelectedIndexChanged="function(s, e) {
	cb_cabanglist.PerformCallback();
	grd_bonusthr.Refresh();
}" Init="onInitCB">
                </ClientSideEvents>
                <Columns>
                    <dx:ListBoxColumn FieldName="KodeArea">
                    </dx:ListBoxColumn>
                    <dx:ListBoxColumn FieldName="NamaArea">
                    </dx:ListBoxColumn>
                </Columns>
                <ValidationSettings SetFocusOnError="True">
                    <RequiredField IsRequired="True" ErrorText="Required">
                    </RequiredField>
                </ValidationSettings>
            </dx:ASPxComboBox>
        </td>
        <td style="width: 150px; font-weight: bold">Cabang</td>
        <td>
            <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_cabanglist" ID="cb_cabang" Width="350px">
                <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_bonusthr.Refresh();
	//cb_karyawan.PerformCallback();
	//mycb.PerformCallback('get_setting');
}" Init="onInitCB">
                </ClientSideEvents>
                <Columns>
                    <dx:ListBoxColumn FieldName="KodeCabang">
                    </dx:ListBoxColumn>
                    <dx:ListBoxColumn FieldName="NamaCabang">
                    </dx:ListBoxColumn>
                </Columns>
                <ValidationSettings SetFocusOnError="True">
                    <RequiredField IsRequired="True" ErrorText="Required">
                    </RequiredField>
                </ValidationSettings>
            </dx:ASPxComboBox>
        </td>
    </tr>
    <tr>
        <td style="font-weight: bold; width: 150px">Import Dari Ms. Excel</td>
        <td style="width: 350px">
                <dx:ASPxUploadControl runat="server" UploadMode="Auto" ShowProgressPanel="True" ShowUploadButton="True" Width="280px" ID="ASPxUploadControl1">
                    <ValidationSettings AllowedFileExtensions=".xlsx, .XLSX">
                    </ValidationSettings>
                    <ClientSideEvents FileUploadComplete="function(s, e) {
	grd_bonusthr.PerformCallback(&#39;import&#39;);
}">
                    </ClientSideEvents>
                    <AdvancedModeSettings EnableDragAndDrop="True">
                    </AdvancedModeSettings>
                </dx:ASPxUploadControl>
            </td>
        <td style="width: 150px; font-weight: bold">&nbsp;</td>
        <td>&nbsp;</td>
    </tr>
</table>
<dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_bonusthr">
    <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_bonusthr.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_bonusthr.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_bonusthr.PerformCallback('new'); 
break;
}

}" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <SettingsPager AlwaysShowPager="True">
        <AllButton Visible="True">
        </AllButton>
        <PageSizeItemSettings Visible="True">
        </PageSizeItemSettings>
    </SettingsPager>
    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
    <SettingsBehavior AllowFocusedRow="True" />
<SettingsPopup>
<FilterControl AutoUpdatePosition="False"></FilterControl>
</SettingsPopup>
    <SettingsSearchPanel Visible="True" />
    <SettingsExport EnableClientSideExportAPI="True">
    </SettingsExport>
    <Columns>
        <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0" SelectCheckBoxPosition="Left">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                    <Image IconID="iconbuilder_security_visibility_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_area.NamaArea" VisibleIndex="2" Caption="Perusahaan">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="3" Caption="Cabang">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_karyawan.NIK" VisibleIndex="4" Caption="NIK">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_karyawan.Nama" VisibleIndex="5" Caption="Nama">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataDateColumn FieldName="Periode" VisibleIndex="6" Caption="Periode THR">
            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="MMM yyyy">
            </PropertiesDateEdit>
        </dx:GridViewDataDateColumn>
        <dx:GridViewDataTextColumn FieldName="Bonus" VisibleIndex="7">
            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n0">
            </PropertiesTextEdit>
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="9">
        </dx:GridViewDataTextColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem Name="btn_new" Text="New">
                    <Image IconID="actions_new_svg_16x16">
                    </Image>
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ShowFilterRow">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                </dx:GridViewToolbarItem>
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
</dx:ASPxGridView>

<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_bonus_thr">
</dx:EntityServerModeDataSource>
