﻿Imports System.Data.Entity
Imports HRD.Application
Imports HRD.Domain
Public Class UnitOfWork
    Implements IUnitOfWork, IDisposable

#Region "Properties"
    Private ReadOnly _context As HRDEntities
    Private dbContextTransaction As DbContextTransaction
    Private _repositories As Hashtable
#End Region
#Region "Ctor"
    Public Sub New(ByVal context As HRDEntities)
        _context = context
    End Sub
#End Region
    Public Function Repository(Of TEntity As Class)() As IRepository(Of TEntity) Implements IUnitOfWork.Repository
        _repositories = If(_repositories, New Hashtable())

        Dim type = GetType(TEntity).Name

        If Not _repositories.ContainsKey(type) Then
            Dim repositoryType = GetType(EfRepository(Of ))

            Dim repositoryInstance = Activator.CreateInstance(repositoryType.MakeGenericType(GetType(TEntity)), _context)

            _repositories.Add(type, repositoryInstance)
        End If

        Return DirectCast(_repositories(type), IRepository(Of TEntity))
    End Function
    Public Async Function SaveAsync() As Task(Of Integer) Implements IUnitOfWork.SaveAsync
        Return Await _context.SaveChangesAsync()
    End Function
    Public Function Save() As Integer Implements IUnitOfWork.Save
        Return _context.SaveChanges()
    End Function
    Public Sub BeginTransaction() Implements IUnitOfWork.BeginTransaction
        dbContextTransaction = _context.Database.BeginTransaction()
    End Sub
    Public Sub CommitTransaction() Implements IUnitOfWork.CommitTransaction
        If dbContextTransaction IsNot Nothing Then
            dbContextTransaction.Commit()
        End If
    End Sub
    Public Sub RollbackTransaction() Implements IUnitOfWork.RollbackTransaction
        If dbContextTransaction IsNot Nothing Then
            dbContextTransaction.Rollback()
        End If
    End Sub
    Private disposed As Boolean = False
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposed Then
            If disposing Then
                _context.Dispose()
            End If
        End If
        disposed = True
    End Sub
    Public Sub Dispose()
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
    Public Sub Reset() Implements IUnitOfWork.Reset
        Dim entries = _context.ChangeTracker.Entries().Where(Function(e) e.Entity IsNot Nothing).ToList()
        entries.ForEach(Sub(e) e.State = EntityState.Detached)
    End Sub

    Private Sub IDisposable_Dispose() Implements IDisposable.Dispose
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Public Function GetCurrentContext() As HRDEntities Implements IUnitOfWork.GetCurrentContext
        Return _context
    End Function
End Class
