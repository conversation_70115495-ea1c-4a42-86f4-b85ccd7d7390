﻿Imports DevExpress.Web
Public Class frmRekapThrKeBank
    Inherits PageBase

    Public Sub New()
        MyBase.New("7373")
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub
    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")
        Select Case s(0)
            Case "show_rpt"
                Me.ucRekapThrBank.ShowRPT()
            Case "send_email"
                'Me.ucSlipGaji.SendEmail()
        End Select
    End Sub

End Class