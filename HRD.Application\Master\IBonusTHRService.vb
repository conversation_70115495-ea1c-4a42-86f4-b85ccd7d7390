﻿Imports HRD.Domain
Public Interface IBonusTHRService
    Function GetBonusTHRAsync() As Task(Of ResponseModel)
    Function GetBonusTHRByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tm_bonus_thr) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function SaveImport(ByVal dt As DataTable, ByVal _userInfo As ApplicationUser) As Task(Of ResponseModel)
End Interface
