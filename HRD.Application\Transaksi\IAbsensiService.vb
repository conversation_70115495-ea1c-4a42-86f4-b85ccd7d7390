﻿Imports HRD.Domain
Public Interface IAbsensiService
    Function GetAbsensisAsync() As Task(Of ResponseModel)
    Function GetAbsensiByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_absensi) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function SaveImport(dt As DataTable) As Task(Of ResponseModel)
    Function GenerateAbsensi(cabang_id As String, startDate As Date, endDate As Date) As Task(Of ResponseModel)
End Interface
