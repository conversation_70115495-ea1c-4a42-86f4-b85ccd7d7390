﻿<%@ Master Language="VB" AutoEventWireup="true" CodeBehind="Root.master.vb" Inherits="HRDv23.Root" %>

<!DOCTYPE html>

<html>
<head runat="server" EnableViewState="false">
    <meta charset="UTF-8" />
    <title></title>
    <meta name="viewport" content="width=device-width, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0" />
    <link rel="stylesheet" type="text/css" href='<%# ResolveUrl("~/Content/Content.css") %>' />
    <link rel="stylesheet" type="text/css" href='<%# ResolveUrl("~/Content/Layout.css") %>' />
    <asp:ContentPlaceHolder ID="Head" runat="server"></asp:ContentPlaceHolder>
    <script type="text/javascript" src='<%# ResolveUrl("~/Content/Script.js") %>'></script>
    <script type="text/javascript" src='<%# ResolveUrl("~/Scripts/jquery-3.7.1.min.js") %>'></script>
     <script type="text/javascript">
        function onInitCB(s) {
            if (s.GetValue() == 0) {
                s.SetValue(null);
            }
            if (s.GetValue() == "00000000-0000-0000-0000-000000000000") {
                s.SetValue(null);
            }
        }
    </script>
</head>
<body>
    <form id="form1" runat="server" class="form">
        <div class="content-wrapper">
            <dx:ASPxPanel runat="server" ID="HeaderPanel" ClientInstanceName="headerPanel" FixedPosition="WindowTop"
                FixedPositionOverlap="true" CssClass="app-header">
                <PanelCollection>
                    <dx:PanelContent runat="server">
                        <div class="left-block">
                            <dx:ASPxMenu runat="server" ID="LeftAreaMenu" ClientInstanceName="leftAreaMenu"
                                ItemAutoWidth="false" ItemWrap="false" SeparatorWidth="0" EnableHotTrack="false"
                                Width="100%" CssClass="header-menu" SyncSelectionMode="None">
                                <ItemStyle VerticalAlign="Middle" CssClass="item" />
                                <Items>
                                    <dx:MenuItem Text="" Name="ToggleLeftPanel" GroupName="LeftPanel">
                                        <ItemStyle CssClass="toggle-item vertically-aligned" CheckedStyle-CssClass="checked selected" >
<CheckedStyle CssClass="checked selected"></CheckedStyle>
                                        </ItemStyle>
                                        <Image Url="~/Content/Images/menu.svg" Height="18px" Width="18px" />
                                    </dx:MenuItem>
                                    <dx:MenuItem Text="" Name="Back">
                                        <ItemStyle CssClass="toggle-item vertically-aligned" />
                                        <Image Url="~/Content/Images/back.svg" Height="18px" Width="18px" />
                                    </dx:MenuItem>
                                    <dx:MenuItem Text="" ItemStyle-CssClass="image-item vertically-aligned" NavigateUrl="~/">
                                        <Image SpriteProperties-CssClass="header-logo" >
<SpriteProperties CssClass="header-logo"></SpriteProperties>
                                        </Image>

<ItemStyle CssClass="image-item vertically-aligned"></ItemStyle>
                                    </dx:MenuItem>
                                </Items>
                                <ClientSideEvents ItemClick="onLeftMenuItemClick" />
                            </dx:ASPxMenu>
                        </div>
                    
                        <div class="right-block">
                            <dx:ASPxMenu runat="server" ID="RightAreaMenu" ClientInstanceName="rightAreaMenu"
                                ItemAutoWidth="false" ItemWrap="false" ShowPopOutImages="False"
                                SeparatorWidth="0" ApplyItemStyleToTemplates="true"
                                Width="100%" CssClass="header-menu" OnItemClick="RightAreaMenu_ItemClick">
                                <ItemStyle VerticalAlign="Middle" CssClass="item" />
                                <SubMenuItemStyle CssClass="item" />
                                <SubMenuStyle CssClass="header-sub-menu" />
                                <Items>
                                    <dx:MenuItem Name="AccountItem" ItemStyle-CssClass="image-item">

<ItemStyle CssClass="image-item"></ItemStyle>
                                        <TextTemplate>
                                            <div class="account-background">
                                                <div runat="server" id="AccountImage" class="empty-image" />
                                            </div>
                                        </TextTemplate>
                                        <Items>
                                            <dx:MenuItem Name="SignInItem" Text="Sign in" NavigateUrl="~/Account/SignIn.aspx"></dx:MenuItem>
                                            <dx:MenuItem Name="RegisterItem" Text="Register" NavigateUrl="~/Account/Register.aspx"></dx:MenuItem>
                                            <dx:MenuItem Name="MyAccountItem" Text="My account" ItemStyle-CssClass="myaccount-item">
<ItemStyle CssClass="myaccount-item"></ItemStyle>
                                                <TextTemplate>
                                                    <div class="user-info">
                                                        <div class="avatar">
                                                            <img runat="server" id="AvatarUrl" src="Content/Images/user.svg" />
                                                        </div>
                                                        <div class="text-container">
                                                            <dx:ASPxLabel ID="UserNameLabel" runat="server" CssClass="user-name"></dx:ASPxLabel>
                                                            <dx:ASPxLabel ID="EmailLabel" runat="server" CssClass="email"></dx:ASPxLabel>
                                                        </div>
                                                    </div>
                                                </TextTemplate>
                                            </dx:MenuItem>
                                            <dx:MenuItem Name="SignOutItem" Text="Sign out" Image-Url="Content/Images/sign-out.svg" Image-Height="16px">
<Image Height="16px" Url="Content/Images/sign-out.svg"></Image>
                                            </dx:MenuItem>
                                        </Items>
                                    </dx:MenuItem>
                                    <dx:MenuItem Text="" Name="ToggleRightPanel" GroupName="RightPanel">
                                        <ItemStyle CssClass="toggle-item vertically-aligned" CheckedStyle-CssClass="checked selected" >
<CheckedStyle CssClass="checked selected"></CheckedStyle>
                                        </ItemStyle>
                                        <Image Height="18px" SpriteProperties-CssClass="image" >
<SpriteProperties CssClass="image"></SpriteProperties>
                                        </Image>
                                    </dx:MenuItem>
                                </Items>
                                <ClientSideEvents ItemClick="onRightMenuItemClick" />
                            </dx:ASPxMenu>
                        </div>

                        <div class="menu-container">
                            <div>
                                <dx:ASPxMenu runat="server" ID="ApplicationMenu" ClientInstanceName="applicationMenu" ItemAutoWidth="false" EnableSubMenuScrolling="true"
                                    ShowPopOutImages="True" SeparatorWidth="0" ItemWrap="false"
                                    CssClass="header-menu application-menu" Width="100%" HorizontalAlign="Right" OnItemDataBound="ApplicationMenu_ItemDataBound" Theme="Default">
                                    <SettingsAdaptivity Enabled="true" EnableAutoHideRootItems="true" />
                                    <ItemStyle VerticalAlign="Middle" CssClass="item" SelectedStyle-CssClass="selected" HoverStyle-CssClass="hovered" >
<SelectedStyle CssClass="selected"></SelectedStyle>

<HoverStyle CssClass="hovered"></HoverStyle>
                                    </ItemStyle>
                                    <ItemImage Width="22" Height="22" />
                                    <SubMenuStyle CssClass="header-sub-menu" />
                                    <AdaptiveMenuImage SpriteProperties-CssClass="adaptive-image" >
<SpriteProperties CssClass="adaptive-image"></SpriteProperties>
                                    </AdaptiveMenuImage>
                                </dx:ASPxMenu>
                            </div>
                        </div>
                        <div class="dx-clear"></div>
                    </dx:PanelContent>
                </PanelCollection>
            </dx:ASPxPanel>

            <dx:ASPxPanel runat="server" ID="LeftPanel" ClientInstanceName="leftPanel"
                Collapsible="true" ScrollBars="Auto" FixedPosition="WindowLeft" Width="272px"
                CssClass="left-panel" Paddings-Padding="0" Styles-ExpandBar-CssClass="expand-bar">
                <SettingsAdaptivity CollapseAtWindowInnerWidth="960" />
                <SettingsCollapsing ExpandButton-Visible="false" ExpandEffect="PopupToRight" AnimationType="Slide" Modal="true" />
                <PanelCollection>
                    <dx:PanelContent>
                        <asp:ContentPlaceHolder runat="server" ID="LeftPanelContent"></asp:ContentPlaceHolder>
                    </dx:PanelContent>
                </PanelCollection>
                <ClientSideEvents Init="onLeftPanelInit" Collapsed="onLeftPanelCollapsed" />
            </dx:ASPxPanel>

            <dx:ASPxPanel runat="server" ID="RightPanel" ClientInstanceName="rightPanel"
                FixedPosition="WindowRight" FixedPositionOverlap="true" Collapsible="true" Paddings-Padding="0"
                ScrollBars="Auto" Width="260px" CssClass="right-panel" Styles-ExpandBar-CssClass="expand-bar">
                <SettingsCollapsing ExpandButton-Visible="false" ExpandEffect="PopupToLeft" Modal="true" />
                <PanelCollection>
                    <dx:PanelContent>
                        <asp:ContentPlaceHolder ID="RightPanelContent" runat="server"></asp:ContentPlaceHolder>
                    </dx:PanelContent>
                </PanelCollection>
                <ClientSideEvents Collapsed="onRightPanelCollapsed" />
            </dx:ASPxPanel>

            <dx:ASPxPanel runat="server" ID="PageToolbarPanel" ClientInstanceName="pageToolbarPanel"
                CssClass="page-toolbar-wrapper">
                <PanelCollection>
                    <dx:PanelContent>
                        <asp:ContentPlaceHolder runat="server" ID="PageToolbar" />
                    </dx:PanelContent>
                </PanelCollection>
                <ClientSideEvents Init="onPageToolbarInit" />
            </dx:ASPxPanel>

            <div class="content" id="pageContent">
                <asp:SiteMapPath ID="SiteMapPath1" runat="server" Font-Names="Verdana" Font-Size="0.8em" PathSeparator=" : ">
                <CurrentNodeStyle ForeColor="#333333" />
                <NodeStyle Font-Bold="True" ForeColor="#990000" />
                <PathSeparatorStyle Font-Bold="True" ForeColor="#990000" />
                <RootNodeStyle Font-Bold="True" ForeColor="#FF8000" />
            </asp:SiteMapPath>
                <asp:ContentPlaceHolder runat="server" ID="PageContent"></asp:ContentPlaceHolder>
                <div class="footer-wrapper" id="footerWrapper">
                    <div class="footer">
                        <span class="footer-left">&copy; 2023 DedyNof</span>
                        <span class="footer-right">
                            <a class="footer-link" href="#">Privacy Policy</a>
                            <a class="footer-link" href="#">Terms of Service</a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <%--<dx:ASPxSiteMapDataSource runat="server" ID="ApplicationMenuDataSource" SiteMapFileName="~/App_Data/ApplicationMenuDataSource.sitemap" />--%>
        <dx:ASPxGlobalEvents runat="server">
            <ClientSideEvents ControlsInitialized="onControlsInitialized" BrowserWindowResized="onBrowserWindowResized" />
        </dx:ASPxGlobalEvents>
    </form>
</body>
</html>