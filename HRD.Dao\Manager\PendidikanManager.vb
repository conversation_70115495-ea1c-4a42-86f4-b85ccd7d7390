﻿Public Class PendidikanManager
    Private ReadOnly daoDactory As IDaoFactory
    Public Sub New(daoDactory As IDaoFactory)
        Me.daoDactory = daoDactory

    End Sub
    ReadOnly Property DataAccess As IPendidikanDao
        Get
            Return daoDactory.GetPendidikanDao
        End Get
    End Property
    ReadOnly Property GetJurusanDao As IJurusanDao
        Get
            Return daoDactory.GetJurusanDao
        End Get
    End Property
End Class
