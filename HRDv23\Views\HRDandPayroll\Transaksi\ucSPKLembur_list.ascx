﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucSPKLembur_list.ascx.vb" Inherits="HRDv23.ucSPKLembur_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

    <table style="width: 100%;">
        <tr>
            <td style="width: 150px; font-weight: bold">Released</td>
            <td style="width: 350px">
                <dx:ASPxCheckBox ID="chk_released" runat="server" CheckState="Unchecked">
                    <ClientSideEvents ValueChanged="function(s, e) {
	    grd_spklembur.Refresh();
    }" />
                </dx:ASPxCheckBox>
            </td>
            <td style="font-weight: bold; width: 150px">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td style="width: 150px; font-weight: bold"><PERSON><PERSON>haan</td>
            <td style="width: 350px">
                <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" NullText="ALL" ClientInstanceName="cb_area_list" Width="100%">
                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	cb_cabang_list.PerformCallback();
	grd_spklembur.Refresh();
}" Init="onInitCB">
                    </ClientSideEvents>
                    <Columns>
                        <dx:ListBoxColumn FieldName="KodeArea">
                        </dx:ListBoxColumn>
                        <dx:ListBoxColumn FieldName="NamaArea">
                        </dx:ListBoxColumn>
                    </Columns>
                    <ClearButton DisplayMode="Always">
                    </ClearButton>
                    <ValidationSettings SetFocusOnError="True">
                        <RequiredField ErrorText="Required">
                        </RequiredField>
                    </ValidationSettings>
                </dx:ASPxComboBox>
            </td>
            <td style="font-weight: bold; width: 150px">Cabang</td>
            <td>
                <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_cabang_list" ID="cb_cabang" NullText="ALL" Width="350px" LoadDropDownOnDemand="True">
                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_spklembur.Refresh();
}" Init="onInitCB">
                    </ClientSideEvents>
                    <Columns>
                        <dx:ListBoxColumn FieldName="KodeCabang">
                        </dx:ListBoxColumn>
                        <dx:ListBoxColumn FieldName="NamaCabang">
                        </dx:ListBoxColumn>
                    </Columns>
                    <ClearButton DisplayMode="Always">
                    </ClearButton>
                    <ValidationSettings SetFocusOnError="True">
                        <RequiredField ErrorText="Required">
                        </RequiredField>
                    </ValidationSettings>
                </dx:ASPxComboBox>
            </td>
        </tr>
        <tr>
            <td style="width: 150px; font-weight: bold">&nbsp;</td>
            <td style="width: 350px">
                &nbsp;</td>
            <td style="font-weight: bold; width: 150px">&nbsp;</td>
            <td>
                &nbsp;</td>
        </tr>
        <tr>
            <td style="width: 150px; font-weight: bold">Import Dari Ms. Excel</td>
            <td style="width: 350px">
                <dx:ASPxUploadControl runat="server" UploadMode="Auto" ShowProgressPanel="True" ShowUploadButton="True" Width="280px" ID="ASPxUploadControl1">
                    <ValidationSettings AllowedFileExtensions=".xlsx, .XLSX">
                    </ValidationSettings>
                    <ClientSideEvents FileUploadComplete="function(s, e) {
	grd_spklembur.PerformCallback(&#39;import&#39;);
}">
                    </ClientSideEvents>
                    <AdvancedModeSettings EnableDragAndDrop="True">
                    </AdvancedModeSettings>
                </dx:ASPxUploadControl>
            </td>
            <td style="font-weight: bold; width: 150px">&nbsp;</td>
            <td>
                &nbsp;</td>
        </tr>
        </table>
    <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_spklembur">
        <ClientSideEvents CustomButtonClick="function(s, e) {
	    var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	    if(e.buttonID!='btn_print'){
		    if(e.buttonID=='btn_delete'){
			    var b=confirm('Are you sure to delete this item?');
			    if(b){
				    cp_spk_lembur.PerformCallback(e.buttonID+';'+rowKey);
			    }
		    }else{cp_spk_lembur.PerformCallback(e.buttonID+';'+rowKey);}
	    }else{wd1.Show();}

    }" ToolbarItemClick="function(s, e) {
	    switch (e.item.name) { 
    case 'btn_new':
    cp_spk_lembur.PerformCallback('new;'+cb_area_list.GetValue()+';'+cb_cabang_list.GetValue()); 
    break;
    }

    }" />
        <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
        </SettingsAdaptivity>
        <SettingsPager AlwaysShowPager="True">
            <AllButton Visible="True">
            </AllButton>
            <PageSizeItemSettings Visible="True">
            </PageSizeItemSettings>
        </SettingsPager>
        <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
        <SettingsBehavior AllowFocusedRow="True" />
    <SettingsPopup>
    <FilterControl AutoUpdatePosition="False"></FilterControl>
    </SettingsPopup>
        <SettingsSearchPanel Visible="True" />
        <SettingsExport EnableClientSideExportAPI="True">
        </SettingsExport>
        <Columns>
            <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
                <CustomButtons>
                    <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                        <Image IconID="iconbuilder_actions_edit_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                        <Image IconID="scheduling_delete_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                        <Image IconID="iconbuilder_security_visibility_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_unrelease" Text="Un-Release">
                        <Image IconID="spreadsheet_3symbolsuncircled_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                </CustomButtons>
            </dx:GridViewCommandColumn>
<dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="1" Caption="Perusahaan"></dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="2" Caption="Cabang">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="NoSpk" VisibleIndex="3">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="4">
                <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                </PropertiesDateEdit>
            </dx:GridViewDataDateColumn>
            <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="6">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="TotalJam" VisibleIndex="5">
                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                </PropertiesTextEdit>
            </dx:GridViewDataTextColumn>
        </Columns>
        <Toolbars>
            <dx:GridViewToolbar>
                <Items>
                    <dx:GridViewToolbarItem Name="btn_new" Text="New">
                        <Image IconID="actions_new_svg_16x16">
                        </Image>
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Command="ShowFilterRow">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                    </dx:GridViewToolbarItem>
                </Items>
            </dx:GridViewToolbar>
        </Toolbars>
    </dx:ASPxGridView>

    <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_spk_lembur">
    </dx:EntityServerModeDataSource>
