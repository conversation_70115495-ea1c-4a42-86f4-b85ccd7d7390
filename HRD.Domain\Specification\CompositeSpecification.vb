﻿Imports HRD.Domain

Public MustInherit Class CompositeSpecification(Of T)
    Implements ISpecification(Of T)

    Public MustOverride Function IsSatisfiedBy(candidate As T) As Boolean Implements ISpecification(Of T).IsSatisfiedBy


    Public Function [And](other As ISpecification(Of T)) As ISpecification(Of T) Implements ISpecification(Of T).And
        Return New AndSpecification(Of T)(Me, other)
    End Function

    Public Function [Not]() As ISpecification(Of T) Implements ISpecification(Of T).Not
        Return New NotSpecification(Of T)(Me)
    End Function
End Class
