﻿Imports System.ComponentModel
Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Imports HRD.Helpers

Public Class rpt_cuti_perkaryawan
    Dim i As Integer

    Public endDate As Date
    Public startDate As Date




    Private Sub Detail1_BeforePrint(sender As Object, e As CancelEventArgs)
        i += 1
        'cell_no.Text = $"{i}."



    End Sub



    Private Sub ReportFooter_BeforePrint(sender As Object, e As CancelEventArgs)
        'Dim o = CType(Me.GetCurrentRow(), tr_absensi)

        'txt_saldoCuti.Text = myFunction.GetSaldoCuti(o.karyawan_id, endDate)
    End Sub

    Private Sub Detail_BeforePrint(sender As Object, e As CancelEventArgs) Handles Detail.BeforePrint
        i = 0

        Dim o = CType(Me.GetCurrentRow, tm_karyawan)
        Dim os = o.tr_absensi.Where(Function(f) (f.Cuti_b = True Or f.CutiBersama = True Or (f.Ijin_id.HasValue AndAlso f.tr_ijin.tm_ijin_master.PotongCutiTahunan = True)) AndAlso f.<PERSON> >= startDate AndAlso f.<PERSON>gal < endDate.AddDays(1))
        Dim subrpt As New subRpt_cutiPerKaryawan
        subrpt.Tgl2 = endDate
        subrpt.kar_id = o.Id
        subrpt.DataSource = os
        Me.XrSubreport1.ReportSource = subrpt

    End Sub


End Class