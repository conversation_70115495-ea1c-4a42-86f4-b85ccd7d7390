# Optimasi Fungsi Generate Absensi

Berikut adalah rekomendasi optimasi untuk fungsi generate absensi di sistem Anda:

## 1. Optimasi Fungsi GenerateAbsensi

```vb
Public Async Function GenerateAbsensi(cabang_id As String, startDate As Date, endDate As Date) As Task(Of ResponseModel) Implements IAbsensiService.GenerateAbsensi
    Try
        Dim tgl1 As Date = startDate.Date
        Dim tgl2 As Date = endDate.Date

        ' Preload all necessary data in one go to reduce database queries
        Dim cab = _myFunction.tm_cabang(cabang_id)
        
        ' Get all employees for this branch in one query
        Dim kars = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking
            .Where(Function(f) f.Cabang_id = cabang_id And (f.Berhenti_b <> True Or (f.<PERSON>rhenti_b = True And f.Tgl_Berhenti > startDate)))
            .Include("tm_karyawan_datadinas")
            .ToList()

        ' Get all holidays for this date range in one query
        Dim liburs = _unitOfWork.Repository(Of tm_libur_nasional).TableNoTracking
            .Where(Function(f) f.Area_id = cab.Area_id AndAlso f.StartDate <= tgl2 AndAlso f.EndDate >= tgl1)
            .ToList()

        ' Get all existing attendance records for this date range in one query
        Dim existingAbsensi = _unitOfWork.Repository(Of tr_absensi).TableNoTracking
            .Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Tanggal >= tgl1 AndAlso f.Tanggal <= tgl2)
            .ToList()
            .GroupBy(Function(a) a.karyawan_id)
            .ToDictionary(Function(g) g.Key, Function(g) g.ToList())

        ' Get all leave requests for this date range in one query
        Dim ijins = _unitOfWork.Repository(Of tr_ijin).TableNoTracking
            .Where(Function(f) f.Posted AndAlso f.StartDate <= tgl2 AndAlso f.EndDate >= tgl1)
            .ToList()
            .GroupBy(Function(i) i.Karyawan_id)
            .ToDictionary(Function(g) g.Key, Function(g) g.ToList(), EqualityComparer(Of Integer).Default)

        ' Get all overtime records for this date range in one query
        Dim lemburs = _unitOfWork.Repository(Of tr_spk_lembur_line).TableNoTracking
            .Where(Function(f) f.tr_spk_lembur.Posted AndAlso f.tr_spk_lembur.Tanggal >= tgl1 AndAlso f.tr_spk_lembur.Tanggal <= tgl2)
            .ToList()
            .GroupBy(Function(l) l.Karyawan_id)
            .ToDictionary(Function(g) g.Key, Function(g) g.ToList(), EqualityComparer(Of Integer).Default)

        ' Process employees in batches to avoid memory issues
        Const batchSize As Integer = 50
        Dim results As New List(Of ResponseModel)()
        
        For i As Integer = 0 To kars.Count - 1 Step batchSize
            Dim batch = kars.Skip(i).Take(batchSize).ToList()
            Dim batchTasks = batch.Select(Function(kar) GenerateAbsensiPerKaryawan(
                kar, 
                startDate, 
                endDate, 
                liburs, 
                If(existingAbsensi.ContainsKey(kar.Id), existingAbsensi(kar.Id), New List(Of tr_absensi)()), 
                If(ijins.ContainsKey(kar.Id), ijins(kar.Id), New List(Of tr_ijin)()), 
                If(lemburs.ContainsKey(kar.Id), lemburs(kar.Id), New List(Of tr_spk_lembur_line)())
            ))
            
            Dim batchResults = Await Task.WhenAll(batchTasks)
            results.AddRange(batchResults)
            
            ' Save after each batch to reduce memory usage
            _unitOfWork.Save()
        Next

        ' Check the result of each task
        If results.All(Function(r) r.Success) Then
            ' Bulk update operations
            _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) 
                f.Cabang_id = cabang_id AndAlso 
                f.Tanggal >= startDate AndAlso 
                f.Tanggal <= endDate And 
                f.Masuk_b = True And 
                f.Keterangan <> Nothing
            ).Update(Function(u) New tr_absensi With {.Keterangan = ""})

            _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) 
                f.Cabang_id = cabang_id AndAlso 
                f.Tanggal >= startDate AndAlso 
                f.Tanggal <= endDate And 
                f.Alpha_b = True And 
                Not f.Keterangan.Contains("Alpha")
            ).Update(Function(u) New tr_absensi With {.Keterangan = "Alpha"})

            _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) 
                f.Cabang_id = cabang_id AndAlso 
                f.Tanggal >= startDate AndAlso 
                f.Tanggal <= endDate And 
                f.tm_karyawan.Tgl_Berhenti <= startDate
            ).Delete

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse("Sukses", Nothing)
        Else
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End If
    Catch ex As Exception
        Log(NameOf(Me.GenerateAbsensi), ex.Message)
        Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
    End Try
End Function
```

## 2. Optimasi Fungsi GenerateAbsensiPerKaryawan

```vb
Private Async Function GenerateAbsensiPerKaryawan(kar As tm_karyawan, startDate As Date, endDate As Date, 
                                             liburs As List(Of tm_libur_nasional),
                                             existingAbsensi As List(Of tr_absensi),
                                             ijins As List(Of tr_ijin),
                                             lemburs As List(Of tr_spk_lembur_line)) As Task(Of ResponseModel)
    Try
        ' Get attendance settings based on date
        Dim shift As Integer = If(kar.Ship.HasValue, kar.Ship.Value, 0)
        Dim settingAbs = _myFunction.tm_absensi_setting_by_date(kar.Cabang_id, startDate.Date, shift)
        Dim repo = _unitOfWork.Repository(Of tr_absensi)
        Dim tgl1 As Date = startDate.Date
        Dim tgl2 As Date = endDate.Date

        ' Filter existing absensi for Alpha records
        Dim alphaAbsensi = existingAbsensi.Where(Function(f) f.Alpha_b = True AndAlso f.FromAbsensi <> True).ToList()

        ' Process existing records in one batch
        Dim taskUpdate = UpdateExistingAbsensi(alphaAbsensi, lemburs, ijins, liburs, kar)

        ' Generate list of all dates in the range
        Dim allDates = Enumerable.Range(0, (endDate - startDate).Days + 1)
                       .Select(Function(offset) startDate.AddDays(offset))
                       .ToList()

        ' Get all dates that already have attendance records
        Dim existingDates = existingAbsensi.Select(Function(a) a.Tanggal.Date).Distinct().ToList()

        ' Find dates that need new attendance records
        Dim missingDates = allDates.Except(existingDates).ToList()

        ' Process new attendance records in batches
        Dim newAbsensiRecords As New List(Of tr_absensi)()
        Dim _dicAlpha As New Dictionary(Of Integer, Integer)()

        ' Process missing dates to reduce memory usage
        For Each missingDate In missingDates
            Dim absensiData = DetermineAbsensiData(kar, missingDate, settingAbs, _dicAlpha)
            If absensiData IsNot Nothing Then
                ' Apply leave, overtime, and holiday rules
                Dim ijinForDate = ijins.FirstOrDefault(Function(i) i.StartDate <= missingDate AndAlso i.EndDate >= missingDate)
                If ijinForDate IsNot Nothing Then
                    ' Apply leave rules
                    absensiData.Ijin_b = If(ijinForDate.tm_ijin_master.KodeIjin.Substring(0, 1) <> "C", True, False)
                    absensiData.Keterangan = ijinForDate.tm_ijin_master.NamaIjin
                    absensiData.Cuti_b = If(ijinForDate.tm_ijin_master.IsCuti = True, True, False)
                    absensiData.PotongGaji = If(ijinForDate.tm_ijin_master.PotongGaji = True, True, False)
                    absensiData.Alpha_b = False
                    absensiData.Ijin_id = ijinForDate.Id
                Else
                    Dim lemburForDate = lemburs.FirstOrDefault(Function(l) l.tr_spk_lembur.Tanggal.Date = missingDate.Date)
                    If lemburForDate IsNot Nothing Then
                        ' Apply overtime rules
                        absensiData.LemburLine_id = lemburForDate.Id
                        _myFunction.UpdateAbsensiDataLembur(absensiData, kar)
                    Else
                        Dim liburForDate = liburs.FirstOrDefault(Function(l) l.StartDate <= missingDate AndAlso l.EndDate >= missingDate)
                        If liburForDate IsNot Nothing Then
                            ' Apply holiday rules
                            absensiData.LiburNasional_id = liburForDate.Id
                            absensiData.Off_b = True
                            absensiData.Alpha_b = False
                            absensiData.Ijin_b = False
                            absensiData.Ijin_id = Nothing
                            absensiData.Cuti_b = liburForDate.CutiBersama
                            absensiData.PotongGaji = False
                            absensiData.Keterangan = liburForDate.NamaHariLibur
                            absensiData.CutiBersama = liburForDate.CutiBersama
                            absensiData.LiburNasional = True
                        End If
                    End If
                End If
                newAbsensiRecords.Add(absensiData)
            End If
        Next

        ' Process the update task
        Dim r = Await taskUpdate
        If r.Success AndAlso r.Output IsNot Nothing Then
            Await UpdateRangeAbsensi(r.Output)
        End If

        ' Add all new attendance records at once
        If newAbsensiRecords.Any() Then
            Await repo.AddRangeAsync(newAbsensiRecords)
        End If

        Return ResponseModel.SuccessResponse("Sukses", Nothing)
    Catch ex As Exception
        Log(NameOf(Me.GenerateAbsensiPerKaryawan), ex.Message)
        Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
    End Try
End Function
```

## 3. Optimasi Fungsi getJumlahOffMingguan

```vb
Private Function getJumlahOffMingguan(kar_id As String, tgl As Date, ByRef _dicAlpha As Dictionary(Of Integer, Integer)) As Integer
    ' Menggunakan dictionary untuk caching hasil
    Dim weekOfMonth As Integer = GetWeekOfMonth(tgl)
    Dim _key As Integer = weekOfMonth
    
    ' Jika sudah ada di cache, gunakan nilai yang sudah dihitung
    If _dicAlpha.ContainsKey(_key) Then
        Dim jmlAlpha As Integer = _dicAlpha(_key)
        jmlAlpha += 1
        _dicAlpha(_key) = jmlAlpha
        Return jmlAlpha
    End If
    
    ' Menghitung tanggal awal minggu (Senin)
    Dim startDate As Date = tgl.AddDays((-1) * tgl.DayOfWeek + 1)
    Dim endDate As Date = tgl
    
    If tgl < startDate Then
        startDate = startDate.AddDays(-7)
        weekOfMonth = GetWeekOfMonth(startDate)
        _key = weekOfMonth
    End If
    
    ' Hitung jumlah hari OFF dalam minggu ini
    Dim abs = _unitOfWork.Repository(Of tr_absensi).TableNoTracking
                    .Where(Function(f) f.karyawan_id = kar_id _
                                     And f.Tanggal >= startDate _
                                     And f.Tanggal <= endDate _
                                     And f.Off_b = True)
                    .Count
    
    ' Tambahkan ke cache
    Dim jmlAlpha As Integer = abs + 1
    _dicAlpha.Add(_key, jmlAlpha)
    
    Return jmlAlpha
End Function
```

## Manfaat Optimasi

1. **Pengurangan Kueri Database**: Dengan melakukan preloading data dalam satu kueri, kita mengurangi jumlah kueri database yang diperlukan.

2. **Pemrosesan Batch**: Memproses karyawan dalam batch mengurangi penggunaan memori dan meningkatkan performa.

3. **Paralelisasi**: Menggunakan Task.WhenAll untuk memproses beberapa karyawan secara paralel.

4. **Caching**: Menggunakan dictionary untuk menyimpan hasil perhitungan yang sering digunakan.

5. **Bulk Operations**: Menggunakan operasi bulk untuk update dan delete data.

## Implementasi

Untuk mengimplementasikan optimasi ini:

1. Ganti fungsi `GenerateAbsensi` dengan versi yang dioptimasi
2. Ganti fungsi `GenerateAbsensiPerKaryawan` dengan versi yang dioptimasi
3. Ganti fungsi `getJumlahOffMingguan` dengan versi yang dioptimasi

Pastikan untuk menguji perubahan ini secara menyeluruh sebelum menerapkannya di lingkungan produksi.
