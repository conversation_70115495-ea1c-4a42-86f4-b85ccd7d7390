﻿Imports Microsoft.VisualBasic
Imports System
Imports DevExpress.Web
Imports HRDv23.Model
Imports HRD.Helpers
Imports HRD.Dao

Partial Public Class SignInModule
    Inherits System.Web.UI.Page

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly manager As New UserManager(daoFactory)
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs)
        Session.Clear()
        Session.Abandon()
    End Sub

    Protected Sub SignInButton_Click(ByVal sender As Object, ByVal e As EventArgs)
        FormLayout.FindItemOrGroupByName("GeneralError").Visible = False
        If ASPxEdit.ValidateEditorsInContainer(Me) Then
            ' DXCOMMENT: You Authentication logic
            If (Not AuthHelper.SignIn(UserNameTextBox.Text, PasswordButtonEdit.Text)) Then
                GeneralErrorDiv.InnerText = "Invalid login attempt."
                FormLayout.FindItemOrGroupByName("GeneralError").Visible = True
            Else
                Dim user = manager.DataAccess.FindBy(Function(f) f.Username = UserNameTextBox.Text).FirstOrDefault
                Dim roles = user.tm_user_role.Select(Function(r) r.tm_role.RoleName)
                Dim rolestring = String.Join(",", roles)


                Dim ticket As FormsAuthenticationTicket = New FormsAuthenticationTicket(2, user.Username, DateTime.Now, DateTime.Now.AddHours(6), True, rolestring, FormsAuthentication.FormsCookiePath)
                Dim has As String = FormsAuthentication.Encrypt(ticket)
                Dim cookie As HttpCookie = New HttpCookie(FormsAuthentication.FormsCookieName, has)
                If ticket.IsPersistent Then
                    cookie.Expires = ticket.Expiration
                End If

                Response.Cookies.Add(cookie)

                Response.Redirect("~/")
            End If
        End If
    End Sub

End Class