﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web
Imports System.IO
Imports OfficeOpenXml

Public Class ucSPKLembur_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ISPKLemburEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_spk_lembur_release.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_spk_lembur_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_spk_lembur.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_spk_lembur.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                ElseIf Controller.Action = Action.UnPosting Then
                    btn_save.Text = "Un-Release..."
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction

        If Controller.Action = Action.Posting Then
            Controller.Posting(oSelectItem)
            Return
        End If

        If Controller.Action = Action.UnPosting Then
            Controller.UnPosting(oSelectItem)
            Return
        End If

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tr_spk_lembur
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tr_spk_lembur)
        End Get
        Set(value As tr_spk_lembur)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property

    Public Sub BindGrid()
        Dim _user = AuthHelper.GetLoggedInUserInfo

        oSelectItem.tr_spk_lembur_line.Clear()

        Dim sMessage As New HashSet(Of String)()

        Dim dt = Session("_data")
        Dim i As Integer = 0
        Dim bNew As Boolean = False

        For Each dr As DataRow In dt.Rows
            If i = 0 Then
                i = i + 1
                Continue For
            End If

            Dim _Kar = myFunction.tm_karyawanByNIK(dr(0))
            If _Kar Is Nothing Then

                Continue For
            End If


            Dim o = oSelectItem.tr_spk_lembur_line.Where(Function(f) f.Deleting <> True AndAlso f.Karyawan_id = _Kar.Id).SingleOrDefault

            If o IsNot Nothing Then
                Continue For
            End If

            o = New tr_spk_lembur_line
            With o
                o.Karyawan_id = _Kar.Id
            End With

            oSelectItem.tr_spk_lembur_line.Add(o)
        Next

        Me.ASPxGridView1.DataBind()
        If sMessage.Count > 0 Then
            Me.ASPxGridView1.JSProperties("cpMsg") = MessageFormatter.GetFormattedErrorMessage(String.Join("</BR>", sMessage))
        End If
    End Sub

    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs)
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Protected Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs)
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub

    Protected Sub LinqServerModeDataSource1_Selecting(sender As Object, e As DevExpress.Data.Linq.LinqServerModeDataSourceSelectEventArgs) Handles LinqServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        If oSelectItem Is Nothing Then
            Return
        End If

        e.QueryableSource = oSelectItem.tr_spk_lembur_line.Where(Function(f) f.Deleting <> True).AsQueryable
    End Sub

    Private Sub ASPxGridView1_RowInserting(sender As Object, e As ASPxDataInsertingEventArgs) Handles ASPxGridView1.RowInserting
        Dim o As New tr_spk_lembur_line
        With o
            .Karyawan_id = e.NewValues("Karyawan_id")
            .tm_karyawan = myFunction.tm_karyawan(.Karyawan_id)
            oSelectItem.tr_spk_lembur_line.Add(o)
        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView1.RowUpdating
        Dim o = CType(oSelectItem.tr_spk_lembur_line.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tr_spk_lembur_line)
        With o
            .Karyawan_id = e.NewValues("Karyawan_id")
            .tm_karyawan = myFunction.tm_karyawan(.Karyawan_id)

        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowDeleting(sender As Object, e As ASPxDataDeletingEventArgs) Handles ASPxGridView1.RowDeleting
        Dim o = CType(oSelectItem.tr_spk_lembur_line.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tr_spk_lembur_line)
        With o
            .Deleting = True
        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowValidating(sender As Object, e As ASPxDataValidationEventArgs) Handles ASPxGridView1.RowValidating
        Dim os = oSelectItem.tr_spk_lembur_line.Where(Function(f) f.RowGuid <> e.Keys("RowGuid"))
        os = os.Where(Function(f) f.Karyawan_id = e.NewValues("Karyawan_id"))
        If os.Count > 0 Then
            e.RowError = "Karyawan ini sudah ada di daftar"
        End If
    End Sub

    Private Sub ASPxGridView1_CommandButtonInitialize(sender As Object, e As ASPxGridViewCommandButtonEventArgs) Handles ASPxGridView1.CommandButtonInitialize

        If Controller Is Nothing Then
            Return
        End If

        Select Case e.ButtonType
            Case ColumnCommandButtonType.New, ColumnCommandButtonType.Edit, ColumnCommandButtonType.Delete
                e.Enabled = (oAction = Action.AddNew Or oAction = Action.Edit)
        End Select
    End Sub
    Private Sub ASPxUploadControl1_FileUploadComplete(sender As Object, e As FileUploadCompleteEventArgs) Handles ASPxUploadControl1.FileUploadComplete
        Dim fileName As String = e.UploadedFile.FileName
        Dim fileExtension As String = System.IO.Path.GetExtension(fileName)
        Dim savePath As String = Server.MapPath("~/Content/Uploads/")
        Dim fileSavePath As String = savePath & fileName
        If Not System.IO.Directory.Exists(savePath) Then
            System.IO.Directory.CreateDirectory(savePath)
        End If
        e.UploadedFile.SaveAs(fileSavePath)
        Dim dt As DataTable = MyMethod.ReadExcelFile(fileSavePath)
        Session("_data") = dt
    End Sub

    Private Sub ASPxGridView1_CustomCallback(sender As Object, e As ASPxGridViewCustomCallbackEventArgs) Handles ASPxGridView1.CustomCallback
        If String.IsNullOrEmpty(e.Parameters) Then
            Return
        End If
        Select Case e.Parameters.ToString
            Case "import"
                Me.BindGrid()
        End Select
    End Sub
End Class
