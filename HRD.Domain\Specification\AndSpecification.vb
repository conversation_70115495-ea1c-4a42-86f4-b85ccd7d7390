﻿Public Class AndSpecification(Of T)
    Inherits CompositeSpecification(Of T)

    Private _leftSpecification As ISpecification(Of T)
    Private _rightSpecification As ISpecification(Of T)

    Public Sub New(ByVal leftSpecification As ISpecification(Of T), ByVal rightSpecification As ISpecification(Of T))
        _leftSpecification = leftSpecification
        _rightSpecification = rightSpecification
    End Sub

    Public Overrides Function IsSatisfiedBy(ByVal candidate As T) As Boolean
        Return _leftSpecification.IsSatisfiedBy(candidate) AndAlso _rightSpecification.IsSatisfiedBy(candidate)
    End Function
End Class
