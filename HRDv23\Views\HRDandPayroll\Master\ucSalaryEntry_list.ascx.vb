﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Public Class ucSalaryEntry_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IDataDinasListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id

        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If


        e.QueryableSource = Controller.DataListEntriSalary(cb_area.Value, cb_cabang.Value).AsQueryable
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub
End Class