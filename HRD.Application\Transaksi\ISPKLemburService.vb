﻿Imports HRD.Application
Imports HRD.Domain
Public Interface ISPKLemburService
    Function GetQueryableAsync() As Task(Of ResponseModel)
    Function GetByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_spk_lembur, oLines As IList(Of tr_spk_lembur_line)) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function PostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel)
    Function UnPostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel)
End Interface
