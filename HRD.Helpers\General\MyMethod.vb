﻿Imports System.IO
Imports System.Net.Mail
Imports System.Reflection
Imports System.Web.UI
Imports DevExpress.Web
Imports ExcelDataReader
Imports HRD.Dao
Imports Z.EntityFramework.Plus

Public NotInheritable Class MyMethod
    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()

#Region "FormLayout"
    Private Shared _tipeEnum As TipeEnumFormLayout
    Private Shared _domain As Object
    Public Shared Sub FormLayoutMyMethod(aspxForm As ASPxFormLayout, _tipe As TipeEnumFormLayout, Optional ByRef _object As Object = Nothing)
        _tipeEnum = _tipe
        _domain = _object
        For Each item In aspxForm.Items
            If TypeOf item Is LayoutGroupBase Then
                TryCast(item, LayoutGroupBase).ForEach(AddressOf MyMethod.GetNestedControls)
            ElseIf TypeOf item Is LayoutItem Then
                SetValue(TryCast(item, LayoutItem), _tipeEnum, _domain)
            End If
        Next
    End Sub
    Private Shared Sub GetNestedControls(item As LayoutItemBase)
        For Each c As LayoutItemBase In item.Collection
            If TypeOf c Is LayoutGroup Then
                Dim _layoutgroup = TryCast(c, LayoutGroup)
                TryCast(c, LayoutGroup).ForEach(AddressOf GetNestedControls)
            End If
            If TypeOf c Is LayoutItem Then
                SetValue(TryCast(c, LayoutItem), _tipeEnum, _domain)
            End If
        Next c
    End Sub
    Private Shared Sub SetValue(ByVal c As LayoutItem, _tipe As TipeEnumFormLayout, Optional ByRef _object As Object = Nothing)
#Disable Warning BC40000 ' Type or member is obsolete
        For Each control As Control In c.LayoutItemNestedControlContainer.Controls
#Enable Warning BC40000 ' Type or member is obsolete
            Dim editor As ASPxEdit = TryCast(control, ASPxEdit)
            If editor IsNot Nothing Then
                Select Case _tipe
                    Case TipeEnumFormLayout.SetIsValidValue
                        editor.IsValid = True
                    Case TipeEnumFormLayout.SetValueToDomain
                        If c.FieldName <> "" Then
                            Dim myClassType As Type = _object.GetType

                            Dim agePropertyInfo As PropertyInfo = myClassType.GetProperty(c.FieldName)

                            ' Check if the property type is Integer
                            If agePropertyInfo.PropertyType Is GetType(Integer) Then
                                _object.GetType().GetProperty(c.FieldName).SetValue(_object, CInt(editor.Value))
                            ElseIf agePropertyInfo.PropertyType Is GetType(Nullable(Of Int32)) Then
                                If CInt(editor.Value) = 0 Then
                                    _object.GetType().GetProperty(c.FieldName).SetValue(_object, Nothing)
                                Else
                                    _object.GetType().GetProperty(c.FieldName).SetValue(_object, CInt(editor.Value))
                                End If
                            Else
                                _object.GetType().GetProperty(c.FieldName).SetValue(_object, editor.Value)
                            End If


                        End If
                    Case TipeEnumFormLayout.SetDisableForm
                        If editor.ReadOnly Then
                        Else
                            editor.Enabled = False
                        End If
                    Case TipeEnumFormLayout.SetEnableForm
                        If editor.ReadOnly Then
                        Else
                            editor.Enabled = True
                        End If
                End Select


            End If
        Next control
    End Sub
#End Region

    Public Shared Sub Area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetAreaDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Area_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetAreaDao

        Dim os = mgr.FindBy(Function(f) (f.KodeArea.ToString.Contains(e.Filter) Or f.NamaArea.ToString.Contains(e.Filter)))
        Dim user = AuthHelper.GetLoggedInUserInfo
        os = os.Where(Function(f) user.AreaIdPermisionList.Contains(f.Id))
        'If user.PermisionArea < 2 Then
        '    os = os.Where(Function(f) f.Id = user.Area_id)
        'End If
        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Cabang_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetCabangDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Cabang_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs, area_id As String)
        Dim mgr = daoFactory.GetCabangDao

        Dim os = mgr.FindBy(Function(f) (f.KodeCabang.ToString.Contains(e.Filter) Or f.NamaCabang.ToString.Contains(e.Filter)))
        os = os.Where(Function(f) f.Area_id = area_id)

        Dim _user = AuthHelper.GetLoggedInUserInfo
        If _user.PermisionArea = 0 Then
            os = os.Where(Function(f) f.Id = _user.Cabang_id)
        End If

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Role_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetRoleDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Role_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetRoleDao

        Dim os = mgr.FindBy(Function(f) (f.RoleName.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub JenisKelamin_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetJenisKelaminDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub JenisKelamin_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetJenisKelaminDao

        Dim os = mgr.FindBy(Function(f) (f.JenisKelamin.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Agama_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetAgamaDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Agama_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetAgamaDao

        Dim os = mgr.FindBy(Function(f) (f.agama.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub StatusPTKP_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetStatusPTKPDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub StatusPTKP_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetStatusPTKPDao

        Dim os = mgr.FindBy(Function(f) (f.KodeStatus.ToString.Contains(e.Filter) Or f.StatusPernikahan.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub StatusPerkawinan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetStatusPerkawinanDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub StatusPerkawinan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetStatusPerkawinanDao

        Dim os = mgr.FindBy(Function(f) (f.KodeStatus.ToString.Contains(e.Filter) Or f.StatusPernikahan.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Pendidikan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetPendidikanDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Pendidikan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetPendidikanDao

        Dim os = mgr.FindBy(Function(f) (f.KodePendidikan.ToString.Contains(e.Filter) Or f.NamaPendidikan.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Jurusan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetJurusanDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Jurusan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs, pendidikan_id As String)
        Dim mgr = daoFactory.GetJurusanDao

        Dim os = mgr.FindBy(Function(f) (f.KodeJurusan.ToString.Contains(e.Filter) Or f.NamaJurusan.ToString.Contains(e.Filter)))
        os = os.Where(Function(f) f.Pendidikan_id = pendidikan_id)
        os = os.OrderBy(Function(f) f.id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub NamaBank_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetNamaBankDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub NamaBank_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetNamaBankDao

        Dim os = mgr.FindBy(Function(f) (f.KodeBank.ToString.Contains(e.Filter) Or f.NamaBank.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Karyawan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetKaryawanDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Karyawan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs, cabang_id As String)
        Dim mgr = daoFactory.GetKaryawanDao

        Dim os = mgr.FindBy(Function(f) (f.NIK.ToString.Contains(e.Filter) Or f.Nama.ToString.Contains(e.Filter)))
        os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Berhenti_b <> True)

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)}, "c_karyawan").ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Karyawan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs, cabang_id As String, tipeAbsen As Integer)
        Dim mgr = daoFactory.GetKaryawanDao

        Dim os = mgr.FindBy(Function(f) (f.NIK.ToString.Contains(e.Filter) Or f.Nama.ToString.Contains(e.Filter)))
        os = os.Where(Function(f) f.Cabang_id = cabang_id And f.Berhenti_b <> True And f.TipeAbsensiOFF = tipeAbsen)

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)}, "c_karyawan").ToList
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Department_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetDepartmentDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Department_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetDepartmentDao

        Dim os = mgr.FindBy(Function(f) (f.NamaDepartemen.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Jabatan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetJabatanDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Jabatan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetJabatanDao

        Dim os = mgr.FindBy(Function(f) (f.Jabatan.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Ijin_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetIjinMasterDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub Ijin_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetIjinMasterDao

        Dim os = mgr.FindBy(Function(f) (f.KodeIjin.ToString.Contains(e.Filter) Or f.NamaIjin.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub ShiftKaryawan_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs)
        If e.Value Is Nothing Then
            Return
        End If
        If e.Value.ToString.Trim = "" Then
            Return
        End If
        Dim mgr = daoFactory.GetShiftAbsensiDao

        Dim i As Integer = Nothing
        If Not Integer.TryParse(e.Value.ToString, i) Then
            Return
        End If


        Dim os = mgr.FindBy(Function(f) f.Id = i)
        Dim cb = CType(source, ASPxComboBox)
        cb.DataSource = os.ToList
        cb.DataBind()


    End Sub
    Public Shared Sub ShiftKaryawan_ItemsRequestedByFilterCondition(source As Object, e As DevExpress.Web.ListEditItemsRequestedByFilterConditionEventArgs)
        Dim mgr = daoFactory.GetShiftAbsensiDao

        Dim os = mgr.FindBy(Function(f) (f.Deskripsi.ToString.Contains(e.Filter)))

        os = os.OrderBy(Function(f) f.Id)
        os = os.Skip(e.BeginIndex)
        os = os.Take(e.EndIndex - e.BeginIndex + 1)
        Dim cb = CType(source, ASPxComboBox)

        'cb.DataSource = os.FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromHours(1)}).ToList
        cb.DataSource = os.FromCache(absoluteExpiration:=Now.AddMinutes(2)).ToList
        cb.DataBind()


    End Sub
    Public Shared Sub SendEmail(toEmail As String, subject As String, body As String, attachment As Byte(), attachmentName As String)
        'Dim smtpClient As New SmtpClient("smtp.gmail.com") ' Ganti dengan konfigurasi SMTP server Anda
        Dim smtpClient As New SmtpClient("mail.beemoist.com")
        'smtpClient.Port = 587 ' Ganti dengan port SMTP server Anda
        smtpClient.Port = 587
        'smtpClient.Credentials = New System.Net.NetworkCredential("<EMAIL>", "cargloss162") ' Ganti dengan kredensial Anda
        smtpClient.Credentials = New System.Net.NetworkCredential("<EMAIL>", "g02496011") ' Ganti dengan kredensial Anda
        smtpClient.EnableSsl = True ' Ganti sesuai konfigurasi SMTP server Anda


        Dim mailMessage As New MailMessage()
        mailMessage.From = New MailAddress("<EMAIL>") ' Ganti dengan email pengirim
        mailMessage.To.Add(toEmail)
        mailMessage.Subject = subject
        mailMessage.Body = body
        mailMessage.IsBodyHtml = True

        ' Lampirkan slip gaji
        If attachment IsNot Nothing Then
            Dim memoryStream As New MemoryStream(attachment)
            Dim attachmentItem As New Attachment(memoryStream, attachmentName, "application/pdf")
            mailMessage.Attachments.Add(attachmentItem)
        End If

        smtpClient.Send(mailMessage)
    End Sub


    Public Shared Sub SendEmailWithAttachments(toEmail As String, subject As String, body As String, attachmentBytes1 As Byte(), attachmentName1 As String, attachmentBytes2 As Byte(), attachmentName2 As String)
        Using mail As New MailMessage()
            'mail.From = New MailAddress("<EMAIL>") ' Ganti dengan alamat email pengirim Anda
            'mail.From = New MailAddress("<EMAIL>")
            mail.From = New MailAddress("<EMAIL>") ' Ganti dengan alamat email pengirim Anda
            mail.To.Add(toEmail)
            mail.Subject = subject
            mail.Body = body
            mail.IsBodyHtml = True ' Atur ke True jika body email dalam format HTML, False jika plain text

            ' Menambahkan lampiran pertama jika tersedia
            ' Initialize MemoryStreams outside of Using blocks
            Dim ms1 As MemoryStream = Nothing
            Dim ms2 As MemoryStream = Nothing
            Try
                If attachmentBytes1 IsNot Nothing AndAlso attachmentBytes1.Length > 0 Then
                    ms1 = New MemoryStream(attachmentBytes1)
                    Dim attachment1 As New Attachment(ms1, attachmentName1, "application/pdf")
                    mail.Attachments.Add(attachment1)
                End If

                If attachmentBytes2 IsNot Nothing AndAlso attachmentBytes2.Length > 0 Then
                    ms2 = New MemoryStream(attachmentBytes2)
                    Dim attachment2 As New Attachment(ms2, attachmentName2, "application/pdf")
                    mail.Attachments.Add(attachment2)
                End If

                ' Send email
                'Using smtp As New SmtpClient("mail.beemoist.com")
                '    smtp.Port = 587 '465
                '    smtp.EnableSsl = True
                '    smtp.Credentials = New System.Net.NetworkCredential("<EMAIL>", "g02496011")
                '    smtp.Send(mail)
                'End Using

                Using smtp As New SmtpClient("mail.cargloss.co.id")
                    smtp.Port = 587
                    smtp.EnableSsl = False
                    smtp.Credentials = New System.Net.NetworkCredential("<EMAIL>", "j!MeQ},;@#GL")
                    smtp.Send(mail)
                End Using

            Finally
                ' Ensure MemoryStreams are disposed after the email is sent
                If ms1 IsNot Nothing Then ms1.Dispose()
                If ms2 IsNot Nothing Then ms2.Dispose()
            End Try

        End Using
    End Sub
    Public Shared Function ReadExcelFile(fileSavePath As String) As DataTable
        Dim dt As New DataTable
        Try
            Using stream As FileStream = File.Open(fileSavePath, FileMode.Open, FileAccess.Read)
                ' Auto-detect format, supports:
                '  - Binary Excel files (2.0-2003 format; *.xls)
                '  - OpenXml Excel files (2007 format; *.xlsx)
                Using reader As IExcelDataReader = ExcelReaderFactory.CreateReader(stream)
                    ' Choose one of either 1 or 2:
                    '
                    ' 1. Use the reader methods
                    ' Do while the reader reads
                    ' reader.Read()
                    ' Loop
                    ' Dim result As DataSet = reader.AsDataSet()
                    ' The result of each spreadsheet is in result.Tables
                    '
                    ' 2. Use the AsDataSet extension method
                    Dim result As DataSet = reader.AsDataSet()
                    dt = result.Tables(0)
                End Using
            End Using
        Catch ex As Exception
            Throw ex
        End Try
        Return dt
    End Function
    Public Shared Function ReadExcelFile(fileSavePath As String, sheetIndex As Integer) As DataTable
        Dim dt As New DataTable
        Try
            Using stream As FileStream = File.Open(fileSavePath, FileMode.Open, FileAccess.Read)
                ' Auto-detect format, supports:
                '  - Binary Excel files (2.0-2003 format; *.xls)
                '  - OpenXml Excel files (2007 format; *.xlsx)
                Using reader As IExcelDataReader = ExcelReaderFactory.CreateReader(stream)
                    ' Choose one of either 1 or 2:
                    '
                    ' 1. Use the reader methods
                    ' Do while the reader reads
                    ' reader.Read()
                    ' Loop
                    ' Dim result As DataSet = reader.AsDataSet()
                    ' The result of each spreadsheet is in result.Tables
                    '
                    ' 2. Use the AsDataSet extension method
                    Dim result As DataSet = reader.AsDataSet()
                    dt = result.Tables(sheetIndex)
                End Using
            End Using
        Catch ex As Exception
            Throw ex
        End Try
        Return dt
    End Function


End Class
Public Enum TipeEnumFormLayout
    SetIsValidValue
    SetValueToDomain
    SetDisableForm
    SetEnableForm
End Enum