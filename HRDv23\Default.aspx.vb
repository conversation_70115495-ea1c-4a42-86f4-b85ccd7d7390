﻿Imports DevExpress.DashboardCommon
Imports DevExpress.DashboardWeb
Imports DevExpress.DataAccess.EntityFramework
Imports HRD.Domain
Imports HRD.Helpers
Public Class _Default
    Inherits PageBase

    Public Sub New()
        MyBase.New("1")
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            If AuthHelper.GetLoggedInUserInfo Is Nothing Then
                Me.ucDashboards.Visible = False
            Else
                If Not AuthHelper.IsAuthenticated Then
                    Response.Redirect("~/Login.aspx")
                Else
                    Me.ucDashboards.Visible = True
                End If
            End If

            'Using ctx As New HRDEntities
            '    Dim newDN = ctx.tm_karyawan_datadinas.Where(Function(f) f.Pd_GajiPokok = 0).ToList

            '    Dim bSave As Boolean = False

            '    For Each dn In newDN
            '        Dim dnCount = ctx.tm_karyawan_datadinas.Where(Function(f) f.Karyawan_id = dn.<PERSON><PERSON><PERSON>_id).Count
            '        If dnCount >= 2 Then
            '            Dim oldDN = ctx.tm_karyawan_datadinas.Where(Function(f) f.Pd_GajiPokok > 0).OrderByDescending(Function(x) x.Id).Take(1).FirstOrDefault
            '            If oldDN IsNot Nothing Then
            '                bSave = True
            '                With dn
            '                    .Pd_GajiPokok = oldDN.Pd_GajiPokok
            '                    .Pd_T_Jabatan = oldDN.Pd_T_Jabatan
            '                    .Pd_T_Makan = oldDN.Pd_T_Makan
            '                    .Pd_T_PremiHadir = oldDN.Pd_T_PremiHadir
            '                    .Pd_T_Susu = oldDN.Pd_T_Susu
            '                    .Pd_T_Transport = oldDN.Pd_T_Transport
            '                    .Pd_T_Kontrak = oldDN.Pd_T_Kontrak
            '                    .Pd_T_Probation = oldDN.Pd_T_Probation
            '                    .Pd_T_PremiAss = oldDN.Pd_T_PremiAss
            '                    .Pd_T_Pajak = oldDN.Pd_T_Pajak
            '                    .Pd_T_JKK = oldDN.Pd_T_JKK
            '                    .Pd_T_JKM = oldDN.Pd_T_JKM
            '                    .Pd_T_JHT = oldDN.Pd_T_JHT
            '                    .Pd_T_JPK = oldDN.Pd_T_JPK
            '                    .Pd_T_JP = oldDN.Pd_T_JP
            '                    .TotalPendapatan = oldDN.TotalPendapatan
            '                    .Pt_P_JKK = oldDN.Pt_P_JKK
            '                    .Pt_P_JKM = oldDN.Pt_P_JKM
            '                    .Pt_P_JHT = oldDN.Pt_P_JHT
            '                    .Pt_P_JPK = oldDN.Pt_P_JPK
            '                    .Pt_P_JP = oldDN.Pt_P_JP
            '                    .Pt_K_JHT = oldDN.Pt_K_JHT
            '                    .Pt_K_JPK = oldDN.Pt_K_JPK
            '                    .Pt_K_JP = oldDN.Pt_K_JP
            '                    .Pt_K_JPK_Mandiri = oldDN.Pt_K_JPK_Mandiri
            '                    .Pt_PPH21 = oldDN.Pt_PPH21
            '                    .Pt_SP = oldDN.Pt_SP
            '                    .Pt_SerPekerja = oldDN.Pt_SerPekerja
            '                    .TotalPotongan = oldDN.TotalPotongan
            '                    .THP = oldDN.THP
            '                    .SaldoAwalCuti = oldDN.SaldoAwalCuti
            '                End With

            '                ctx.Entry(dn).State = Entity.EntityState.Modified


            '            End If
            '        End If
            '    Next

            '    If bSave Then
            '        ctx.SaveChanges()
            '    End If
            'End Using

        End If
    End Sub

End Class