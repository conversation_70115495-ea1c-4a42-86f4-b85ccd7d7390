﻿Imports HRD.Application
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Public Class TunjanganOnetimeController
    Inherits Controller(Of tr_tunjangan_onetime)
    Implements ITunjanganOnetimeListController, ITunjanganOnetimeEditController

    Public ReadOnly Property DataList(cabang_id As String, bPosted As Boolean) As IQueryable(Of tr_tunjangan_onetime) Implements ITunjanganOnetimeListController.DataList
        Get

            Dim Serv = ObjectFactory.GetInstance(Of TunjanganOnetimeService)()
            Dim r = Serv.GetTunjanganOnetimesAsync.GetAwaiter.GetResult

            If r.Success Then
                Dim os As IQueryable(Of tr_tunjangan_onetime) = r.Output
                os = os.Where(Function(x) x.Posted = bPosted And x.Cabang_id = cabang_id)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If

        End Get
    End Property

    Public Overrides Sub AddNewItem()

        Action = Action.AddNew
        SelectedItem = New tr_tunjangan_onetime With {.PeriodeGaji = Now}

    End Sub

    Public Overrides Sub SelectItem(id As String)

        Dim Serv = ObjectFactory.GetInstance(Of TunjanganOnetimeService)()
        Dim r = Serv.GetTunjanganOnetimeByIdAsync(id).GetAwaiter.GetResult

        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)

        Dim Serv = ObjectFactory.GetInstance(Of TunjanganOnetimeService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult

        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()


    End Sub

    Public Overrides Sub Saving(TEntity As tr_tunjangan_onetime)

        'ExpressMapper.Mapper.Register(Of tr_tunjangan_onetime, tr_tunjangan_onetime)() _
        '    .Ignore(Function(x) x.tm_area) _
        '    .Ignore(Function(x) x.tm_cabang) _
        '    .Ignore(Function(x) x.tm_karyawan)


        ExpressMapper.Mapper.Register(Of tr_tunjangan_onetime, tr_tunjangan_onetime)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_karyawan)

        Dim o = ExpressMapper.Mapper.Map(Of tr_tunjangan_onetime, tr_tunjangan_onetime)(TEntity)
        Dim Serv = ObjectFactory.GetInstance(Of TunjanganOnetimeService)()

        If o.Id <= 0 Then
            o.Tahun = Format(o.PeriodeGaji, "yyyy")
            o.Bulan = Format(o.PeriodeGaji, "MM")
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult

        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub
End Class
Public Interface ITunjanganOnetimeListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(cabang_id As String, bPosted As Boolean) As IQueryable(Of tr_tunjangan_onetime)
End Interface
Public Interface ITunjanganOnetimeEditController
    Inherits IControllerMain, IControllerEdit(Of tr_tunjangan_onetime)

End Interface