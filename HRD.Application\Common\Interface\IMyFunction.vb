Imports HRD.Domain

Public Interface IMyFunction
    Function GetPPH21(totalPendapatan As Decimal, id_kar As String, zakat As Decimal) As Decimal
    Function tm_area(id As String) As Domain.tm_area
    Function GetKodeNIK(o As tm_karyawan) As String
    'Function GetPotonganPinjaman(id_kar As String) As Decimal
    Function CalculateWeekdays(cabang_id As String, startDate As Date, endDate As Date, ByRef allDay As Integer) As Integer
    Function GetAbsensis(karyawan_id As String, startDate As Date, endDate As Date) As IQueryable(Of tr_absensi)
    Function CekSPKLembur(tgl As Date, kar_id As String, ByRef ket As String, ByRef jamMasuk As DateTime, ByRef jamKeluar As DateTime) As tr_spk_lembur_line
    Function tm_absensi_setting(cabang_id As String) As tm_absensi_setting
    Function tm_absensi_setting(cabang_id As String, shift As String) As tm_absensi_setting
    Function tm_absensi_setting_by_date(cabang_id As String, tanggal As Date, Optional shift As Integer = 0) As tm_absensi_setting
    Function GetPotonganPinjaman(id_kar As String, tgl As Date) As Decimal
    Function GetSettingAbsensi_OFF(kar_id As String, tgl As Date) As (bOff As Boolean, dMSmasuk As DateTime, dMSkeluar As DateTime, sKeterangan As String)
    Function CekHariLiburOFF(kar_id As String, tgl As Date, ByRef ket As String) As Boolean
    Function tm_cabang(id As String) As tm_cabang
    Function PPh21CalculatorBulanan(totalPendapatanBrutoBulanan As Decimal, kode_statusPTKP As String) As (ter_pajak_id As String, tarifPercent As Decimal, nilaiPajak As Decimal)
    Function PPh21CalculatorTahunan(totalPendapatanBrutoTahunan As Decimal, zakat As Decimal, kode_statusPTKP As String) As Decimal
    Function CalculateAssuransi(gajiPokok As Decimal, id_kar As String, cabang_id As String) As Object
    Function UpdateAbsensiDataLembur(absensi As tr_absensi, oKar As tm_karyawan) As Boolean
    Function CekHariLibur(area_id As String, kar As tm_karyawan, tgl As Date, ByRef ket As String, ByRef Optional cutiBersama As Boolean = Nothing) As Boolean
    Function GetJumlahHariLiburNasionals(cabang_id As String, startDate As Date, endDate As Date) As Integer
    Function CekHariJumat(tgl As Date) As Boolean
    Function tm_libur_nasional(tgl As Date) As tm_libur_nasional
    Function GetTotalPendapatanTahunan(kar_id As String, tahun As Integer) As Decimal
    Function GetTotalPph21Tahunan(kar_id As String, tahun As Integer) As Decimal
    Function CekHariSabtu(tgl As Date) As Boolean
    Function CekHariMinggu(tgl As Date) As Boolean
    Function tm_karyawan(id As Integer) As tm_karyawan

End Interface
