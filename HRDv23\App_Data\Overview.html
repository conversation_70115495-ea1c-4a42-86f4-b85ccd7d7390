﻿<h1 id="ResponsiveWebApplication" class="title">Responsive Web Application</h1>
<p>
  The <strong>Responsive Web Application</strong>  template includes several adaptive web pages based on DevExpress ASP.NET AJAX controls or MVC extensions. You can use the layout elements from this template to create an adaptive layout in your application.
</p>

<p>
  Refer to the <a href="http://help.devexpress.com/#AspNet/CustomDocument120713">Getting Started with Responsive Web Application Template</a> article for more information about the template
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/demo.png" alt="DevExpress Responsive Web Application Project Template" />
</p>

<p>
  The template provides the following standard solutions frequently used to build adaptive layouts:
</p>

<ul>
  <li>
    Navigation bar
  </li>
  <li>
    Side menus
  </li>
  <li>
    Sticky toolbar
  </li>
  <li>
    Sticky footer
  </li>
</ul>

<h2 class="category" id="navbar">Navigation Bar</h2>

<p>
  A navigation bar (navbar) is a fixed-height toolbar at the top of a page. This template incorporates a "sticky" navbar - this element "sticks" to the top of the page and does not move when end users scroll the page.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/menu.gif" alt="" />
</p>

<p>
  The navbar contains the following elements:
</p>

<ul>
  <li>
    A hamburger button - collapses/expands a left sidebar
  </li>
  <li>
    An image with the company's logo and title. Only the logo is displayed on mobile devices
  </li>
  <li>
    A responsive menu that transforms into the button on devices with the narrow screen. When this button is clicked, menu items are displayed in a popup window.
  </li>
  <li>
    A sign in button that displays a popup window when clicked. The window displays the Sign In/Register buttons or a user's profile
  </li>
  <li>
    An additional hamburger button that collapses/expands a right sidebar (only for the <b>GridView</b> page) when clicked
  </li>
</ul>


<h2 class="category" id="sidebar">Sidebars</h2>

<p>
  A sidebar is a collapsible panel that displays additional UI elements on the page's left or right side. In this template, sidebars are automatically collapsed. End users should click the hamburger button to invoke a sidebar on a mobile device.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/sidebar.gif" alt="Adaptive Sidebars" />
</p>

<p>
  The <b>Home</b>, <b>GridView</b>, <b>Scheduler</b>, and <b>Article</b> pages include the left sidebar. The <b>GridView</b> page also includes the right sidebar.
</p>


<h2 class="category" id="toolbar">Adaptive Toolbar</h2>

<p>
  The <b>GridView</b> and <b>Scheduler</b> pages include a toolbar under the navbar. Use this toolbar as a container for UI elements that should be attached to the top of the page.
</p>

<p>
  The toolbar provides two adaptive modes:
</p>

<ul>
  <li>
    the menu hides items' text and displays only icons
  </li>
  <li>
    the menu hides its items one by one to the popup menu hidden under the ellipse button when the browser width is changed.
  </li>
</ul>

<p>
  <img class="img-responsive" src="Content/Images/Overview/toolbar.gif" alt="Toolbar" />
</p>

<p>
  The toolbar implemented for the <b>GridView</b> page also incorporates a collapsible panel that can be used to implement filtering or searching functionality.
</p>

<h2 class="category" id="stickyFooter">Sticky Footer</h2>

<p>
  A fixed-height footer pinned to the bottom of the browser window.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/sticky-footer.gif" alt="Sticky Footer" />
</p>


<h1 class="title" id="content">Content Pages</h1>

<p>
  The <b>Responsive Web Application</b> template contains four content pages. These pages provide adaptive layouts that you can use to implement the following scenarios: viewing and editing data tables, managing a schedule, administering user accounts or displaying formatted text.
</p>


<h2 class="category" id="grid">Grid View</h2>

<p>
  This page demonstrates an adaptive layout that incorporates the ASPxGridView control. The page’s layout includes the following features:
</p>

<ul>
  <li>Vertical and horizontal touch scrolling</li>
  <li>Data pagination and the control’s built-in pager</li>
  <li>A left sidebar that incorporates a filtering interface</li>
  <li>A search panel stored in the toolbar</li>
  <li>An edit form implemented as an adaptive modal window</li>
</ul>

<p>
  <img class="img-responsive" src="Content/Images/Overview/grid.png" alt="Adaptive Grid View" />
</p>


<h2 class="category" id="scheduler">Scheduler</h2>

<p>
  The <strong>Scheduler</strong> page contains an adaptive layout that incorporates the ASPxScheduler control. This page’s layout includes the following features:
</p>

<ul>
  <li>
    A top fixed toolbar that stores filtering and exporting UI
  </li>
  <li>
    A built-in adaptive toolbar that allows end-users to switch dates and views
  </li>
  <li>
    A floating action button that provides fast access to event management
  </li>
  <li>
    A left sidebar that incorporates a calendar (implemented using the <strong>ASPxDateNavigator</strong>) and the resource filtering interface
  </li>
</ul>


<p>
  <img class="img-responsive" src="Content/Images/Overview/scheduler.png" alt="Scheduler" />
</p>


<h2 class="category" id="article">Article</h2>

<p>
  The <strong>Article</strong> page demonstrates an adaptive reader for formatted texts. The page's layout is built using CSS styles and adapted for the browser's window size: margins and images are reduced for small screens. CSS styles applied to the Article page are stored in the separate file that you can use in your applications.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/article.png" alt="Adaptive Article" />
</p>


<h2 class="category" id="signin">Sign In/Register</h2>

<p>
  The template also includes sign in/register UI.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/login.gif" alt="Sign In/Register UI" />
</p>

<p>
  Authorized users can access their profiles by clicking on his/her photo in the top right corner. If a user's photo is unavailable, the profile icon displays a user's initials.
</p>

<p>
  <img class="img-responsive" src="Content/Images/Overview/login.png" alt="Sign In/Register UI" />
</p>