﻿Imports System.Linq.Expressions
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Public Class PinjamanPermisionSpec
    Inherits CompositeSpecification(Of tr_register_pinjaman)

    Private ReadOnly _loggedInUser As ApplicationUser
    Private ReadOnly _status As String
    Private ReadOnly _posted As Boolean
    Public Sub New(bPosted As Boolean, sStatus As String)

        _loggedInUser = AuthHelper.GetLoggedInUserInfo
        _status = sStatus
        _posted = bPosted

    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of tr_register_pinjaman, Boolean))
        Get
            Select Case _loggedInUser.PermisionArea
                Case 0
                    Return Function(f) f.Cabang_id = _loggedInUser.Cabang_id And f.Posted = _posted And f.Status = _status
                    'Case 1
                    '    Return Function(f) f.Area_id = _loggedInUser.Area_id And f.Posted = _posted And f.Status = _status
                Case Else
                    Return Function(f) f.Posted = _posted And f.Status = _status
            End Select
        End Get
    End Property

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of tr_register_pinjaman)) As IQueryable(Of tr_register_pinjaman)
        If Criteria Is Nothing Then
            Return query
        End If
        Return query.Where(Criteria)
    End Function
End Class