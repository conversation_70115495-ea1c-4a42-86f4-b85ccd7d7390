﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmImportAbsen.aspx.vb" Inherits="HRDv23.frmImportAbsen" %>

<%@ Register Src="~/Views/HRDandPayroll/Transaksi/ucImportAbsensi_list.ascx" TagPrefix="uc1" TagName="ucImportAbsensi_list" %>

<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" Width="100%" ClientInstanceName="cp_importabsensi">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal runat="server" ID="ltl_msg"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" AlignItemCaptionsInAllGroups="True">
                    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
                    </SettingsAdaptivity>
                    <Items>
                        <dx:LayoutItem ColSpan="1" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucImportAbsensi_list runat="server" id="ucImportAbsensi_list" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
