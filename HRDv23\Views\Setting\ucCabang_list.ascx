﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucCabang_list.ascx.vb" Inherits="HRDv23.ucCabang_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id">
    <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_cabang.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_cabang.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_cabang.PerformCallback('new'); 
break;
}

}" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells">
    </SettingsAdaptivity>
    <SettingsPager AlwaysShowPager="True">
        <AllButton Visible="True">
        </AllButton>
        <PageSizeItemSettings Visible="True">
        </PageSizeItemSettings>
    </SettingsPager>
    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
    <SettingsBehavior AllowFocusedRow="True" />
<SettingsPopup>
<FilterControl AutoUpdatePosition="False"></FilterControl>
</SettingsPopup>
    <SettingsSearchPanel Visible="True" />
    <SettingsExport EnableClientSideExportAPI="True">
    </SettingsExport>
    <Columns>
        <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_area.NamaArea" VisibleIndex="1" Caption="Perusahaan">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="KodeCabang" VisibleIndex="2">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="NamaCabang" VisibleIndex="3">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataCheckColumn FieldName="IsHeadOffice" VisibleIndex="4">
        </dx:GridViewDataCheckColumn>
        <dx:GridViewDataTextColumn FieldName="Alamat" VisibleIndex="5">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Telp" VisibleIndex="6">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Fax" VisibleIndex="7">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataCheckColumn FieldName="SabtuLibur" VisibleIndex="8">
        </dx:GridViewDataCheckColumn>
        <dx:GridViewDataCheckColumn FieldName="MingguLibur" VisibleIndex="9">
        </dx:GridViewDataCheckColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem Name="btn_new" Text="New">
                    <Image IconID="actions_new_svg_16x16">
                    </Image>
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ShowFilterRow">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                </dx:GridViewToolbarItem>
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
</dx:ASPxGridView>
<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_cabang">
</dx:EntityServerModeDataSource>

