﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class SPKLemburController
    Inherits Controller(Of tr_spk_lembur)
    Implements ISPKLemburListController, ISPKLemburEditController

    Public ReadOnly Property DataList(bPosted As Boolean, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_spk_lembur) Implements ISPKLemburListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then

                Dim os As IQueryable(Of tr_spk_lembur) = r.Output
                os = os.Where(Function(f) f.Posted = bPosted)
                If area_id <> Nothing Then
                    os = os.Where(Function(f) f.Area_id = area_id)
                End If
                If cabang_id <> Nothing Then
                    os = os.Where(Function(f) f.Cabang_id = cabang_id)
                End If

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_spk_lembur With {.Area_id = _user.Area_id, .Tanggal = Now}
    End Sub
    Public Overloads Sub AddNewItem(area_id As String, cabang_id As String)
        Action = Action.AddNew
        SelectedItem = New tr_spk_lembur With {.Area_id = area_id, .Tanggal = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_spk_lembur)


        ExpressMapper.Mapper.Register(Of tr_spk_lembur, tr_spk_lembur)() _
        .Ignore(Function(i) i.tm_area) _
        .Ignore(Function(i) i.tm_cabang) _
        .Ignore(Function(i) i.tr_spk_lembur_line)

        Dim o = ExpressMapper.Mapper.Map(Of tr_spk_lembur, tr_spk_lembur)(TEntity)

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()

        o.StartTime = $"{Format(o.Tanggal, "yyyy-MM-dd ")}{Format(o.StartTime, "HH:mm")}"
        o.EndTime = $"{Format(o.Tanggal, "yyyy-MM-dd ")}{Format(o.EndTime, "HH:mm")}"

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o, TEntity.tr_spk_lembur_line.ToList).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
    Public Sub SaveImport(dt As DataTable) Implements ISPKLemburListController.SaveImport
        Dim spkDictionary As New Dictionary(Of String, tr_spk_lembur) ' Dictionary untuk menyimpan SPK berdasarkan kunci
        Dim rows As DataRowCollection = dt.Rows
        Dim i As Integer = 0

        For Each row As DataRow In rows
            i += 1
            If i = 1 Then
                Continue For ' Lewati header
            End If

            ' Handle DBNull safely
            Dim nik As String = If(IsDBNull(row(5)), String.Empty, row(5).ToString())
            Dim tanggal As Date
            If Not Date.TryParse(If(IsDBNull(row(0)), String.Empty, row(0).ToString()), tanggal) Then
                Continue For ' Skip invalid dates
            End If

            Dim kar = myFunction.tm_karyawanByNIK(nik)
            If kar IsNot Nothing Then
                Dim spkKey As String = $"{tanggal}_{kar.Cabang_id}" ' Kunci berdasarkan tanggal dan cabang_id
                Dim spkLembur As tr_spk_lembur

                ' Cek apakah SPK sudah ada
                If Not spkDictionary.TryGetValue(spkKey, spkLembur) Then
                    ' Jika tidak ada, buat SPK baru
                    spkLembur = New tr_spk_lembur With {
                    .Tanggal = tanggal,
                    .StartTime = If(IsDBNull(row(1)), Nothing, row(1)),
                    .EndTime = If(IsDBNull(row(2)), Nothing, row(2)),
                    .PotJamIstirahat = If(IsDBNull(row(3)), False, row(3).ToString() = "1"),
                    .Keterangan = If(IsDBNull(row(4)), String.Empty, row(4).ToString()),
                    .Area_id = kar.Area_id,
                    .Cabang_id = kar.Cabang_id
                }
                    spkDictionary(spkKey) = spkLembur ' Simpan SPK ke dictionary
                End If

                ' Tambahkan line untuk karyawan
                Dim spkline As New tr_spk_lembur_line With {
                .Karyawan_id = kar.Id
            }
                spkLembur.tr_spk_lembur_line.Add(spkline) ' Tambahkan line ke SPK yang sesuai
            End If
        Next

        ' Simpan semua SPK yang telah dibuat
        For Each spk In spkDictionary.Values
            Saving(spk) ' Asumsikan ada metode untuk menyimpan objek SPK
        Next
    End Sub


    'Public Sub SaveImport(dt As DataTable) Implements ISPKLemburListController.SaveImport
    '    Dim spkDictionary As New Dictionary(Of String, tr_spk_lembur) ' Dictionary untuk menyimpan SPK berdasarkan kunci
    '    Dim rows As DataRowCollection = dt.Rows
    '    Dim i As Integer = 0

    '    For Each row As DataRow In rows
    '        i += 1
    '        If i = 1 Then
    '            Continue For ' Lewati header
    '        End If

    '        Dim nik As String = row(5).ToString()
    '        Dim kar = myFunction.tm_karyawanByNIK(nik)
    '        Dim tanggal As Date = row(0).ToString()
    '        If kar IsNot Nothing Then
    '            Dim spkKey As String = $"{tanggal}_{kar.Cabang_id}" ' Kunci berdasarkan tanggal dan cabang_id
    '            Dim spkLembur As tr_spk_lembur

    '            ' Cek apakah SPK sudah ada
    '            If Not spkDictionary.TryGetValue(spkKey, spkLembur) Then
    '                ' Jika tidak ada, buat SPK baru
    '                spkLembur = New tr_spk_lembur With {
    '                .Tanggal = row(0),
    '                .StartTime = row(1),
    '                .EndTime = row(2),
    '                .PotJamIstirahat = row(3).ToString() = "1",
    '                .Keterangan = row(4),
    '                .Area_id = kar.Area_id,
    '                .Cabang_id = kar.Cabang_id
    '            }
    '                spkDictionary(spkKey) = spkLembur ' Simpan SPK ke dictionary
    '            End If

    '            ' Tambahkan line untuk karyawan
    '            Dim spkline As New tr_spk_lembur_line With {
    '            .Karyawan_id = kar.Id
    '        }
    '            spkLembur.tr_spk_lembur_line.Add(spkline) ' Tambahkan line ke SPK yang sesuai
    '        End If
    '    Next

    '    ' Simpan semua SPK yang telah dibuat
    '    For Each spk In spkDictionary.Values
    '        Saving(spk) ' Asumsikan ada metode untuk menyimpan objek SPK
    '    Next
    'End Sub

    Public Function Posting(TEntity As tr_spk_lembur) As Boolean Implements ISPKLemburEditController.Posting

        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.PostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Function

    Public Function UnPosting(TEntity As tr_spk_lembur) As Boolean Implements ISPKLemburEditController.UnPosting

        'TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.UnPostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Function
End Class
Public Interface ISPKLemburListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bPosted As Boolean, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_spk_lembur)
    Sub SaveImport(dt As DataTable)
End Interface
Public Interface ISPKLemburEditController
    Inherits IControllerMain, IControllerEdit(Of tr_spk_lembur)

    Function Posting(TEntity As tr_spk_lembur) As Boolean
    Function UnPosting(TEntity As tr_spk_lembur) As Boolean
End Interface