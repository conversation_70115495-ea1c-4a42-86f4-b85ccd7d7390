Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class SPKLemburController
    Inherits Controller(Of tr_spk_lembur)
    Implements ISPKLemburListController, ISPKLemburEditController

    Public ReadOnly Property DataList(bPosted As Boolean, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_spk_lembur) Implements ISPKLemburListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then

                Dim os As IQueryable(Of tr_spk_lembur) = r.Output
                os = os.Where(Function(f) f.Posted = bPosted)
                If area_id <> Nothing Then
                    os = os.Where(Function(f) f.Area_id = area_id)
                End If
                If cabang_id <> Nothing Then
                    os = os.Where(Function(f) f.Cabang_id = cabang_id)
                End If

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_spk_lembur With {.Area_id = _user.Area_id, .Tanggal = Now}
    End Sub
    Public Overloads Sub AddNewItem(area_id As String, cabang_id As String)
        Action = Action.AddNew
        SelectedItem = New tr_spk_lembur With {.Area_id = area_id, .Tanggal = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_spk_lembur)


        ExpressMapper.Mapper.Register(Of tr_spk_lembur, tr_spk_lembur)() _
        .Ignore(Function(i) i.tm_area) _
        .Ignore(Function(i) i.tm_cabang) _
        .Ignore(Function(i) i.tr_spk_lembur_line)

        Dim o = ExpressMapper.Mapper.Map(Of tr_spk_lembur, tr_spk_lembur)(TEntity)

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()        ' Ensure Tanggal is not Nothing
        If o.Tanggal = Nothing Then
            sMsg = "Tanggal SPK harus diisi."
            Saved = False
            Return
        End If

        ' TotalJam is now directly entered by the user

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o, TEntity.tr_spk_lembur_line.ToList).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
    Public Sub SaveImport(dt As DataTable) Implements ISPKLemburListController.SaveImport
        Dim spkDictionary As New Dictionary(Of String, tr_spk_lembur) ' Dictionary untuk menyimpan SPK berdasarkan kunci
        Dim rows As DataRowCollection = dt.Rows
        Dim i As Integer = 0

        For Each row As DataRow In rows
            i += 1
            If i = 1 Then
                Continue For ' Lewati header
            End If            ' Handle DBNull safely
            Dim nik As String = If(IsDBNull(row(3)), String.Empty, row(3).ToString())
            Dim tanggal As Date
            If Not Date.TryParse(If(IsDBNull(row(0)), String.Empty, row(0).ToString()), tanggal) Then
                Continue For ' Skip invalid dates
            End If

            Dim kar = myFunction.tm_karyawanByNIK(nik)
            If kar IsNot Nothing Then
                Dim spkKey As String = $"{tanggal}_{kar.Cabang_id}" ' Kunci berdasarkan tanggal dan cabang_id
                Dim spkLembur As tr_spk_lembur

                ' Cek apakah SPK sudah ada
                If Not spkDictionary.TryGetValue(spkKey, spkLembur) Then                    ' Jika tidak ada, buat SPK baru
                    spkLembur = New tr_spk_lembur With {
                    .Tanggal = tanggal,
                    .TotalJam = If(IsDBNull(row(1)), 0D, Convert.ToDecimal(row(1))),
                    .Keterangan = If(IsDBNull(row(2)), String.Empty, row(2).ToString()),
                    .Area_id = kar.Area_id,
                    .Cabang_id = kar.Cabang_id
                }
                    spkDictionary(spkKey) = spkLembur ' Simpan SPK ke dictionary
                End If

                ' Tambahkan line untuk karyawan
                Dim spkline As New tr_spk_lembur_line With {
    .Karyawan_id = kar.Id
}
                spkLembur.tr_spk_lembur_line.Add(spkline) ' Tambahkan line ke SPK yang sesuai
            End If
        Next

        ' Simpan semua SPK yang telah dibuat
        For Each spk In spkDictionary.Values
            Saving(spk) ' Asumsikan ada metode untuk menyimpan objek SPK
        Next
    End Sub
    Public Sub PostingSelectedItems(selectedIds() As String)
        Try
            Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
            Dim successCount As Integer = 0
            Dim failCount As Integer = 0
            Dim batchSize As Integer = 50 ' Process 50 records at a time to avoid timeouts
            Dim totalRecords As Integer = selectedIds.Length
            Dim currentUserName As String = AuthHelper.GetLoggedInUserInfo.UserName

            ' Process in batches to avoid timeouts
            For batchStart As Integer = 0 To totalRecords - 1 Step batchSize
                ' Calculate the size of this batch (might be smaller for the last batch)
                Dim currentBatchSize As Integer = Math.Min(batchSize, totalRecords - batchStart)
                Dim batchIds As New List(Of Integer)

                ' Parse and collect valid IDs for this batch
                For i As Integer = batchStart To batchStart + currentBatchSize - 1
                    Dim id As Integer
                    If Integer.TryParse(selectedIds(i), id) Then
                        batchIds.Add(id)
                    Else
                        failCount += 1 ' Count invalid IDs as failures
                    End If
                Next

                ' Process this batch of IDs if there are any valid ones
                If batchIds.Count > 0 Then
                    ' Use bulk processing if available (for better performance)
                    Dim result = Serv.ProcessBatchPostingAsync(batchIds.ToArray(), currentUserName).GetAwaiter.GetResult

                    If result.Success Then
                        ' If bulk processing isn't available, the service will handle items one by one
                        ' The result contains the count of success and failures
                        If TypeOf result.Output Is Dictionary(Of String, Integer) Then
                            Dim counts = DirectCast(result.Output, Dictionary(Of String, Integer))
                            If counts.ContainsKey("success") Then
                                successCount += counts("success")
                            End If
                            If counts.ContainsKey("fail") Then
                                failCount += counts("fail")
                            End If
                        Else
                            ' Default behavior if the output format isn't as expected
                            successCount += batchIds.Count
                        End If
                    Else
                        ' If bulk processing failed, fall back to individual processing
                        For Each id As Integer In batchIds
                            Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
                            If r.Success Then
                                Dim entity = r.Output
                                entity.PostedBy = currentUserName

                                Dim postResult = Serv.PostingAsync(entity).GetAwaiter.GetResult
                                If postResult.Success Then
                                    successCount += 1
                                Else
                                    failCount += 1
                                End If
                            Else
                                failCount += 1
                            End If
                        Next
                    End If
                End If
            Next

            ' Set message based on results
            If failCount = 0 Then
                sMsg = $"Successfully released {successCount} SPK Lembur records."
                Saved = True
            Else
                sMsg = $"Released {successCount} SPK Lembur records. Failed to release {failCount} records."
                Saved = (successCount > 0)
            End If

        Catch ex As Exception
            sMsg = MessageFormatter.GetFormattedErrorMessage($"Error releasing SPK Lembur records: {ex.Message}")
            Saved = False
        End Try
    End Sub
    'Public Sub SaveImport(dt As DataTable) Implements ISPKLemburListController.SaveImport
    '    Dim spkDictionary As New Dictionary(Of String, tr_spk_lembur) ' Dictionary untuk menyimpan SPK berdasarkan kunci
    '    Dim rows As DataRowCollection = dt.Rows
    '    Dim i As Integer = 0

    '    For Each row As DataRow In rows
    '        i += 1
    '        If i = 1 Then
    '            Continue For ' Lewati header
    '        End If

    '        Dim nik As String = row(3).ToString()
    '        Dim kar = myFunction.tm_karyawanByNIK(nik)
    '        Dim tanggal As Date = row(0).ToString()
    '        If kar IsNot Nothing Then
    '            Dim spkKey As String = $"{tanggal}_{kar.Cabang_id}" ' Kunci berdasarkan tanggal dan cabang_id
    '            Dim spkLembur As tr_spk_lembur

    '            ' Cek apakah SPK sudah ada
    '            If Not spkDictionary.TryGetValue(spkKey, spkLembur) Then
    '                ' Jika tidak ada, buat SPK baru
    '                spkLembur = New tr_spk_lembur With {
    '                .Tanggal = row(0),
    '                .TotalJam = If(IsDBNull(row(1)), 0D, Convert.ToDecimal(row(1))),
    '                .Keterangan = row(2),
    '                .Area_id = kar.Area_id,
    '                .Cabang_id = kar.Cabang_id
    '            }
    '                spkDictionary(spkKey) = spkLembur ' Simpan SPK ke dictionary
    '            End If

    '            ' Tambahkan line untuk karyawan
    '            Dim spkline As New tr_spk_lembur_line With {
    '            .Karyawan_id = kar.Id
    '        }
    '            spkLembur.tr_spk_lembur_line.Add(spkline) ' Tambahkan line ke SPK yang sesuai
    '        End If
    '    Next

    '    ' Simpan semua SPK yang telah dibuat
    '    For Each spk In spkDictionary.Values
    '        Saving(spk) ' Asumsikan ada metode untuk menyimpan objek SPK
    '    Next
    'End Sub

    Public Function Posting(TEntity As tr_spk_lembur) As Boolean Implements ISPKLemburEditController.Posting

        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.PostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Function

    Public Function UnPosting(TEntity As tr_spk_lembur) As Boolean Implements ISPKLemburEditController.UnPosting

        'TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName

        Dim Serv = ObjectFactory.GetInstance(Of SPKLemburService)()
        Dim r = Serv.UnPostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Function
End Class
Public Interface ISPKLemburListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bPosted As Boolean, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_spk_lembur)
    Sub SaveImport(dt As DataTable)
End Interface
Public Interface ISPKLemburEditController
    Inherits IControllerMain, IControllerEdit(Of tr_spk_lembur)

    Function Posting(TEntity As tr_spk_lembur) As Boolean
    Function UnPosting(TEntity As tr_spk_lembur) As Boolean
End Interface