﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Public Class ucSettingAbsenHariKhusus_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ISettingAbsensiListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.QueryableSource = Controller.DataList(True).AsQueryable
    End Sub

End Class