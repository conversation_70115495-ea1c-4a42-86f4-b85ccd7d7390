﻿Imports System.Threading
Imports HRD.Application
Imports HRD.Domain


Public Class TmCabangQueryHandler


    Private ReadOnly _uow As IUnitOfWork

    Public Sub New(uow As IUnitOfWork)
        _uow = uow
    End Sub
    Public Async Function Handle(request As GetTmCabangQuery) As Task(Of tm_cabang)

        Dim cabang = _uow.Repository(Of tm_cabang).TableNoTracking.Where(Function(c) request.Id Is Nothing OrElse c.Id = request.Id).FirstOrDefault

        Return cabang

        'Return Await _context.tm_cabang
        '    .Include(Function(c) c.tm_area)
        '.Include(Function(c) c.tm_user)
        '.Include(Function(c) c.tm_karyawan)
        '.FirstOrDefaultAsync(Function(c) query.Id Is Nothing OrElse c.Id = query.Id, cancellationToken)

    End Function
    Public Async Function Handle(request As GetAllTmCabangQuery) As Task(Of IQueryable(Of tm_cabang))
        Dim cabangs = _uow.Repository(Of tm_cabang).TableNoTracking

        Return cabangs
    End Function
End Class
