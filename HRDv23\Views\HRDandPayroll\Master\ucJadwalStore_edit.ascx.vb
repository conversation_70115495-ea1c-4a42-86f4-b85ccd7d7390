﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web

Public Class ucJadwalStore_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IOffKaryawanEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If oSelectItem.tm_off_karyawan_line.Count > 0 Then
                cb_area.ClientEnabled = False
                cb_cabang.ClientEnabled = False
            End If

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_jadwal_store_release.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_jadwal_store_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_jadwal_store.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_jadwal_store.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction

        If Controller.Action = Action.Posting Then
            Controller.Saving(oSelectItem)
            Return
        End If

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tm_off_karyawan
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tm_off_karyawan)
        End Get
        Set(value As tm_off_karyawan)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

            Me.ASPxGridView1.DataSourceID = Nothing
        Else
            Me.ASPxGridView1.DataSourceID = "EntityServerModeDataSource1"
        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub EntityServerModeDataSource1_Selecting(sender As Object, e As DevExpress.Data.Linq.LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        If oSelectItem Is Nothing Then
            Return
        End If
        e.QueryableSource = oSelectItem.tm_off_karyawan_line.Where(Function(f) f.Deleting <> True).AsQueryable
    End Sub

    Protected Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs)
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Protected Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs)
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value, 1)
    End Sub

    Protected Sub cb_minggu_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs)
        MyMethod.ShiftKaryawan_ItemRequestedByValue(source, e)
    End Sub

    Protected Sub cb_minggu_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs)
        MyMethod.ShiftKaryawan_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub ASPxGridView1_RowInserting(sender As Object, e As ASPxDataInsertingEventArgs) Handles ASPxGridView1.RowInserting
        Dim o As New tm_off_karyawan_line
        With o
            .Karyawan_id = e.NewValues("Karyawan_id")
            .Senin = e.NewValues("Senin")
            .Selasa = e.NewValues("Selasa")
            .Rabu = e.NewValues("Rabu")
            .Kamis = e.NewValues("Kamis")
            .Jumat = e.NewValues("Jumat")
            .Sabtu = e.NewValues("Sabtu")
            .Minggu = e.NewValues("Minggu")

            oSelectItem.tm_off_karyawan_line.Add(o)
        End With

        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView1.RowUpdating
        Dim o = CType(oSelectItem.tm_off_karyawan_line.Where(Function(f) f.Deleting <> True And f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_off_karyawan_line)
        With o
            .Karyawan_id = e.NewValues("Karyawan_id")
            .Senin = e.NewValues("Senin")
            .Selasa = e.NewValues("Selasa")
            .Rabu = e.NewValues("Rabu")
            .Kamis = e.NewValues("Kamis")
            .Jumat = e.NewValues("Jumat")
            .Sabtu = e.NewValues("Sabtu")
            .Minggu = e.NewValues("Minggu")
        End With

        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowDeleting(sender As Object, e As ASPxDataDeletingEventArgs) Handles ASPxGridView1.RowDeleting
        Dim o = CType(oSelectItem.tm_off_karyawan_line.Where(Function(f) f.Deleting <> True And f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_off_karyawan_line)
        With o
            .Deleting = True
        End With

        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_DataBound(sender As Object, e As EventArgs) Handles ASPxGridView1.DataBound
        If oSelectItem Is Nothing Then
            Return
        End If
        Dim grd = CType(sender, ASPxGridView)

        grd.JSProperties("cpCount") = oSelectItem.tm_off_karyawan_line.Where(Function(f) f.Deleting <> True).Count
    End Sub

    Private Sub ASPxGridView1_RowValidating(sender As Object, e As ASPxDataValidationEventArgs) Handles ASPxGridView1.RowValidating
        Dim os = oSelectItem.tm_off_karyawan_line.Where(Function(f) f.Deleting <> True And f.RowGuid <> e.Keys("RowGuid"))
        os = os.Where(Function(f) f.Karyawan_id = e.NewValues("Karyawan_id"))
        Dim o = os.Take(1).FirstOrDefault
        If o IsNot Nothing Then
            e.RowError = "Karyawan ini sudah ada dalam daftar!"
        End If

    End Sub

    Private Sub ASPxGridView1_CommandButtonInitialize(sender As Object, e As ASPxGridViewCommandButtonEventArgs) Handles ASPxGridView1.CommandButtonInitialize
        If Controller Is Nothing Then
            Return
        End If
        Select Case e.ButtonType
            Case ColumnCommandButtonType.New, ColumnCommandButtonType.Delete, ColumnCommandButtonType.Edit
                e.Enabled = (oAction = Action.AddNew Or oAction = Action.Edit)
        End Select
    End Sub
End Class