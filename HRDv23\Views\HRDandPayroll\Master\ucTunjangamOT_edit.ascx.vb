﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web

Public Class ucTunjangamOT_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ITunjanganOnetimeEditController

    Private Const PageIdSessionKey As String = "_PageID"
    Private Const ActionSessionKeySuffix As String = "_Action"

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)

        Visible = Controller.SelectedItem IsNot Nothing

        If Not Visible Then Return

        oSelectItem = Controller.SelectedItem
        oAction = Controller.Action

        ASPxFormLayout1.DataSource = Controller.SelectedItem
        ASPxFormLayout1.DataBind()

        ConfigureFormBasedOnAction()

        'If Controller.SelectedItem IsNot Nothing Then

        '    Visible = True

        '    oSelectItem = Controller.SelectedItem
        '    oAction = Controller.Action



        '    ASPxFormLayout1.DataSource = Controller.SelectedItem
        '    ASPxFormLayout1.DataBind()

        '    If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

        '    Else
        '        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
        '    End If

        '    If Controller.Action = Action.Posting Then
        '        'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
        '        'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

        '        btn_save.Text = "Release"
        '    Else
        '        btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_tunjangan_ot.PerformCallback('save');s.SetEnabled(false);};}"
        '        btn_back.ClientSideEvents.Click = "function(s, e) {cp_tunjangan_ot.PerformCallback('back');}"

        '        If Controller.Action = Action.View Then
        '            btn_save.Visible = False
        '        End If
        '    End If


        'Else
        '    Visible = False
        'End If



    End Sub

    Private Sub ConfigureFormBasedOnAction()
        If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then
            ' Configuration for AddNew or Edit
        Else
            MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
        End If

        ConfigureButtonsBasedOnAction()
    End Sub
    Private Sub ConfigureButtonsBasedOnAction()
        If Controller.Action = Action.Posting Then
            btn_save.Text = "Release"
        Else
            btn_save.Visible = Controller.Action <> Action.View
            SetClientSideEvents("save", "back")
        End If
    End Sub
    Private Sub SetClientSideEvents(saveCallback As String, backCallback As String)
        btn_save.ClientSideEvents.Click = $"function(s, e) {{if (ASPxClientEdit.ValidateGroup(null) == true) {{cp_tunjangan_ot.PerformCallback('{saveCallback}');s.SetEnabled(false);}}}}"
        btn_back.ClientSideEvents.Click = $"function(s, e) {{cp_tunjangan_ot.PerformCallback('{backCallback}');}}"
    End Sub
    Private Function GetSessionKey(baseKey As String) As String
        Dim value = ViewState(baseKey)
        If value IsNot Nothing Then
            Return value.ToString() & baseKey
        Else
            ' Handle the null case or initialize with a default value
            ' For example, return a default string or initialize ViewState(baseKey)
            Return "DefaultValue" & baseKey
        End If
    End Function

    Sub Saving()
        Controller.Action = oAction


        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    'Property oSelectItem As tr_tunjangan_onetime
    '    Get
    '        Return CType(Session(ViewState("_PageID").ToString()), tr_tunjangan_onetime)
    '    End Get
    '    Set(value As tr_tunjangan_onetime)
    '        Session(ViewState("_PageID").ToString()) = value
    '    End Set
    'End Property
    Property oSelectItem As tr_tunjangan_onetime
        Get
            Return CType(Session(GetSessionKey(PageIdSessionKey)), tr_tunjangan_onetime)
        End Get
        Set(value As tr_tunjangan_onetime)
            Session(GetSessionKey(PageIdSessionKey)) = value
        End Set
    End Property

    'Private Property oAction As Action
    '    Get
    '        Return Session(ViewState("_PageID").ToString() & "_Action")
    '    End Get
    '    Set(value As Action)
    '        Session(ViewState("_PageID").ToString() & "_Action") = value
    '    End Set
    'End Property
    Private Property oAction As Action
        Get
            Return Session(GetSessionKey(PageIdSessionKey) & ActionSessionKeySuffix)
        End Get
        Set(value As Action)
            Session(GetSessionKey(PageIdSessionKey) & ActionSessionKeySuffix) = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'If Not IsPostBack Then
        '    ViewState("_PageID") = (New Random()).Next().ToString()

        'End If
        If Not IsPostBack Then
            ViewState(PageIdSessionKey) = (New Random()).Next().ToString()
        End If
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub
End Class