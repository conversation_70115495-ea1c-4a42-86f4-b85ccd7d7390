﻿Imports System.IO
Imports System.Threading.Tasks
Imports DevExpress.Web
Imports DevExpress.XtraReports.UI
Imports HRD.Application
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Reports
Imports StructureMap

Public Class ucSlipGaji
    Inherits System.Web.UI.UserControl


    Sub ShowRPT()
        Dim cls As New clsRPT
        Dim rpt = cls.GetRpt_SlipGaji(cb_cabang.Value, txt_periode.Value, cb_karyawan.Value)
        Me.ASPxWebDocumentViewer1.OpenReport(rpt)


    End Sub
    Sub SendEmail()
        Dim cls As New clsRPT()
        Dim serv = ObjectFactory.GetInstance(Of GajiService)()
        Dim r = Task.Run(Function() serv.GetGajisAsync()).Result
        Dim sPesan As New StringBuilder()

        If r.Success Then
            Dim _cabang_id As String = cb_cabang.Value
            Dim _karyawan_id As String = cb_karyawan.Value
            Dim tgl As Date = txt_periode.Value
            Dim tgl1 As Date = tgl.AddMonths(-1)
            tgl1 = New Date(tgl1.Year, tgl1.Month, 21)
            Dim tgl2 As Date = tgl1.AddMonths(1).AddDays(-1)

            Dim os As IQueryable(Of tr_gaji) = r.Output

            Dim o = os.Where(Function(f) f.Cabang_id = _cabang_id And f.Tanggal.Year = tgl.Year And f.Tanggal.Month = tgl.Month).FirstOrDefault()

            If o Is Nothing Then
                Return
            End If

            Dim osLine = o.tr_gaji_line.AsQueryable
            If _karyawan_id <> Nothing Then
                osLine = osLine.Where(Function(f) f.Karyawan_id = _karyawan_id)
            End If

            ' Membagi pengiriman email ke dalam batch
            Dim batchSize As Integer = 10
            Dim osLineList = osLine.ToList()
            Dim totalBatches As Integer = Math.Ceiling(osLineList.Count / batchSize)

            For batchIndex As Integer = 0 To totalBatches - 1
                Dim currentBatch = osLineList.Skip(batchIndex * batchSize).Take(batchSize)
                Dim sendEmailTasks As New List(Of Task(Of String))()

                ' Looping karyawan untuk batch saat ini
                For Each x In currentBatch
                    Dim email As String = x.tm_karyawan.Email
                    _karyawan_id = x.Karyawan_id
                    If x.tm_karyawan Is Nothing Then
                        x.tm_karyawan = myFunction.tm_karyawan(_karyawan_id)
                    End If
                    If Not String.IsNullOrEmpty(email) AndAlso myFunction.IsValidEmail(email) Then
                        Dim task = SendEmailWithRetryAsync(cls, _cabang_id, tgl, tgl1, tgl2, x.Karyawan_id, email, x.tm_karyawan.Nama, x.tm_karyawan.TglLahir, o.tm_area.NamaArea)
                        sendEmailTasks.Add(task)
                    Else
                        sPesan.AppendLine($"Email tidak valid untuk karyawan: {x.tm_karyawan.NIK} - {x.tm_karyawan.Nama}</BR>")
                    End If
                Next

                ' Tunggu semua tugas dalam batch selesai dan gabungkan pesan
                Dim results = Task.WhenAll(sendEmailTasks.ToArray()).Result
                For Each result In results
                    If result <> "" Then
                        sPesan.Append(result & "</BR>")
                    End If

                Next
            Next

            ltl_msg.Text = MessageFormatter.GetFormattedNoticeMessage("Send Email Sukses </BR>" & sPesan.ToString())
        Else
            ltl_msg.Text = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Private Async Function SendEmailWithRetryAsync(cls As clsRPT, cabangId As String, tgl As Date, tgl1 As Date, tgl2 As Date, karyawanId As String, email As String, nama As String, tglLahir As Date, namaArea As String) As Task(Of String)
        Dim retryCount As Integer = 3
        Dim success As Boolean = False
        Dim sPesan As New StringBuilder()

        For attempt As Integer = 1 To retryCount
            Try
                ' Logika pengiriman email
                Await SendEmailWithReportsAsync(cls, cabangId, tgl, tgl1, tgl2, karyawanId, email, nama, tglLahir, namaArea)
                success = True
                Exit For ' Jika berhasil, keluar dari loop
            Catch ex As Exception When attempt < retryCount
                ' Catat kegagalan sementara dan tunggu sebelum mencoba lagi
                sPesan.AppendLine($"Percobaan {attempt} gagal untuk email {email}: {ex.Message}")
                Task.Delay(2000).Wait() ' Sinkron untuk menunda sebelum retry
            Catch ex As Exception
                ' Jika gagal pada upaya terakhir, catat kesalahan
                sPesan.AppendLine($"Gagal mengirim email ke {email} setelah {retryCount} percobaan: {ex.Message}")
                Exit For
            End Try
        Next

        ' Tambahkan pesan sukses jika berhasil
        If success Then
            'sPesan.AppendLine($"Email berhasil dikirim ke {email}")
        End If

        ' Kembalikan pesan hasil
        Return sPesan.ToString()
    End Function

    'Sub SendEmail()
    '    Dim cls As New clsRPT()
    '    Dim serv = ObjectFactory.GetInstance(Of GajiService)()
    '    Dim r = Task.Run(Function() serv.GetGajisAsync()).Result
    '    Dim sPesan As New StringBuilder
    '    If r.Success Then
    '        Dim _cabang_id As String = cb_cabang.Value
    '        Dim _karyawan_id As String = cb_karyawan.Value
    '        Dim tgl As Date = txt_periode.Value
    '        Dim tgl1 As Date = tgl.AddMonths(-1)
    '        tgl1 = New Date(tgl1.Year, tgl1.Month, 21)
    '        Dim tgl2 As Date = tgl1.AddMonths(1).AddDays(-1)

    '        Dim os As IQueryable(Of tr_gaji) = r.Output

    '        Dim o = os.Where(Function(f) f.Cabang_id = _cabang_id And f.Tanggal.Year = tgl.Year And f.Tanggal.Month = tgl.Month).FirstOrDefault()

    '        If o Is Nothing Then
    '            Return
    '        End If

    '        Dim osLine = o.tr_gaji_line.AsQueryable
    '        If _karyawan_id <> Nothing Then
    '            osLine = osLine.Where(Function(f) f.Karyawan_id = _karyawan_id)
    '        End If

    '        ' Membuat list untuk menyimpan tasks
    '        Dim sendEmailTasks As New List(Of Task)()

    '        ' Looping karyawan
    '        For Each x In osLine
    '            Dim email As String = x.tm_karyawan.Email
    '            'Dim email As String = "<EMAIL>"
    '            _karyawan_id = x.Karyawan_id
    '            If x.tm_karyawan Is Nothing Then
    '                x.tm_karyawan = myFunction.tm_karyawan(_karyawan_id)
    '            End If
    '            If Not String.IsNullOrEmpty(email) AndAlso myFunction.IsValidEmail(email) Then
    '                Dim task = SendEmailWithReportsAsync(cls, _cabang_id, tgl, tgl1, tgl2, x.Karyawan_id, email, x.tm_karyawan.Nama, x.tm_karyawan.TglLahir, o.tm_area.NamaArea)
    '                sendEmailTasks.Add(task)

    '            Else
    '                sPesan.Append($"Email tidak valid untuk karyawan: {x.tm_karyawan.NIK} - {x.tm_karyawan.Nama}</BR>")
    '            End If


    '        Next
    '        Task.WaitAll(sendEmailTasks.ToArray())
    '        ltl_msg.Text = MessageFormatter.GetFormattedNoticeMessage("Send Email Sukses </BR>" & sPesan.ToString)
    '    Else
    '        ltl_msg.Text = MessageFormatter.GetFormattedErrorMessage(r.Message)
    '    End If
    'End Sub
    Private Async Function SendEmailWithReportsAsync(cls As clsRPT, cabangId As String, tgl As Date, tgl1 As Date, tgl2 As Date, karyawanId As String, email As String, nama As String, tglLahir As Date, companyName As String) As Task
        Dim rptGaji = cls.GetRpt_SlipGaji(cabangId, tgl, karyawanId)
        Dim rptAbsensi = cls.GetRpt_LapAbsensiPerKaryawan(cabangId, tgl1, tgl2, karyawanId, False)
        SendEmailWithAttachments(rptGaji, rptAbsensi, email, nama, Format(tglLahir, "ddMMyyyy"), tgl, companyName)
    End Function
    Public Sub SendEmailWithAttachments(reportGaji As XtraReport, reportAbsensi As XtraReport, toEmail As String, toName As String, sPassword As String, periode As Date, companyName As String)
        ' Set the password for the PDF file
        reportGaji.ExportOptions.Pdf.PasswordSecurityOptions.OpenPassword = sPassword
        reportAbsensi.ExportOptions.Pdf.PasswordSecurityOptions.OpenPassword = sPassword

        ' Generate slip gaji dan absensi
        Dim slipGaji As Byte() = myFunction.GeneratePayrollSlip(reportGaji)
        Dim slipAbsensi As Byte() = myFunction.GeneratePayrollSlip(reportAbsensi) ' Asumsi metode serupa untuk absensi

        ' Subject dan body email
        Dim subject As String = $"Slip Gaji & Absensi - {Format(periode, "MMM yyyy")}"
        Dim body As String = $"Dear {toName},

Berikut kami lampirkan untuk slip gaji dan absensi dari {companyName} periode {Format(periode, "MMMM yyyy")}, atas nama : {toName}



Terima Kasih,"

        ' Nama lampiran
        Dim attachmentNameGaji As String = $"SlipGaji_{toName}.pdf"
        Dim attachmentNameAbsensi As String = $"Absensi_{toName}.pdf"

        ' Kirim email dengan dua lampiran
        MyMethod.SendEmailWithAttachments(toEmail, subject, body, slipGaji, attachmentNameGaji, slipAbsensi, attachmentNameAbsensi)
    End Sub

    'Public Sub SendPayrollSlip(report As XtraReport, toEmail As String, toName As String, sPassword As String)

    '    ' Set the password for the PDF file
    '    report.ExportOptions.Pdf.PasswordSecurityOptions.OpenPassword = sPassword

    '    ' Generate slip gaji
    '    Dim slip As Byte() = myFunction.GeneratePayrollSlip(report)

    '    ' Subject dan body email
    '    Dim subject As String = "Slip Gaji Anda"
    '    Dim body As String = "Berikut terlampir slip gaji Anda."

    '    ' Nama lampiran
    '    Dim attachmentName As String = $"SlipGaji_{toName}.pdf"

    '    ' Kirim email
    '    MyMethod.SendEmail(toEmail, subject, body, slip, attachmentName)
    'End Sub



    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = AuthHelper.GetLoggedInUserInfo.Cabang_id

            txt_periode.Value = Now.Date
        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub
End Class