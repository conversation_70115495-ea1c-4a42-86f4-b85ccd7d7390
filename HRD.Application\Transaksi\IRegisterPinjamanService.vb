﻿Imports HRD.Domain
Public Interface IRegisterPinjamanService
    Function GetQueryableAsync() As Task(Of ResponseModel)
    Function GetByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_register_pinjaman) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function Posting(ByVal o As tr_register_pinjaman) As Task(Of ResponseModel)
End Interface
