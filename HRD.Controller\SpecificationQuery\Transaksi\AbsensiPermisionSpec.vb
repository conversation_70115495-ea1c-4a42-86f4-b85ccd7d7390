﻿Imports HRD.Domain
Imports HRD.Helpers
Public Class AbsensiPermisionSpec
    Inherits CompositeSpecification(Of tr_absensi)

    Private ReadOnly _loggedInUser As ApplicationUser

    Public Sub New()
        _loggedInUser = AuthHelper.GetLoggedInUserInfo

    End Sub

    Public Overrides ReadOnly Property Criteria As Expressions.Expression(Of Func(Of tr_absensi, Boolean))
        Get
            Select Case _loggedInUser.PermisionArea
                Case 0
                    Return Function(f) f.Cabang_id = _loggedInUser.Cabang_id
                    'Case 1
                    'Return Function(f) f.Area_id = _loggedInUser.Area_id
                Case Else
                    Return Function(f) _loggedInUser.AreaIdPermisionList.Contains(f.Area_id)
            End Select
        End Get
    End Property

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of tr_absensi)) As IQueryable(Of tr_absensi)
        If Criteria Is Nothing Then
            Return query
        End If
        Return query.Where(Criteria)
    End Function
End Class
