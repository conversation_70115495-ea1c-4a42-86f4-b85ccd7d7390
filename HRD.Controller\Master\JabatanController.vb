﻿Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap

Public Class JabatanController
    Inherits Controller(Of tm_jabatan)
    Implements IJabatanListController, IJabatanEditController

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_jabatan
    End Sub


    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of JabatanService)()
        Dim r = serv.GetJabatanByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If



    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of JabatanService)()
        Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_jabatan)
        ExpressMapper.Mapper.Register(Of tm_jabatan, tm_jabatan)() _
            .Ignore(Function(x) x.tm_karyawan_datadinas)

        Dim o = ExpressMapper.Mapper.Map(Of tm_jabatan, tm_jabatan)(TEntity)
        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim serv = ObjectFactory.GetInstance(Of JabatanService)()
        Dim r = serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IJabatanListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface IJabatanEditController
    Inherits IControllerMain, IControllerEdit(Of tm_jabatan)


End Interface