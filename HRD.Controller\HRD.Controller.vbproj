<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1C9DDDEA-22BA-4F3F-8248-6016A9452DB7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>HRD.Controller</RootNamespace>
    <AssemblyName>HRD.Controller</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>HRD.Controller.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>HRD.Controller.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BmiHrd.Encryption, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\BmiHrd\BmiHrd.Encryption\bin\Release\BmiHrd.Encryption.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ExpressMapper, Version=1.9.1.0, Culture=neutral, PublicKeyToken=ac363faa09311ba0, processorArchitecture=MSIL">
      <HintPath>..\packages\Expressmapper.1.9.1\lib\net46\ExpressMapper.dll</HintPath>
    </Reference>
    <Reference Include="MediatR, Version=12.0.0.0, Culture=neutral, PublicKeyToken=bb9a41a5e8aaa7e2, processorArchitecture=MSIL">
      <HintPath>..\packages\MediatR.12.4.1\lib\netstandard2.0\MediatR.dll</HintPath>
    </Reference>
    <Reference Include="MediatR.Contracts, Version=2.0.1.0, Culture=neutral, PublicKeyToken=bb9a41a5e8aaa7e2, processorArchitecture=MSIL">
      <HintPath>..\packages\MediatR.Contracts.2.0.1\lib\netstandard2.0\MediatR.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap">
      <HintPath>..\..\..\..\..\EBook\Professional-ASP.NET-Design-Patterns-master\Professional-ASP.NET-Design-Patterns-master\ASPPatterns.Chap8.MVP\Lib\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="Z.EntityFramework.Extensions, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Extensions.7.100.0.4\lib\net45\Z.EntityFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Z.EntityFramework.Plus.EF6, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Plus.EF6.7.100.0.4\lib\net45\Z.EntityFramework.Plus.EF6.dll</HintPath>
    </Reference>
    <Reference Include="Z.Expressions.Eval, Version=5.0.11.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.Expressions.Eval.5.0.11\lib\net45\Z.Expressions.Eval.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controller.vb" />
    <Compile Include="Master\AreaController.vb" />
    <Compile Include="Master\BonusTHRController.vb" />
    <Compile Include="Master\CabangController.vb" />
    <Compile Include="Master\DepartmentController.vb" />
    <Compile Include="Master\EventSettingController.vb" />
    <Compile Include="Master\JabatanController.vb" />
    <Compile Include="Master\KaryawanController.vb" />
    <Compile Include="Master\LiburNasionalController.vb" />
    <Compile Include="Master\OffKaryawanController.vb" />
    <Compile Include="Master\PendidikanController.vb" />
    <Compile Include="Master\RoleController.vb" />
    <Compile Include="Master\SettingAbsensiController.vb" />
    <Compile Include="Master\StatusPerkawinanController.vb" />
    <Compile Include="Master\TunjanganOnetimeController.vb" />
    <Compile Include="Master\UserController.vb" />
    <Compile Include="Mutasi\DataDinasController.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Pinjaman\BayarPinjamanController.vb" />
    <Compile Include="Pinjaman\RegisterPinjamanController.vb" />
    <Compile Include="SpecificationQuery\AndSpecification.vb" />
    <Compile Include="SpecificationQuery\CompositeSpecification.vb" />
    <Compile Include="SpecificationQuery\Dashboard\HabisKontrakSpec.vb" />
    <Compile Include="SpecificationQuery\Master\SettingAbsensiPermision.vb" />
    <Compile Include="SpecificationQuery\Pinjaman\PinjamanPermisionSpec.vb" />
    <Compile Include="SpecificationQuery\ISpecification.vb" />
    <Compile Include="SpecificationQuery\NotSpecification.vb" />
    <Compile Include="SpecificationQuery\Pinjaman\PinjamanRelasedSpec.vb" />
    <Compile Include="SpecificationQuery\Pinjaman\PinjamanStatusSpec.vb" />
    <Compile Include="SpecificationQuery\Transaksi\AbsensiPermisionSpec.vb" />
    <Compile Include="SpecificationQuery\Transaksi\IjinPermisionSpec.vb" />
    <Compile Include="Transaksi\AbsensiController.vb" />
    <Compile Include="Transaksi\CutiBesarController.vb" />
    <Compile Include="Transaksi\GajiController.vb" />
    <Compile Include="Transaksi\IjinController.vb" />
    <Compile Include="Transaksi\KompensasiKontrakController.vb" />
    <Compile Include="Transaksi\SPKLemburController.vb" />
    <Compile Include="Transaksi\THRController.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HRD.Application\HRD.Application.vbproj">
      <Project>{6db5b377-9f8e-4758-8a53-4bf13aaf4dcf}</Project>
      <Name>HRD.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Dao\HRD.Dao.vbproj">
      <Project>{c2185db9-aecc-41b7-af08-fc7bdd4091b2}</Project>
      <Name>HRD.Dao</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Domain\HRD.Domain.vbproj">
      <Project>{93a2fb6b-4503-4da1-80f4-ba3ca6dbb7d8}</Project>
      <Name>HRD.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Helpers\HRD.Helpers.vbproj">
      <Project>{a149668f-9d7f-44e3-981b-56661a2d8d77}</Project>
      <Name>HRD.Helpers</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Infrastructure\HRD.Infrastructure.vbproj">
      <Project>{902d25f8-78e1-4e7a-8578-4d15dd9801d9}</Project>
      <Name>HRD.Infrastructure</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>