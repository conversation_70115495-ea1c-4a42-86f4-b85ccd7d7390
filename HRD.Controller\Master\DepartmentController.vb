﻿Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap

Public Class DepartmentController
    Inherits Controller(Of tm_department)
    Implements IDepartmentListController, IDepartmentEditController

    Public ReadOnly Property DataList As IQueryable(Of tm_department) Implements IDepartmentListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of DepartmentService)()

            Dim r = Serv.GetDepartmentsAsync.GetAwaiter.GetResult
            If r.Success Then
                Return r.Output
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_department
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of DepartmentService)()
        Dim r = Serv.GetDepartmentByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of DepartmentService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_department)
        ExpressMapper.Mapper.Register(Of tm_department, tm_department)() _
            .Ignore(Function(i) i.tm_karyawan_datadinas)

        Dim o = ExpressMapper.Mapper.Map(Of tm_department, tm_department)(TEntity)
        Dim Serv = ObjectFactory.GetInstance(Of DepartmentService)()
        'Dim r = New DepartmentService(New UnitOfWork(New HRDEntities), New ErrorMessageLog).UpsertAsync(o).GetAwaiter.GetResult
        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IDepartmentListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList As IQueryable(Of tm_department)
End Interface
Public Interface IDepartmentEditController
    Inherits IControllerMain, IControllerEdit(Of tm_department)

End Interface