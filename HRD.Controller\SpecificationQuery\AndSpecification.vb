﻿Imports System.Linq.Expressions

Public Class AndSpecification(Of T)
    Inherits CompositeSpecification(Of T)

    Private _leftSpecification As ISpecification(Of T)
    Private _rightSpecification As ISpecification(Of T)

    Public Sub New(ByVal leftSpecification As ISpecification(Of T), ByVal rightSpecification As ISpecification(Of T))
        _leftSpecification = leftSpecification
        _rightSpecification = rightSpecification
    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of T, Boolean))

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of T)) As IQueryable(Of T)
        Dim leftQuery = _leftSpecification.SatisfyingEntitiesInQuery(query)
        Dim rightQuery = _rightSpecification.SatisfyingEntitiesInQuery(query)

        'If _leftSpecification.Criteria IsNot Nothing AndAlso _rightSpecification.Criteria IsNot Nothing Then
        '    Return leftQuery.Where(_rightSpecification.Criteria).Concat(rightQuery.Where(_leftSpecification.Criteria))
        'ElseIf _leftSpecification.Criteria IsNot Nothing AndAlso _rightSpecification.Criteria Is Nothing Then
        '    Return leftQuery.Concat(rightQuery.Where(_leftSpecification.Criteria))
        'ElseIf _leftSpecification.Criteria Is Nothing AndAlso _rightSpecification.Criteria IsNot Nothing Then
        '    Return leftQuery.Where(_rightSpecification.Criteria).Concat(rightQuery)
        'Else
        '    Return leftQuery.Concat(rightQuery)
        'End If

        Return leftQuery.Intersect(rightQuery)

    End Function

    'Public Overrides Function IsSatisfiedBy(candidate As T) As Boolean
    '    Return _leftSpecification.IsSatisfiedBy(candidate) AndAlso _rightSpecification.IsSatisfiedBy(candidate)

    'End Function
End Class
