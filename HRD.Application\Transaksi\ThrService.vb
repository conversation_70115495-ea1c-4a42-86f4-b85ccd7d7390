﻿Imports HRD.Application
Imports HRD.Domain
Imports Z.EntityFramework.Plus
Public Class ThrService
    Implements IThrService
    Private ReadOnly _myFunction As IMyFunction

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, ByVal myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IThrService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_thr)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_thr).DeleteAsync(Id)
                Await _unitOfWork.Repository(Of tr_thr_line).Delete(Function(f) f.Thr_id = Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IThrService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_thr)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements IThrService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_thr)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_thr) As Task(Of ResponseModel) Implements IThrService.UpsertAsync
        Try


            Dim qCabang As New GetAllTmCabangQuery
            Dim cmdCabang As New TmCabangQueryHandler(_unitOfWork)
            Dim cabangs = Await cmdCabang.Handle(qCabang)
            cabangs = cabangs.Where(Function(f) f.Area_id = o.Area_id)
            If o.Cabang_id <> Nothing Then
                cabangs = cabangs.Where(Function(f) f.Id = o.Cabang_id)
            End If

            Dim cabangList = cabangs.ToList
            For Each x In cabangList
                o.Cabang_id = x.Id
                o.KodeTHR = GetKodeTHR(o)
                o.Periode = Format(o.PeriodeDate, "yyyy")

                Dim thr = _unitOfWork.Repository(Of tr_thr).Table.Where(Function(f) f.Cabang_id = x.Id And f.KodeTHR = o.KodeTHR).FirstOrDefault
                If thr IsNot Nothing Then
                    If thr.Posted Then
                        Continue For
                    End If

                    Await _unitOfWork.Repository(Of tr_thr_line).Delete(Function(f) f.Thr_id = thr.Id)

                    With thr
                        .Keterangan = o.Keterangan
                        .ModifiedBy = o.ModifiedBy
                        .ModifiedDate = o.ModifiedDate
                        .Periode = o.Periode
                        .PeriodeDate = o.PeriodeDate
                        .TipeTHR = o.TipeTHR
                        .GajiPokok_b = o.GajiPokok_b
                        .Total = o.Total
                        .T_Jabatan_b = o.T_Jabatan_b
                        .T_Kontrak_b = o.T_Kontrak_b
                        .T_Makan_b = o.T_Makan_b
                        .T_Susu_b = o.T_Susu_b
                        .T_Transport_b = o.T_Transport_b
                        .T_Probation_b = o.T_Probation_b
                    End With





                    _unitOfWork.Repository(Of tr_thr)().Update(thr)
                Else


                    thr = New tr_thr With {.Area_id = o.Area_id, .Cabang_id = x.Id, .KodeTHR = o.KodeTHR, .isCompleted = False, .Keterangan = o.Keterangan _
                        , .CreatedBy = o.CreatedBy, .CreatedDate = o.CreatedDate, .ModifiedBy = o.ModifiedBy, .ModifiedDate = o.ModifiedDate, .Periode = o.Periode _
                        , .PeriodeDate = o.PeriodeDate, .Posted = o.Posted, .Id = o.Id, .PostedBy = o.PostedBy, .PostedDate = o.PostedDate, .TipeTHR = o.TipeTHR _
                        , .GajiPokok_b = o.GajiPokok_b, .Total = o.Total, .T_Jabatan_b = o.T_Jabatan_b, .T_Kontrak_b = o.T_Kontrak_b, .T_Makan_b = o.T_Makan_b _
                        , .T_Susu_b = o.T_Susu_b, .T_Transport_b = o.T_Transport_b, .T_Probation_b = o.T_Probation_b}




                    Await _unitOfWork.Repository(Of tr_thr)().AddAsync(thr)


                End If

                Dim r = Await ProsesTHRPerCabang(thr)
                If r.Success Then
                    thr.Total = If(thr.tr_thr_line.Sum(Function(s) CType(s.Total, Decimal?)), 0)
                Else
                    Return ResponseModel.FailureResponse(r.Message)
                End If
            Next


            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Private Function GetKodeTHR(tEntity As tr_thr) As String
        Dim area = _myFunction.tm_area(tEntity.Area_id)
        Dim cab = _myFunction.tm_cabang(tEntity.Cabang_id)
        Dim sFormat As String = $"{area.KodeArea}-{cab.KodeCabang}-{Format(tEntity.PeriodeDate, "yyyyMM")}-ID"
        If tEntity.TipeTHR <> 0 Then
            sFormat = $"{area.KodeArea}-{cab.KodeCabang}-{Format(tEntity.PeriodeDate, "yyyyMM")}-NT"
        End If
        Return sFormat
    End Function
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "THR Service", method, msg)
    End Sub
    Private Async Function ProsesTHRPerCabang(tEntity As tr_thr) As Task(Of ResponseModel)
        Try
            ' Define batch size
            Const batchSize As Integer = 50

            ' Filter eligible employees
            Dim mkars = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking.Where(Function(f) f.Cabang_id = tEntity.Cabang_id And f.Berhenti_b <> True)

            If tEntity.TipeTHR <> 0 Then
                mkars = mkars.Where(Function(f) f.Agama_id <> 1)
            Else
                mkars = mkars.Where(Function(f) f.Agama_id = 1)
            End If

            ' Convert the query to a list for batch processing
            Dim karyawanList = mkars.ToList()
            Dim bonusList = _unitOfWork.Repository(Of tm_bonus_thr).TableNoTracking.Where(Function(f) f.Periode.Year = tEntity.PeriodeDate.Year AndAlso f.Periode.Month = tEntity.PeriodeDate.Month AndAlso f.Cabang_id = tEntity.Cabang_id AndAlso f.Posted <> True).ToList()
            Dim bonusDict = bonusList.ToDictionary(Function(b) b.Karyawan_id)

            ' Split employees into batches
            Dim batches = karyawanList.
            Select(Function(k, index) New With {.Index = index, .Karyawan = k}).
            GroupBy(Function(x) x.Index \ batchSize).
            Select(Function(g) g.Select(Function(x) x.Karyawan).ToList()).
            ToList()

            Dim sTextErr As New Text.StringBuilder

            ' Process each batch sequentially
            For Each batch In batches
                Dim listTask = batch.Select(Function(k) ProsesTHRPerKaryawan(tEntity, k, bonusDict)).ToList()
                Dim results = Await Task.WhenAll(listTask)

                ' Handle errors within the current batch
                Dim errors = results.Where(Function(r) Not r.Success)
                For Each errorResult In errors
                    sTextErr.Append($"{errorResult.Message}</BR>")
                Next
            Next

            ' Check if there were any errors across all batches
            If sTextErr.Length > 0 Then
                Return ResponseModel.FailureResponse(sTextErr.ToString())
            End If

            Return ResponseModel.SuccessResponse("Sukses", Nothing)
        Catch ex As Exception
            ' Log exception and return failure response
            Log(NameOf(Me.ProsesTHRPerCabang), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Private Async Function ProsesTHRPerKaryawan(tEntity As tr_thr, kar As tm_karyawan, bonusDict As Dictionary(Of Integer, tm_bonus_thr)) As Task(Of ResponseModel)
        'Dim dn = _unitOfWork.Repository(Of tm_karyawan_datadinas).TableNoTracking.Where(Function(f) f.Karyawan_id = kar.Id).OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault
        Dim dn = kar.tm_karyawan_datadinas.OrderByDescending(Function(x) x.Id).FirstOrDefault

        If dn Is Nothing Then
            Return ResponseModel.FailureResponse($"Data Dinas untuk karyawan : {kar.NIK} - {kar.Nama} Belum dibuat!")
        End If

        Dim o As New tr_thr_line

        If tEntity.GajiPokok_b Then
            o.GajiPokok = dn.Pd_GajiPokok
        End If
        If tEntity.T_Jabatan_b Then
            o.T_Jabatan = dn.Pd_T_Jabatan
        End If
        If tEntity.T_Transport_b Then
            o.T_Transport = dn.Pd_T_Transport
        End If
        If tEntity.T_Susu_b Then
            o.T_Susu = dn.Pd_T_Susu
        End If
        If tEntity.T_Makan_b Then
            o.T_Makan = dn.Pd_T_Makan
        End If
        If tEntity.T_Kontrak_b Then
            o.T_Kontrak = dn.Pd_T_Kontrak
        End If
        If tEntity.T_Probation_b Then
            o.T_Probation = dn.Pd_T_Probation
        End If

        ' Add bonus if the employee is in the bonus list
        If bonusDict.ContainsKey(kar.Id) Then
            o.Bonus += bonusDict(kar.Id).Bonus
            o.Bonus_id = bonusDict(kar.Id).Id
        End If
        o.Total = o.GajiPokok + o.T_Jabatan + o.T_Transport + o.T_Susu + o.T_Makan + o.T_Kontrak + o.T_Probation + o.Bonus
        o.Karyawan_id = kar.Id
        o.DataDinas_id = dn.Id

        Dim jmlBulan = DateDiff(DateInterval.Month, kar.TglMasuk, Now)
        o.TahunKerja = jmlBulan
        If jmlBulan < 12 Then
            o.Total = o.Total * (jmlBulan / 12)
        End If

        o.Total = Math.Round(o.Total, 0)

        o.StatusPTKP_id = kar.StatusPerkawinan_id
        Dim mter = _myFunction.PPh21CalculatorBulanan(o.Total, kar.tm_status_PTKP.KodeStatus)
        o.Pt_PPH21 = mter.nilaiPajak
        o.Ter_pajak_id = mter.ter_pajak_id
        o.Ter_pajak_percent = mter.tarifPercent

        o.Pd_T_Pajak = o.Pt_PPH21

        tEntity.tr_thr_line.Add(o)

        Return ResponseModel.SuccessResponse("Sukses", Nothing)
    End Function

    Public Async Function Posting(o As tr_thr, thrLines As List(Of tr_thr_line)) As Task(Of ResponseModel)
        Try

            _unitOfWork.Repository(Of tr_thr)().Update(o)
            ' Update the posted status of the related tm_bonus_thr entities
            Dim bonusIds = thrLines.Where(Function(t) t.Bonus_id.HasValue).Select(Function(t) t.Bonus_id.Value).ToList()
            If bonusIds.Any() Then
                Dim bonusList = _unitOfWork.Repository(Of tm_bonus_thr)().Table.Where(Function(b) bonusIds.Contains(b.Id)).Update(Function(b) New tm_bonus_thr With {.Posted = True})
                'For Each bonus In bonusList
                '    bonus.Posted = True
                '    _unitOfWork.Repository(Of tm_bonus_thr)().Update(bonus)
                'Next
            End If


            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.Posting), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
