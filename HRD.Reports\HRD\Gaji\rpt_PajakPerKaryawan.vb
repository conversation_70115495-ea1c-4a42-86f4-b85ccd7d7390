﻿Imports HRD.Domain
Public Class rpt_PajakPerKaryawan


    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Dim o = CType(Me.GetCurrentRow, tm_ka<PERSON>wan)

        Dim rpt = New subRpt_pajakPerKaryawan
        rpt.ObjectDataSource1.DataSource = o.tr_gaji_line.Where(Function(f) f.tr_gaji.Tahun = pTahun.Value).ToList
        Me.XrSubreport1.ReportSource = rpt

    End Sub
End Class