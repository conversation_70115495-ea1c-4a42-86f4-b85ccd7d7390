﻿Imports HRD.Controller
Imports HRD.Helpers
Imports ILS.MVVM
Public Class frmGantiPassword
    Inherits PageBase

    Public Sub New()
        MyBase.New("9012")
    End Sub

    <Create(Scope:=CreateScope.Session)>
    Property Controller As UserController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = (Controller.SelectedItem Is Nothing)
        ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = (Controller.SelectedItem IsNot Nothing)
    End Sub

    <EventSubscription>
    Public Sub OnMsgChanged(sender As Object, e As EventArgs)
        ltl_msg.Text = Controller.sMsg
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Not IsPostBack Then
            ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
            Controller.Action = Action.ChangePassword
            Controller.SelectItem(AuthHelper.GetLoggedInUserInfo.Id)
        End If
    End Sub

    Protected Sub ASPxCallbackPanel1_Callback(sender As Object, e As DevExpress.Web.CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.Split(";")
        Select Case s(0)
            Case "new"
                Controller.AddNewItem()
            Case "back"
                Controller.Reset()
            Case "save"
                If AuthHelper.SignIn(Controller.SelectedItem.Username, Me.ucGantiPassword_edit.txtOldPassword) <> True Then
                    Controller.sMsg = MessageFormatter.GetFormattedErrorMessage("Password Lama tidak valid")
                    Return
                End If
                If Me.ucGantiPassword_edit.txtNewPassword <> Me.ucGantiPassword_edit.txtKonfirmasiPassword Then
                    Controller.sMsg = MessageFormatter.GetFormattedErrorMessage("New Password dan Konfirmasi Password")
                    Return
                End If
                Me.ucGantiPassword_edit.Saving()
                Controller.Saving()
                If Controller.Saved Then

                    Controller.Reset()

                Else
                    OnEditChanged(Nothing, Nothing)
                    Me.ucGantiPassword_edit.OnEditChanged(Nothing, Nothing)
                End If
            Case "btn_edit"
                Controller.Action = Action.Edit
                Controller.SelectItem(s(1))
            Case "btn_view"
                Controller.Action = Action.View
                Controller.SelectItem(s(1))
            Case "btn_delete"
                Controller.Action = Action.Delete
                Controller.DeleteItem(s(1))
        End Select
    End Sub

    Private Sub frmGantiPassword_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        'If Not IsPostBack Then
        '    Controller.Action = Action.ChangePassword
        '    Controller.SelectItem(AuthHelper.GetLoggedInUserInfo.Id)
        'End If

    End Sub
End Class