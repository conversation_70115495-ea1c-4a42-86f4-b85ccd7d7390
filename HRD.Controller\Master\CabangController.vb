﻿Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Public Class CabangController
    Inherits Controller(Of tm_cabang)
    Implements ICabangListController, ICabangEditController

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New CabangManager(daoFactory)

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_cabang
    End Sub

    Public Overrides Sub SelectItem(id As String)
        mgr.DataAccess.ResetContext()
        SelectedItem = mgr.DataAccess.GetById(id)
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim o = mgr.DataAccess.GetById(id)
            mgr.DataAccess.Delete(o)
            mgr.DataAccess.Save()
            Reset()
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving()
        Try

            Dim o As New tm_cabang With {.Id = SelectedItem.Id, .Alamat = SelectedItem.Alamat, .KodeCabang = SelectedItem.KodeCabang, .Area_id = SelectedItem.Area_id _
                , .CreatedBy = SelectedItem.CreatedBy, .CreatedDate = SelectedItem.CreatedDate, .ModifiedBy = SelectedItem.ModifiedBy, .ModifiedDate = SelectedItem.ModifiedDate _
                , .Fax = SelectedItem.Fax, .IsHeadOffice = SelectedItem.IsHeadOffice, .MingguLibur = SelectedItem.MingguLibur, .NamaCabang = SelectedItem.NamaCabang, .SabtuLibur = SelectedItem.SabtuLibur _
                , .Telp = SelectedItem.Telp, .UpahMinimum = SelectedItem.UpahMinimum, .UpahMinimum_Kes = SelectedItem.UpahMinimum_Kes}

            If o.Id <= 0 Then
                o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.CreatedDate = Now
                mgr.DataAccess.Add(o)
            Else
                o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.ModifiedDate = Now
                mgr.DataAccess.Edit(o)
            End If

            mgr.DataAccess.Save()
            Saved = True
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(o As tm_cabang)
        Throw New NotImplementedException()
    End Sub
End Class
Public Interface ICabangListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface ICabangEditController
    Inherits IControllerMain, IControllerEdit(Of tm_cabang)

End Interface