﻿Imports HRD.Application
Imports HRD.Domain

Public Class BonusTHRService
    Implements IBonusTHRService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _errorMessageLog As IErrorMessageLog
    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        _errorMessageLog = errorMessageLog
    End Sub

    Private Sub Log(ByVal method As String, ByVal msg As String)
        _errorMessageLog.LogError("Application", "TunjanganOnetime Service", method, msg)
    End Sub

    Public Async Function GetBonusTHRAsync() As Task(Of ResponseModel) Implements IBonusTHRService.GetBonusTHRAsync

        Try
            Dim data = _unitOfWork.Repository(Of tm_bonus_thr).TableNoTracking
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, data)
        Catch ex As Exception
            Log("GetBonusTHRAsync", ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function GetBonusTHRByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IBonusTHRService.GetBonusTHRByIdAsync
        Try
            Dim data = Await _unitOfWork.Repository(Of tm_bonus_thr).Get(Id)
            If data Is Nothing Then
                Return ResponseModel.FailureResponse("Data Not Found")
            End If
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, data)
        Catch ex As Exception
            Log("GetBonusTHRByIdAsync", ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(TEntity As tm_bonus_thr) As Task(Of ResponseModel) Implements IBonusTHRService.UpsertAsync
        Try
            Dim o = tm_bonus_thr.Create(TEntity)

            If o.Id <= 0 Then
                o.Bulan = Format(o.Periode, "MM")
                o.Tahun = Format(o.Periode, "yyyy")
                Await _unitOfWork.Repository(Of tm_bonus_thr).AddAsync(o)
            Else
                _unitOfWork.Repository(Of tm_bonus_thr).Update(o)
            End If

            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log("UpsertAsync", ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IBonusTHRService.DeleteAsync
        Try
            Dim data = Await _unitOfWork.Repository(Of tm_bonus_thr).Get(Id)
            If data Is Nothing Then
                Return ResponseModel.FailureResponse("Data Not Found")
            End If
            Await _unitOfWork.Repository(Of tm_bonus_thr).DeleteAsync(Id)
            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Log("DeleteAsync", ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function SaveImport(dt As DataTable, _userInfo As ApplicationUser) As Task(Of ResponseModel) Implements IBonusTHRService.SaveImport
        Dim rows As DataRowCollection = dt.Rows
        Dim repo = _unitOfWork.Repository(Of tm_bonus_thr)
        Dim karyawanRepo = _unitOfWork.Repository(Of tm_karyawan)
        Dim entitiesToAdd As New List(Of tm_bonus_thr)
        Dim entitiesToUpdate As New List(Of tm_bonus_thr)
        Dim nikList As New List(Of String)
        Dim tgl As Date
        ' Collect all NIKs from the DataTable
        For i As Integer = 1 To rows.Count - 1
            Dim row As DataRow = rows(i)
            Dim nik As String = row(0).ToString()
            nikList.Add(nik)
            If Not Date.TryParse(row(2).ToString(), tgl) Then
                Continue For ' Skip if date parsing fails
            End If
        Next

        ' Query all karyawan in one go
        Dim karyawanList = karyawanRepo.TableNoTracking.Where(Function(f) nikList.Contains(f.NIK)).ToList
        Dim karyawanDict = karyawanList.ToDictionary(Function(k) k.NIK)

        ' Query all existing bonuses in one go
        Dim existingBonus = repo.TableNoTracking.Where(Function(f) nikList.Contains(f.tm_karyawan.NIK) AndAlso f.Periode.Year = tgl.Year AndAlso f.Periode.Month = tgl.Month).ToList
        Dim existingBonusDict = existingBonus.ToDictionary(Function(b) (b.tm_karyawan.NIK, b.Periode.Year, b.Periode.Month))

        For i As Integer = 1 To rows.Count - 1
            Dim row As DataRow = rows(i)
            Dim nik As String = row(0).ToString()

            If Not karyawanDict.ContainsKey(nik) Then
                Continue For ' Skip if karyawan not found
            End If

            Dim kar = karyawanDict(nik)
            Dim tanggal As Date
            If Not Date.TryParse(row(2).ToString(), tanggal) Then
                Continue For ' Skip if date parsing fails
            End If

            Dim bonus As Decimal
            If Not Decimal.TryParse(row(3).ToString(), bonus) Then
                Continue For ' Skip if bonus parsing fails
            End If

            Dim keterangan As String = row(4).ToString()
            Dim key = (nik, tanggal.Year, tanggal.Month)
            Dim o As tm_bonus_thr = Nothing

            If existingBonusDict.ContainsKey(key) Then
                o = existingBonusDict(key)
                o.ModifiedBy = _userInfo.UserName
                o.ModifiedDate = Now
                entitiesToUpdate.Add(o)
            Else
                o = New tm_bonus_thr With {
                .CreatedBy = _userInfo.UserName,
                .CreatedDate = Now
            }
                entitiesToAdd.Add(o)
            End If

            With o
                .Area_id = kar.Area_id
                .Cabang_id = kar.Cabang_id
                .Bulan = Format(tanggal, "MM")
                .Tahun = Format(tanggal, "yyyy")
                .Bonus = bonus
                .Karyawan_id = kar.Id
                .Keterangan = keterangan
                .Periode = tanggal
            End With
        Next

        Try
            If entitiesToAdd.Any() Then
                Await repo.AddRangeAsync(entitiesToAdd).ConfigureAwait(False)
            End If

            If entitiesToUpdate.Any() Then
                UpdateRange(entitiesToUpdate)
            End If

            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Log("SaveImport", ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Private Sub UpdateRange(dataList As IList(Of tm_bonus_thr))
        For Each x In dataList
            _unitOfWork.Repository(Of tm_bonus_thr).Update(x)
        Next
    End Sub



End Class
