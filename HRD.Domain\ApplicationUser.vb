﻿Public Class ApplicationUser
    Private privateUserName As String
    Public Property UserName() As String
        Get
            Return privateUserName
        End Get
        Set(ByVal value As String)
            privateUserName = value
        End Set
    End Property
    Private privateFirstName As String
    Public Property FirstName() As String
        Get
            Return privateFirstName
        End Get
        Set(ByVal value As String)
            privateFirstName = value
        End Set
    End Property
    Private privateLastName As String
    Public Property LastName() As String
        Get
            Return privateLastName
        End Get
        Set(ByVal value As String)
            privateLastName = value
        End Set
    End Property
    Private privateEmail As String
    Public Property Email() As String
        Get
            Return privateEmail
        End Get
        Set(ByVal value As String)
            privateEmail = value
        End Set
    End Property
    Private privateAvatarUrl As String
    Public Property AvatarUrl() As String
        Get
            Return privateAvatarUrl
        End Get
        Set(ByVal value As String)
            privateAvatarUrl = value
        End Set
    End Property
    Property Id As Integer
    Public Property PermisionArea As Integer
    Public Property Cabang_id As Integer
    Public Property Area_id As Integer
    Public Property AreaIdPermisionList As IList(Of Integer)
    'Public Property TipeKantor As String
    'Public KantorLayanan_Id As String
End Class