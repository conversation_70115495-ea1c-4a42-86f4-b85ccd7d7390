<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucSPKLembur_release.ascx.vb" Inherits="HRDv23.ucSPKLembur_release" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>    <dx:ASPxCallbackPanel ID="cp_spk_lembur_release" runat="server" ClientInstanceName="cp_spk_lembur_release" OnCallback="cp_spk_lembur_release_Callback">
        <ClientSideEvents EndCallback="function(s, e) { 
            grd_spklembur.Refresh(); 
            grid_SelectAllVisibleRows(grd_spklembur, false); // Clear selection after release
            updateSelectedCount();
        }" />
        <PanelCollection>
            <dx:PanelContent runat="server">
                <table style="width: 100%;">
                    <tr>
                        <td style="width: 150px; font-weight: bold">Perusahaan</td>
                        <td style="width: 350px">
                            <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" NullText="ALL" ClientInstanceName="cb_area" Width="100%">
                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	cb_cabang_list.PerformCallback();
	grd_spklembur.Refresh();
}" Init="onInitCB">
                    </ClientSideEvents>
                    <Columns>
                        <dx:ListBoxColumn FieldName="KodeArea">
                        </dx:ListBoxColumn>
                        <dx:ListBoxColumn FieldName="NamaArea">
                        </dx:ListBoxColumn>
                    </Columns>
                    <ClearButton DisplayMode="Always">
                    </ClearButton>
                    <ValidationSettings SetFocusOnError="True">
                        <RequiredField ErrorText="Required">
                        </RequiredField>
                    </ValidationSettings>
                </dx:ASPxComboBox>
            </td>
            <td style="font-weight: bold; width: 150px">Cabang</td>
            <td>
                <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_cabang_list" ID="cb_cabang" NullText="ALL" Width="350px" LoadDropDownOnDemand="True">
                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_spklembur.Refresh();
}" Init="onInitCB">
                    </ClientSideEvents>
                    <Columns>
                        <dx:ListBoxColumn FieldName="KodeCabang">
                        </dx:ListBoxColumn>
                        <dx:ListBoxColumn FieldName="NamaCabang">
                        </dx:ListBoxColumn>
                    </Columns>
                    <ClearButton DisplayMode="Always">
                    </ClearButton>
                    <ValidationSettings SetFocusOnError="True">
                        <RequiredField ErrorText="Required">
                        </RequiredField>
                    </ValidationSettings>
                </dx:ASPxComboBox>
            </td>
        </tr>        </table>   
                <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_spklembur">       
                    <ClientSideEvents CustomButtonClick="function(s, e) {
	    var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	    if(e.buttonID!='btn_print'){
		    if(e.buttonID=='btn_delete'){
			    var b=confirm('Are you sure to delete this item?');
			    if(b){
				    cp_spk_lembur_release.PerformCallback(e.buttonID+';'+rowKey);
			    }
		    }else{cp_spk_lembur_release.PerformCallback(e.buttonID+';'+rowKey);}
	    }else{wd1.Show();}

    }" ToolbarItemClick="function(s, e) {	    switch (e.item.name) { 
        case 'btn_select_all':
            grid_SelectAllVisibleRows(grd_spklembur, true);
            updateSelectedCount();
            break;
        case 'btn_clear_selection':
            grid_SelectAllVisibleRows(grd_spklembur, false);
            updateSelectedCount();
            break;
        case 'btn_release_selected':
            var selectedIds = getSelectedIds();
            if(selectedIds.length > 0) {
                if(confirm('Are you sure you want to release ' + selectedIds.length + ' selected items?')) {
                    cp_spk_lembur_release.PerformCallback('release_selected;' + selectedIds.join(','));
                }
            } else {
                alert('Please select at least one item to release');
            }
            break;
        case 'btn_new':
            cp_spk_lembur.PerformCallback('new;'+cb_area.GetValue()+';'+cb_cabang.GetValue()); 
            break;
        }
    }" SelectionChanged="function(s, e) { updateSelectedCount(); }" /><SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
        </SettingsAdaptivity>
        <SettingsPager AlwaysShowPager="True">
            <AllButton Visible="True">
            </AllButton>
            <PageSizeItemSettings Visible="True">
            </PageSizeItemSettings>        </SettingsPager>        <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
        <SettingsBehavior AllowFocusedRow="True" AllowSelectByRowClick="True" />
    <SettingsPopup>
    <FilterControl AutoUpdatePosition="False"></FilterControl>
    </SettingsPopup>
        <SettingsSearchPanel Visible="True" />
        <SettingsExport EnableClientSideExportAPI="True">        </SettingsExport>        <Columns>
            <dx:GridViewCommandColumn ShowSelectCheckbox="true" VisibleIndex="0" Width="30px" >
            </dx:GridViewCommandColumn>
            <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="1">
                <CustomButtons>
                    <dx:GridViewCommandColumnCustomButton ID="btn_release" Text="Release">
                        <Image IconID="iconbuilder_actions_checkcircled_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                </CustomButtons>
            </dx:GridViewCommandColumn>
<dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="1" Caption="Perusahaan"></dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="2" Caption="Cabang">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="NoSpk" VisibleIndex="3">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="4">
                <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                </PropertiesDateEdit>
            </dx:GridViewDataDateColumn>
            <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="6">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="TotalJam" VisibleIndex="5">
                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                </PropertiesTextEdit>
            </dx:GridViewDataTextColumn>
        </Columns>        <Toolbars>
            <dx:GridViewToolbar>
                <Items>
                    <dx:GridViewToolbarItem Name="btn_select_all" Text="Select All" BeginGroup="True">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Name="btn_clear_selection" Text="Clear Selection">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Name="btn_release_selected" Text="Release Selected" BeginGroup="True">
                        <Image IconID="iconbuilder_actions_checkcircled_svg_16x16">
                        </Image>
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Command="ShowFilterRow">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                    </dx:GridViewToolbarItem>
                </Items>
            </dx:GridViewToolbar>
        </Toolbars>
    </dx:ASPxGridView>    
                <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_spk_lembur">
    </dx:EntityServerModeDataSource>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>    <script type="text/javascript">
        function getSelectedIds() {
            var selectedIds = [];
            var grid = grd_spklembur;
            
            if (grid) {
                // Use GetVisibleRowsOnPage() which is more likely to be supported in older versions
                var visibleRowCount = grid.GetVisibleRowsOnPage();
                for (var i = 0; i < visibleRowCount; i++) {
                    if (grid.IsRowSelectedOnPage(i)) {
                        var key = grid.GetRowKey(i);
                        if (key) {
                            selectedIds.push(key);
                        }
                    }
                }
            }
            
            return selectedIds;
        }
        
        function updateSelectedCount() {
            var count = 0;
            var grid = grd_spklembur;
            
            if (grid) {
                var visibleRowCount = grid.GetVisibleRowsOnPage();
                for (var i = 0; i < visibleRowCount; i++) {
                    if (grid.IsRowSelectedOnPage(i)) {
                        count++;
                    }
                }
                
                // Try to safely update the toolbar text
                try {
                    var toolbar = grid.GetToolbar(0);
                    if (toolbar) {
                        var releaseButton = toolbar.GetItemByName('btn_release_selected');
                        if (releaseButton) {
                            releaseButton.SetText('Release Selected (' + count + ')');
                        }
                    }
                } catch (e) {
                    // If any of these methods aren't supported, just ignore the error
                    console.log("Could not update button text: " + e.message);
                }
            }
        }
        
        function grid_SelectAllVisibleRows(grid, select) {
            if (grid) {
                try {
                    var visibleRowCount = grid.GetVisibleRowsOnPage();
                    for (var i = 0; i < visibleRowCount; i++) {
                        if (select) {
                            grid.SelectRowOnPage(i);
                        } else {
                            grid.UnselectRowOnPage(i);
                        }
                    }
                } catch (e) {
                    console.log("Error with row selection: " + e.message);
                }
            }
        }
    </script>
