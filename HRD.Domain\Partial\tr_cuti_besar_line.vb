Partial Public Class tr_cuti_besar_line
    Public Shared Function Create(
        Optional id As Integer = 0,
        Optional cutiBesar_id As Integer = 0,
        Optional karyawan_id As Integer = 0,
        Optional tanggalTetap As Date = Nothing,
        Optional lamaKerja As Integer = 0,
        Optional pd_GajiPokok As Decimal = 0,
        Optional pd_T_Jabatan As Decimal = 0,
        Optional pd_T_Susu As Decimal = 0,
        Optional pd_T_Probation As Decimal = 0,
        Optional pd_T_Kehadiran As Decimal = 0,
        Optional pd_T_Makan As Decimal = 0,
        Optional total As Decimal = 0) As tr_cuti_besar_line

        Return New tr_cuti_besar_line With {
            .Id = id,
            .CutiBesar_id = cutiBesar_id,
            .Karyawan_id = karyawan_id,
            .TanggalTetap = tanggalTetap,
            .LamaKerja = lamaKerja,
            .Pd_GajiPokok = pd_GajiPokok,
            .Pd_T_Jabatan = pd_T_Jabatan,
            .Pd_T_Susu = pd_T_Susu,
            .Pd_T_Probation = pd_T_Probation,
            .Pd_T_Kehadiran = pd_T_Kehadiran,
            .Pd_T_Makan = pd_T_Makan,
            .Total = total
        }
    End Function

    ' Create method from existing object
    Public Shared Function Create(source As tr_cuti_besar_line) As tr_cuti_besar_line
        If source Is Nothing Then
            Return Nothing
        End If

        Return New tr_cuti_besar_line With {
            .Id = source.Id,
            .CutiBesar_id = source.CutiBesar_id,
            .Karyawan_id = source.Karyawan_id,
            .TanggalTetap = source.TanggalTetap,
            .LamaKerja = source.LamaKerja,
            .Pd_GajiPokok = source.Pd_GajiPokok,
            .Pd_T_Jabatan = source.Pd_T_Jabatan,
            .Pd_T_Susu = source.Pd_T_Susu,
            .Pd_T_Probation = source.Pd_T_Probation,
            .Pd_T_Kehadiran = source.Pd_T_Kehadiran,
            .Pd_T_Makan = source.Pd_T_Makan,
            .Total = source.Total
        }
    End Function
End Class
