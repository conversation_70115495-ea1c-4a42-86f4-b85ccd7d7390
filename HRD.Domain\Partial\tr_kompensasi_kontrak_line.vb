﻿Partial Public Class tr_kompensasi_kontrak_line
    Public Shared Function Create(
        Optional id As Integer = 0,
        Optional karyawan_id As Integer = 0,
        Optional kompensasi_id As Integer = 0,
        Optional lamaKerja As Integer = 0,
        Optional nilaiKompensasi As Decimal = 0,
        Optional tanggalAkhirKontrak As Date = Nothing,
        Optional TanggalAwalKontrak As Date = Nothing)


        Dim obj As New tr_kompensasi_kontrak_line With {
            .Id = id,
            .Karyawan_id = karyawan_id,
            .Kompensasi_id = kompensasi_id,
            .LamaKerja = lamaKerja,
            .NilaiKompensasi = nilaiKompensasi,
            .TanggalAkhirKontrak = tanggalAkhirKontrak,
            .TanggalAwalKontrak = TanggalAwalKontrak
        }

        Return obj
    End Function

    ' Create method from existing object
    Public Shared Function Create(source As tr_kompensasi_kontrak_line) As tr_kompensasi_kontrak_line
        If source Is Nothing Then
            Return Nothing
        End If

        Return New tr_kompensasi_kontrak_line With {
            .Id = source.Id,
            .Karyawan_id = source.Karyawan_id,
            .Kompensasi_id = source.Kompensasi_id,
            .LamaKerja = source.Lama<PERSON>erja,
            .NilaiKompensasi = source.NilaiKompensasi,
            .TanggalAkhirKontrak = source.TanggalAkhirKontrak,
            .TanggalAwalKontrak = source.TanggalAwalKontrak
        }
    End Function
End Class
