﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucKaryawan_list.ascx.vb" Inherits="HRDv23.ucKaryawan_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="Filter" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem Caption="Perusahaan" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabanglist.PerformCallback();
	grd_karyawan.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Cabang" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabanglist" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" NullText="ALL">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_karyawan.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ClearButton DisplayMode="Always">
                                </ClearButton>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Sudah Berhenti" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxCheckBox ID="chk_berhenti" runat="server" CheckState="Unchecked">
                                <ClientSideEvents ValueChanged="function(s, e) {
	grd_karyawan.Refresh();
}" />
                            </dx:ASPxCheckBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Daftar Karyawan" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_karyawan" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" Width="100%">
                                <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
                                </SettingsAdaptivity>
                                <Settings ShowGroupPanel="true" />
                                <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_karyawan.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_karyawan.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) {
case 'btn_new':
cp_karyawan.PerformCallback('new');
break;
}

}" />
                                <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
                                </SettingsAdaptivity>
                                <SettingsPager AlwaysShowPager="True">
                                    <AllButton Visible="True">
                                    </AllButton>
                                    <PageSizeItemSettings Visible="True">
                                    </PageSizeItemSettings>
                                </SettingsPager>
                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                <SettingsBehavior AllowFocusedRow="True" />
                                <SettingsPopup>
                                    <FilterControl AutoUpdatePosition="False">
                                    </FilterControl>
                                </SettingsPopup>
                                <SettingsSearchPanel Visible="True" />
                                <SettingsExport EnableClientSideExportAPI="True">
                                </SettingsExport>
                                <Columns>
                                    <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                    <Image IconID="iconbuilder_security_visibility_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
                                    <dx:GridViewDataTextColumn Caption="Perusahaan" FieldName="tm_area.NamaArea" ShowInCustomizationForm="True" VisibleIndex="1">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Cabang" FieldName="tm_cabang.NamaCabang" ShowInCustomizationForm="True" VisibleIndex="2">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Departemen" FieldName="DepartemenName" ShowInCustomizationForm="True" VisibleIndex="3" GroupIndex="0">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="NIK" ShowInCustomizationForm="True" VisibleIndex="3">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Nama" ShowInCustomizationForm="True" VisibleIndex="4">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Tempat" ShowInCustomizationForm="True" VisibleIndex="5">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglLahir" ShowInCustomizationForm="True" VisibleIndex="6">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn Caption="J. Kelamin" FieldName="tm_jenis_kelamin.JenisKelamin" ShowInCustomizationForm="True" VisibleIndex="7">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Alamat" ShowInCustomizationForm="True" VisibleIndex="9">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Kota" ShowInCustomizationForm="True" VisibleIndex="10">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Telp" ShowInCustomizationForm="True" VisibleIndex="11">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Status" FieldName="tm_status_PTKP.KodeStatus" ShowInCustomizationForm="True" VisibleIndex="12">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Pendidikan" FieldName="tm_pendidikan.NamaPendidikan" ShowInCustomizationForm="True" VisibleIndex="13">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Jurusan" FieldName="tm_jurusan.NamaJurusan" ShowInCustomizationForm="True" VisibleIndex="14">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Alumni" ShowInCustomizationForm="True" VisibleIndex="15">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="TahunLulus" ShowInCustomizationForm="True" VisibleIndex="16">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataDateColumn Caption="Tgl Masuk" FieldName="TglMasuk" ShowInCustomizationForm="True" VisibleIndex="17">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataComboBoxColumn Caption="Status Karyawan" FieldName="KaryawanTetap" ShowInCustomizationForm="True" VisibleIndex="18">
                                        <PropertiesComboBox ValueType="System.Boolean">
                                            <Items>
                                                <dx:ListEditItem Text="Tetap" Value="True" />
                                                <dx:ListEditItem Text="Kontrak" Value="False" />
                                            </Items>
                                        </PropertiesComboBox>
                                    </dx:GridViewDataComboBoxColumn>
                                    <dx:GridViewDataTextColumn FieldName="Email" ShowInCustomizationForm="True" VisibleIndex="19">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="NoKTP" ShowInCustomizationForm="True" VisibleIndex="8">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="NPWP" ShowInCustomizationForm="True" VisibleIndex="20">
                                    </dx:GridViewDataTextColumn>
                                </Columns>
                                <Toolbars>
                                    <dx:GridViewToolbar>
                                        <Items>
                                            <dx:GridViewToolbarItem Name="btn_new" Text="New">
                                                <Image IconID="actions_new_svg_16x16">
                                                </Image>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem Command="ShowFilterRow">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                            </dx:ASPxGridView>
                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_karyawan" />
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

