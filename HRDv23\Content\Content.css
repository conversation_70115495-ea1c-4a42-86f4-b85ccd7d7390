﻿/* Common */
body
{
    /* DXCOMMENT: Duplicate a font family and font size from the web.config (devexpress->themes->font settings) for the web-site's HTML content */
    font: 16px 'Segoe UI', Helvetica, 'Droid Sans', Tahoma, Geneva, sans-serif;
}

/* Article Layout */
.text-content
{
    min-height: 100%;
    max-width: 816px;
    margin: 0px auto;
    padding: 0px 14px 0px 14px;
    word-wrap: break-word;
}

@media (min-width: 816px)
{
    .text-content
    {
        padding: 0px 48px;
    }
}

/* Base Styles */
.text-content
{
    color: #666;
    line-height: 1.5;
}
    .text-content p
    {
        margin-top: 0;
        margin-bottom: 1rem;
    }

/* Headers */
.text-content h1, .text-content h2, .text-content h3, .text-content h4, .text-content h5, .text-content h6
{
    color: #444;
    font-weight: normal;
    margin-top: 0;
    margin-bottom: 0.5rem;
}
.text-content h3, .text-content h4, .text-content h5, .text-content h6
{
    font-weight: 600;
}

.text-content h1
{
    font-size: 2em;
}
.text-content h2
{
    font-size: 1.75em;
}
.text-content h3
{
    font-size: 1.375em;
}
.text-content h4
{
    font-size: 1.25em;
}
.text-content h5
{
    font-size: 1.125em;
}
.text-content h6
{
    font-size: 1em;
}

.text-content > h1:not(:first-child)
{
    margin-top: 1.75rem;
}

.text-content > h2:not(:first-child)
{
    margin-top: 1.5rem;
}

.text-content > h3:not(:first-child)
{
    margin-top: 1.25rem;
}

.text-content > h4:not(:first-child)
{
    margin-top: 1.125rem;
}

.text-content > h5:not(:first-child)
{
    margin-top: 1rem;
}

.text-content > h6:not(:first-child)
{
    margin-top: .9rem;
}

/* Title + Lead */
.text-content .title
{
    margin-top: 1.2rem;
    margin-bottom: .8rem;
}
@media (min-width: 576px)
{
    .text-content .title
    {
        color: #222;
        font-size: 2em;
        font-weight: 600;
    }
}

.text-content .lead
{
    font-size: 1.125rem;
    font-weight: 300;
}
@media (min-width: 576px)
{
    .text-content .lead
    {
        max-width: 80%;
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }
}

.text-content > h2.category
{
    color: #222;
    font-weight: 600;
}

.text-content > h2.category:not(:first-child)
{
    margin-top: 3rem;
}


/* #href bookmark jump */
.text-content > h1[id]::before,
.text-content > h2[id]::before,
.text-content > h3[id]::before,
.text-content > h4[id]::before
{
    display: block;
    height: calc(3.13rem + 1.2rem); /* Main Menu Height (3.13rem) + .title margin-top = 1.2rem */
    margin-top: calc(-3.13rem - 1.2rem);
    visibility: hidden;
    content: "";
}


/* Link */
.text-content a
{
    /* DXCOMMENT: Duplicates color from the web.config (devexpress->themes->baseColor) */
    color: #F87C1D;
}
.text-content a:visited
{
    /* DXCOMMENT: Duplicates color from the web.config (devexpress->themes->baseColor) */
    color: #BA5D15; /* 25% black overlay */
}
.text-content a:hover
{
    /* DXCOMMENT: Duplicates color from the web.config (devexpress->themes->baseColor) */
    color: #FAA96C; /* 35% white overlay */
}

.text-content b, .text-content strong
{
    color: #444;
}

mark
{
    /* DXCOMMENT: Duplicates color from the web.config (devexpress->themes->baseColor) */
    background-color: #F87C1D;
    color: #fff;
}

small
{
    color: #999999;
    font-size: 0.75em;
}

code, pre
{
    font-family: Consolas, Menlo, Monaco, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875em;
}

hr
{
    height: 1px;
    opacity: 0.1;
    background-color: #000000;
    border: 0;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

blockquote
{
    background-color: #f2f2f2;
    color:#444;
    margin:0;
    margin-bottom: 1rem;
    font-size: 1.125em;
    padding: 29px 26px;
    border: 0;
    border-left: 6px solid #d8d8d8;
}

blockquote p
{
    margin-top:0;
}
blockquote footer
{
    display: block;
    font-size: 0.875em;
    font-style: italic;
    color: #999;
}

blockquote footer::before
{
    content: "\2014 \00A0";
}
        
/* Contents (Left Panel) */
.section-caption.contents-caption
{
    padding-left: 31px;
    border-top: none !important;
}


/* Lists */
.text-content ul,
.text-content ol
{
    margin-top: 1em;
    margin-bottom: 1em;
}

@media (min-width: 992px)
{
    .bd-content > ul, .bd-content > ol, .bd-content > p
    {
        max-width: 80%;
    }
}

/* Text Alignment */
.text-center
{
    text-align: center !important;
}

.text-right
{
    text-align: right !important;
}

.indent-p
{
    padding-left: 40px;
    padding-right: 20px;
}

.bordered-block
{
    background-color: #f2f2f2;
    padding: 1.5rem;
    margin-right: 0;
    margin-left: 0;
    border-width: .2rem;
}

@media (min-width: 576px)
{
    .bordered-block
    {
        padding: 1.5rem;
        margin-right: 0;
        margin-left: 0;
        border-width: .2rem;
    }
}

/* Tables */
.table
{
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.table td,
.table th
{
    padding: .75em;
    padding-top: 0.5em;
    padding-bottom: 0.5em;

    vertical-align: top;
    border-top: 1px solid #dee2e6;
    text-align: left;
}

.table thead th
{
    border-bottom: 1px solid #d8d8d8;
    background-color: #f2f2f2;
    color: #222;
}
.table thead tr
{
    box-shadow: 0px 2px 4px 0 rgba(0, 0, 0, 0.23);
}

.table tbody + tbody
{
    border-top: 1px solid #dee2e6;
}

.table-bordered
{
    border: 1px solid #dee2e6;
}

.table-bordered td,
.table-bordered th
{
    border: 1px solid #dee2e6
}

.table-bordered thead td,
.table-bordered thead th
{
    border-bottom-width: 1px
}

.table-responsive
{
    max-width: 100%;
    overflow-x: auto;
    display: block;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

/* Code, Control Area Block */
.control-area-block
{
    max-width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
    box-sizing: border-box;
    background-color: #f8f9fa;
}

/* Images */
.img-responsive
{
    max-width: 100%;
    height: auto;
}

.img-float-left
{
    float: left;
    padding-right: 40px;
    padding-bottom: 40px;
}
@media (max-width: 576px)
{
    .img-float-left
    {
        padding-right: 16px;
        padding-bottom: 16px;
    }
}

/* Printing Styles */
@media print
{
    .menu-container, .left-block, .right-block,
    .footer-wrapper
    {
        display: none !important;
    }

    .left-panel
    {
        display: none !important;
    }

    .text-content
    {
        max-width: 100%;
    }

    html
    {
        padding: 0 !important;
    }

    .page-toolbar-wrapper
    {
        display: none !important;
    }
}

/* Success, notice and error boxes
-------------------------------------------------------------- */

.error, .notice, .success {
    padding: .8em;
    margin-bottom: 1em;
    border: 2px solid #ddd;
}

.error {
    background: #FBE3E4;
    color: #8a1f11;
    border-color: #FBC2C4;
}

.notice {
    background: #FFF6BF;
    color: #514721;
    border-color: #FFD324;
}

.success {
    background: #E6EFC2;
    color: #264409;
    border-color: #C6D880;
}

.error a {
    color: #8a1f11;
}

.notice a {
    color: #514721;
}

.success a {
    color: #264409;
}
