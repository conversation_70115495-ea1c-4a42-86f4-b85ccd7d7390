﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Public Class rpt_rekap_lembur
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Static rowCount As Integer = 0
        rowCount += 1

        cell_no.Text = $"{rowCount}."
    End Sub

    Private Sub fLemburBiasa_jam1_GetValue(sender As Object, e As GetValueEventArgs) Handles fLemburBiasa_jam1.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.<PERSON> >= pStartDate.Value AndAlso w.<PERSON> <= pEndDate.Value).Sum(Function(f) f.Lembur_HKerja_1)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub

    Private Sub fLemburBiasa_jam2_GetValue(sender As Object, e As GetValueEventArgs) Handles fLemburBiasa_jam2.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.Tanggal >= pStartDate.Value AndAlso w.Tanggal <= pEndDate.Value).Sum(Function(f) f.Lembur_HKerja_2)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub

    Private Sub fLemburLibur_jam7_GetValue(sender As Object, e As GetValueEventArgs) Handles fLemburLibur_jam7.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.Tanggal >= pStartDate.Value AndAlso w.Tanggal <= pEndDate.Value).Sum(Function(f) f.Lembur_HLibur_1_7)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub

    Private Sub fLemburLibur_jam8_GetValue(sender As Object, e As GetValueEventArgs) Handles fLemburLibur_jam8.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.Tanggal >= pStartDate.Value AndAlso w.Tanggal <= pEndDate.Value).Sum(Function(f) f.Lembur_HLibur_8)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub

    Private Sub fLemburLibur_jam9_GetValue(sender As Object, e As GetValueEventArgs) Handles fLemburLibur_jam9.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.Tanggal >= pStartDate.Value AndAlso w.Tanggal <= pEndDate.Value).Sum(Function(f) f.Lembur_HLibur_9_10)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub

    Private Sub fTotal_GetValue(sender As Object, e As GetValueEventArgs) Handles fTotal.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim jmlMenit = o.tr_absensi.Where(Function(w) w.Tanggal >= pStartDate.Value AndAlso w.Tanggal <= pEndDate.Value).Sum(Function(f) f.Lembur_HKerja_1 + f.Lembur_HKerja_2 + f.Lembur_HLibur_1_7 + f.Lembur_HLibur_8 + f.Lembur_HLibur_9_10)
        Dim jam = jmlMenit \ 60
        Dim menit = jmlMenit Mod 60
        e.Value = $"{jam}:{menit:D2}" ' e.g., "1:30"
    End Sub
End Class