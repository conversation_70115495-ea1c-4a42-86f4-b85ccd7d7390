﻿Imports HRD.Application
Imports HRD.Domain
Public Class JabatanService
    Implements IJabatanService

#Region "Properties"
    Private ReadOnly _unitOfWork As IUnitOfWork
    'Private ReadOnly logger As ILogger(Of DepartmentService)
    'Private ReadOnly mapper As IMapper
    Private ReadOnly errorMessageLog As IErrorMessageLog
#End Region

#Region "Ctor"
    Public Sub New(ByVal unitOfWork As IUnitOfWork, ByVal errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        'Me.logger = logger
        'Me.mapper = mapper
        Me.errorMessageLog = errorMessageLog
    End Sub
#End Region

#Region "Error"
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Jabatan Service", method, msg)
    End Sub
#End Region

    Public Async Function GetJabatansAsync() As Task(Of ResponseModel) Implements IJabatanService.GetJabatansAsync
        Try
            Dim jabatans = _unitOfWork.Repository(Of tm_jabatan)().TableNoTracking.OrderBy(Function(t) t.Id) '.ToListAsync()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, jabatans)
        Catch ex As Exception
            Log(NameOf(Me.GetJabatansAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetJabatanByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IJabatanService.GetJabatanByIdAsync
        Try
            Dim depart = Await _unitOfWork.Repository(Of tm_jabatan)().Get(Id)
            If depart IsNot Nothing Then
                'Dim appSettingVm = mapper.Map(Of AppSettingVm)(appSetting)
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, depart)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetJabatanByIdAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_jabatan) As Task(Of ResponseModel) Implements IJabatanService.UpsertAsync
        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tm_jabatan)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tm_jabatan)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IJabatanService.DeleteAsync
        Try
            Dim depart = Await _unitOfWork.Repository(Of tm_jabatan)().Get(Id)
            If depart IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_jabatan).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
