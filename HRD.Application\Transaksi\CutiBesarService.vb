Imports HRD.Application
Imports HRD.Domain

Public Class CutiBesarService
    Implements ICutiBesarService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction

    Public Sub New(unitOfWork As IUnitOfWork, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        _myFunction = myFunction
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements ICutiBesarService.GetQueryableAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ICutiBesarService.GetByIdAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking.Where(Function(x) x.Id = Id).FirstOrDefault
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function UpsertAsync(TEntity As tr_cuti_besar) As Task(Of ResponseModel) Implements ICutiBesarService.UpsertAsync
        _unitOfWork.BeginTransaction()
        Try
            ' Validate input
            Dim validationResult = ValidateCutiBesar(TEntity)
            If Not validationResult.Success Then
                Return validationResult
            End If

            If TEntity.Id <= 0 Then
                ' Add new
                TEntity.NoCutiBesar = Await GenerateNoCutiBesar(TEntity.Area_id, TEntity.Tahun)
                TEntity.Status = "DRAFT"
                TEntity.Posted = False
                Await _unitOfWork.Repository(Of tr_cuti_besar)().AddAsync(TEntity)
            Else
                ' Update existing - get from database first
                Dim existing = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(TEntity.Id)
                If existing Is Nothing Then
                    Throw New Exception("Data tidak ditemukan")
                End If

                If existing.Posted Then
                    Throw New Exception("Data yang sudah diposting tidak bisa diubah")
                End If

                ' Update only changed properties
                UpdateCutiBesar(existing, TEntity)
                _unitOfWork.Repository(Of tr_cuti_besar)().Update(existing)
            End If

            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil disimpan", TEntity)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ICutiBesarService.DeleteAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If entity.Posted Then
                Return ResponseModel.FailureResponse("Data yang sudah diposting tidak bisa dihapus")
            End If

            ' Delete related line items first if any
            Await _unitOfWork.Repository(Of tr_cuti_besar_line)().Delete(Function(x) x.CutiBesar_id = Id)

            ' Delete main record
            Await _unitOfWork.Repository(Of tr_cuti_besar)().DeleteAsync(Id)
            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil dihapus")
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function PostingAsync(TEntity As tr_cuti_besar) As Task(Of ResponseModel) Implements ICutiBesarService.PostingAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(TEntity.Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If entity.Posted Then
                Return ResponseModel.FailureResponse("Data sudah diposting sebelumnya")
            End If

            If entity.Status <> "DRAFT" Then
                Return ResponseModel.FailureResponse("Status dokumen harus DRAFT")
            End If

            entity.Posted = True
            entity.Status = "POSTED"
            entity.PostedBy = TEntity.PostedBy
            entity.PostedDate = TEntity.PostedDate

            _unitOfWork.Repository(Of tr_cuti_besar)().Update(entity)
            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil diposting")
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function VoidAsync(Id As Integer, Reason As String) As Task(Of ResponseModel) Implements ICutiBesarService.VoidAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If Not entity.Posted Then
                Return ResponseModel.FailureResponse("Data yang belum diposting tidak bisa divoid")
            End If

            entity.Posted = False
            entity.Status = "VOID"
            entity.PostedBy = String.Empty
            entity.PostedDate = Nothing
            entity.Keterangan = If(String.IsNullOrEmpty(entity.Keterangan), Reason, entity.Keterangan & " [VOID: " & Reason & "]")

            _unitOfWork.Repository(Of tr_cuti_besar)().Update(entity)
            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil divoid")
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Private Function ValidateCutiBesar(entity As tr_cuti_besar) As ResponseModel
        If entity Is Nothing Then
            Return ResponseModel.FailureResponse("Data cuti besar tidak valid")
        End If

        If String.IsNullOrEmpty(entity.Area_id) Then
            Return ResponseModel.FailureResponse("Area harus diisi")
        End If

        If entity.Tanggal = Nothing Then
            Return ResponseModel.FailureResponse("Tanggal harus diisi")
        End If

        If entity.Tahun <= 0 Then
            Return ResponseModel.FailureResponse("Tahun harus diisi")
        End If

        Return ResponseModel.SuccessResponse("Validasi berhasil")
    End Function

    Private Sub UpdateCutiBesar(existing As tr_cuti_besar, updated As tr_cuti_besar)
        ' Update only if changed
        If existing.Tanggal <> updated.Tanggal Then
            existing.Tanggal = updated.Tanggal
        End If

        If existing.Tahun <> updated.Tahun Then
            existing.Tahun = updated.Tahun
        End If

        If existing.Cabang_id <> updated.Cabang_id Then
            existing.Cabang_id = updated.Cabang_id
        End If

        existing.Keterangan = updated.Keterangan
        existing.Total = updated.Total
        existing.ModifiedBy = updated.ModifiedBy
        existing.ModifiedDate = updated.ModifiedDate
    End Sub

    Private Async Function GenerateNoCutiBesar(areaId As String, tahun As Integer) As Task(Of String)
        Try
            Dim lastNo = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking _
                .Where(Function(x) x.Area_id = areaId AndAlso x.Tahun = tahun) _
                .Select(Function(x) x.NoCutiBesar) _
                .Where(Function(x) Not String.IsNullOrEmpty(x)) _
                .OrderByDescending(Function(x) x) _
                .FirstOrDefault()

            Dim nextNumber As Integer = 1
            If Not String.IsNullOrEmpty(lastNo) AndAlso lastNo.Length >= 4 Then
                Dim numberPart = lastNo.Substring(lastNo.Length - 4)
                If Integer.TryParse(numberPart, nextNumber) Then
                    nextNumber += 1
                End If
            End If

            Return $"CB/{areaId}/{tahun.ToString().Substring(2)}/{nextNumber:D4}"
        Catch ex As Exception
            Return $"CB/{areaId}/{tahun.ToString().Substring(2)}/0001"
        End Try
    End Function
End Class
