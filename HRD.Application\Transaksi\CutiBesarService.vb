Imports HRD.Application
Imports HRD.Domain

Public Class CutiBesarService
    Implements ICutiBesarService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction

    Public Sub New(unitOfWork As IUnitOfWork, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        _myFunction = myFunction
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements ICutiBesarService.GetQueryableAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ICutiBesarService.GetByIdAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking.Where(Function(x) x.Id = Id).FirstOrDefault
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function UpsertAsync(TEntity As tr_cuti_besar) As Task(Of ResponseModel) Implements ICutiBesarService.UpsertAsync
        _unitOfWork.BeginTransaction()
        Try
            ' Validate input
            Dim validationResult = ValidateCutiBesar(TEntity)
            If Not validationResult.Success Then
                _unitOfWork.RollbackTransaction()
                Return validationResult
            End If

            If TEntity.Cabang_id = Nothing Then
                ' Handle multi-branch cuti besar
                Await HandleMultiBranchCutiBesar(TEntity)
            Else
                ' Handle single branch cuti besar
                Await HandleSingleBranchCutiBesar(TEntity)
            End If

            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil disimpan", TEntity)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ICutiBesarService.DeleteAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(Id)
            If entity Is Nothing Then
                _unitOfWork.RollbackTransaction()
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If entity.Posted Then
                _unitOfWork.RollbackTransaction()
                Return ResponseModel.FailureResponse("Data yang sudah diposting tidak bisa dihapus")
            End If

            ' Delete related line items first if any
            Await _unitOfWork.Repository(Of tr_cuti_besar_line)().Delete(Function(x) x.CutiBesar_id = Id)

            ' Delete main record
            Await _unitOfWork.Repository(Of tr_cuti_besar)().DeleteAsync(Id)

            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil dihapus", Id)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function PostingAsync(TEntity As tr_cuti_besar) As Task(Of ResponseModel) Implements ICutiBesarService.PostingAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(TEntity.Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If entity.Posted Then
                Return ResponseModel.FailureResponse("Data sudah diposting sebelumnya")
            End If

            If entity.Status <> "DRAFT" Then
                Return ResponseModel.FailureResponse("Status dokumen harus DRAFT")
            End If

            entity.Posted = True
            entity.Status = "POSTED"
            entity.PostedBy = TEntity.PostedBy
            entity.PostedDate = TEntity.PostedDate

            _unitOfWork.Repository(Of tr_cuti_besar)().Update(entity)
            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil diposting", entity)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Public Async Function VoidAsync(Id As Integer, Reason As String) As Task(Of ResponseModel) Implements ICutiBesarService.VoidAsync
        _unitOfWork.BeginTransaction()
        Try
            Dim entity = Await _unitOfWork.Repository(Of tr_cuti_besar)().Get(Id)
            If entity Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If Not entity.Posted Then
                Return ResponseModel.FailureResponse("Data yang belum diposting tidak bisa divoid")
            End If

            entity.Posted = False
            entity.Status = "VOID"
            entity.PostedBy = String.Empty
            entity.PostedDate = Nothing
            entity.Keterangan = If(String.IsNullOrEmpty(entity.Keterangan), Reason, entity.Keterangan & " [VOID: " & Reason & "]")

            _unitOfWork.Repository(Of tr_cuti_besar)().Update(entity)
            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()
            Return ResponseModel.SuccessResponse("Data berhasil divoid", entity)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Private Function ValidateCutiBesar(entity As tr_cuti_besar) As ResponseModel
        If entity Is Nothing Then
            Return ResponseModel.FailureResponse("Data cuti besar tidak valid")
        End If

        If String.IsNullOrEmpty(entity.Area_id) Then
            Return ResponseModel.FailureResponse("Area harus diisi")
        End If

        If entity.Tanggal = Nothing Then
            Return ResponseModel.FailureResponse("Tanggal harus diisi")
        End If

        If entity.Tahun <= 0 Then
            Return ResponseModel.FailureResponse("Tahun harus diisi")
        End If

        Return ResponseModel.SuccessResponse("Validasi berhasil", entity)
    End Function

    Private Async Function HandleMultiBranchCutiBesar(entity As tr_cuti_besar) As Task
        ' Get area with branches
        Dim area = _myFunction.tm_area(entity.Area_id)

        If area Is Nothing Then
            Throw New Exception("Area tidak ditemukan")
        End If

        ' Create list for batch insert
        Dim cutiBesarList = New List(Of tr_cuti_besar)

        For Each cabang In area.tm_cabang
            Dim cutiBesar = Await CreateCutiBesar(entity, cabang.Id)
            cutiBesar.Total = cutiBesar.tr_cuti_besar_line.Sum(Function(f) f.Total)
            cutiBesarList.Add(cutiBesar)
        Next

        ' Batch insert for better performance
        If cutiBesarList.Any() Then
            Await _unitOfWork.Repository(Of tr_cuti_besar)().AddRangeAsync(cutiBesarList)
        End If
    End Function

    Private Async Function HandleSingleBranchCutiBesar(entity As tr_cuti_besar) As Task
        Dim cutiBesar = Await CreateCutiBesar(entity, entity.Cabang_id)

        If cutiBesar.Id = 0 Then
            Await _unitOfWork.Repository(Of tr_cuti_besar)().AddAsync(cutiBesar)
        Else
            ' Get existing data
            Dim existing = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking.Where(Function(x) x.Id = cutiBesar.Id).FirstOrDefault

            If existing Is Nothing Then
                Throw New Exception("Data tidak ditemukan")
            End If

            ' Update only changed properties
            UpdateCutiBesar(existing, cutiBesar)
            existing.Total = cutiBesar.tr_cuti_besar_line.Sum(Function(f) f.Total)
            _unitOfWork.Repository(Of tr_cuti_besar)().Update(existing)
        End If
    End Function

    Private Sub UpdateCutiBesar(existing As tr_cuti_besar, updated As tr_cuti_besar)
        ' Update only if changed
        If existing.Tanggal <> updated.Tanggal Then
            existing.Tanggal = updated.Tanggal
        End If

        If existing.Tahun <> updated.Tahun Then
            existing.Tahun = updated.Tahun
        End If

        If existing.Cabang_id <> updated.Cabang_id Then
            existing.Cabang_id = updated.Cabang_id
        End If

        existing.Keterangan = updated.Keterangan
        existing.Total = updated.Total
        existing.ModifiedBy = updated.ModifiedBy
        existing.ModifiedDate = updated.ModifiedDate
    End Sub

    Private Async Function CreateCutiBesar(source As tr_cuti_besar, cabangId As String) As Task(Of tr_cuti_besar)
        Try
            Dim cutiBesar = tr_cuti_besar.Create(source)
            cutiBesar.Cabang_id = cabangId

            If cutiBesar.Id = 0 Then
                ' Set properties for new record
                cutiBesar.NoCutiBesar = Await GenerateNoCutiBesar(source.Area_id, source.Tahun)
                cutiBesar.Status = "DRAFT"
                cutiBesar.Posted = False
            End If

            ' Get eligible employees for cuti besar
            Dim eligibleEmployees = GetEligibleEmployeesForCutiBesar(cabangId, source.Tahun)

            For Each employee In eligibleEmployees
                Dim line = Await CreateCutiBesarLine(employee, source.Tahun)
                If line IsNot Nothing Then
                    cutiBesar.tr_cuti_besar_line.Add(line)
                End If
            Next

            Return cutiBesar
        Catch ex As Exception
            Throw New Exception($"Error creating cuti besar for cabangId {cabangId}: {ex.Message}", ex)
        End Try
    End Function

    Private Function GetEligibleEmployeesForCutiBesar(cabangId As String, tahun As Integer) As List(Of tm_karyawan)
        ' Get employees who are eligible for cuti besar (worked for at least 6 years)
        Dim cutoffDate = New DateTime(tahun, 1, 1).AddYears(-6)

        Return _unitOfWork.Repository(Of tm_karyawan)() _
            .TableNoTracking _
            .Where(Function(x) x.Cabang_id = cabangId AndAlso
                              x.Status = "AKTIF" AndAlso
                              x.TanggalMasuk.HasValue AndAlso
                              x.TanggalMasuk.Value <= cutoffDate) _
            .ToList()
    End Function

    Private Async Function CreateCutiBesarLine(employee As tm_karyawan, tahun As Integer) As Task(Of tr_cuti_besar_line)
        Try
            ' Calculate years of service
            Dim yearsOfService = CalculateYearsOfService(employee.TanggalMasuk.Value, New DateTime(tahun, 12, 31))

            ' Calculate cuti besar days based on years of service
            Dim cutiBesarDays = CalculateCutiBesarDays(yearsOfService)

            If cutiBesarDays > 0 Then
                ' Get employee's current salary data
                Dim dataDinas = _unitOfWork.Repository(Of tm_karyawan_datadinas)() _
                    .TableNoTracking _
                    .Where(Function(x) x.Karyawan_id = employee.Id) _
                    .OrderByDescending(Function(x) x.Id) _
                    .FirstOrDefault()

                Dim gajiPokok As Decimal = If(dataDinas?.Pd_GajiPokok, 0)
                Dim tJabatan As Decimal = If(dataDinas?.Pd_T_Jabatan, 0)
                Dim tSusu As Decimal = If(dataDinas?.Pd_T_Susu, 0)
                Dim tProbation As Decimal = If(dataDinas?.Pd_T_Probation, 0)
                Dim tKehadiran As Decimal = If(dataDinas?.Pd_T_Kehadiran, 0)
                Dim tMakan As Decimal = If(dataDinas?.Pd_T_Makan, 0)

                ' Calculate total compensation for cuti besar
                Dim totalCompensation = (gajiPokok + tJabatan + tSusu + tProbation + tKehadiran + tMakan) * cutiBesarDays

                Return New tr_cuti_besar_line With {
                    .Id = 0,
                    .Karyawan_id = employee.Id,
                    .TanggalTetap = employee.TanggalMasuk.Value,
                    .LamaKerja = yearsOfService,
                    .Pd_GajiPokok = gajiPokok,
                    .Pd_T_Jabatan = tJabatan,
                    .Pd_T_Susu = tSusu,
                    .Pd_T_Probation = tProbation,
                    .Pd_T_Kehadiran = tKehadiran,
                    .Pd_T_Makan = tMakan,
                    .Total = totalCompensation
                }
            End If

            Return Nothing
        Catch ex As Exception
            Throw New Exception($"Error creating cuti besar line for employee {employee.NIK}: {ex.Message}", ex)
        End Try
    End Function

    Private Function CalculateYearsOfService(startDate As DateTime, endDate As DateTime) As Integer
        Return DateDiff(DateInterval.Year, startDate, endDate)
    End Function

    Private Function CalculateCutiBesarDays(yearsOfService As Integer) As Integer
        ' Standard calculation:
        ' 6-10 years: 12 days
        ' 11-15 years: 18 days
        ' 16-20 years: 24 days
        ' 21+ years: 30 days
        If yearsOfService >= 6 AndAlso yearsOfService <= 10 Then
            Return 12
        ElseIf yearsOfService >= 11 AndAlso yearsOfService <= 15 Then
            Return 18
        ElseIf yearsOfService >= 16 AndAlso yearsOfService <= 20 Then
            Return 24
        ElseIf yearsOfService >= 21 Then
            Return 30
        Else
            Return 0
        End If
    End Function

    Private Async Function GenerateNoCutiBesar(areaId As String, tahun As Integer) As Task(Of String)
        Try
            Dim lastNo = _unitOfWork.Repository(Of tr_cuti_besar)().TableNoTracking _
                .Where(Function(x) x.Area_id = areaId AndAlso x.Tahun = tahun) _
                .Select(Function(x) x.NoCutiBesar) _
                .Where(Function(x) Not String.IsNullOrEmpty(x)) _
                .OrderByDescending(Function(x) x) _
                .FirstOrDefault()

            Dim nextNumber As Integer = 1
            If Not String.IsNullOrEmpty(lastNo) AndAlso lastNo.Length >= 4 Then
                Dim numberPart = lastNo.Substring(lastNo.Length - 4)
                If Integer.TryParse(numberPart, nextNumber) Then
                    nextNumber += 1
                End If
            End If

            Return $"CB/{areaId}/{tahun.ToString().Substring(2)}/{nextNumber:D4}"
        Catch ex As Exception
            Return $"CB/{areaId}/{tahun.ToString().Substring(2)}/0001"
        End Try
    End Function

    ' Method untuk mendapatkan data line cuti besar
    Public Function GetCutiBesarLines(cutiBesarId As Integer) As IQueryable(Of tr_cuti_besar_line)
        Return _unitOfWork.Repository(Of tr_cuti_besar_line)() _
            .TableNoTracking _
            .Where(Function(x) x.CutiBesar_id = cutiBesarId)
    End Function

    ' Method untuk generate nomor dokumen dengan format yang lebih baik
    Private Function GetNoCutiBesar(cabang_id As String, tahun As Integer, Optional format As String = "CB") As String
        Try
            ' Get current date components
            Dim year As String = tahun.ToString().Substring(2)

            ' Get company/branch code if needed
            Dim branchCode As String = _myFunction.tm_cabang(cabang_id).KodeCabang

            ' Get last document number for current year and format
            Dim prefix As String = $"{format}/{branchCode}/{year}/"

            Dim lastDoc = _unitOfWork.Repository(Of tr_cuti_besar)() _
            .TableNoTracking _
            .Where(Function(x) x.NoCutiBesar.StartsWith(prefix)) _
            .OrderByDescending(Function(x) x.NoCutiBesar).Select(Function(s) s.NoCutiBesar) _
            .FirstOrDefault()

            ' Initialize running number
            Dim runningNumber As Integer = 1

            If lastDoc IsNot Nothing Then
                ' Extract running number from last document
                Dim lastNumber As String = lastDoc.Split("/"c).Last()
                If Integer.TryParse(lastNumber, runningNumber) Then
                    runningNumber += 1
                End If
            End If

            ' Format: CB/BRANCH/YY/0001
            Return $"{prefix}{runningNumber.ToString("0000")}"

        Catch ex As Exception
            ' Generate fallback number if needed
            Return $"{format}/{DateTime.Now.ToString("yyMMddHHmmss")}"
        End Try
    End Function

    ' Extension method untuk validasi nomor dokumen
    Public Function IsValidDocumentNumber(docNumber As String) As Boolean
        Try
            ' Basic validation
            If String.IsNullOrEmpty(docNumber) Then Return False

            ' Check format
            Dim parts = docNumber.Split("/"c)
            If parts.Length <> 4 Then Return False

            ' Validate prefix
            If parts(0) <> "CB" Then Return False

            ' Validate year (2 digits)
            Dim year As Integer
            If Not Integer.TryParse(parts(2), year) OrElse year < 0 OrElse year > 99 Then Return False

            ' Validate running number (4 digits)
            Dim number As Integer
            If Not Integer.TryParse(parts.Last(), number) OrElse number < 1 OrElse number > 9999 Then Return False

            Return True

        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Method untuk mendapatkan summary cuti besar per area
    Public Function GetCutiBesarSummary(areaId As String, tahun As Integer) As Object
        Try
            Dim summary = _unitOfWork.Repository(Of tr_cuti_besar)() _
                .TableNoTracking _
                .Where(Function(x) x.Area_id = areaId AndAlso x.Tahun = tahun) _
                .GroupBy(Function(x) x.Status) _
                .Select(Function(g) New With {
                    .Status = g.Key,
                    .Count = g.Count(),
                    .TotalDays = g.Sum(Function(s) s.Total)
                }).ToList()

            Return summary
        Catch ex As Exception
            Return Nothing
        End Try
    End Function
End Class
