﻿Imports DevExpress.Web
Imports HRD.Controller
Imports HRD.Helpers
Imports ILS.MVVM
Public Class frmImportDataKontrak
    Inherits PageBase

    <Create(Scope:=CreateScope.Session, Id:="frmImportDataKontrak")>
    Property Controller As DataDinasController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = (Controller.SelectedItem Is Nothing)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = (Controller.SelectedItem IsNot Nothing)

    End Sub

    <EventSubscription>
    Public Sub OnMsgChanged(sender As Object, e As EventArgs)
        ltl_msg.Text = Controller.sMsg
    End Sub

    Public Sub New()
        MyBase.New("7222")
    End Sub
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
        End If
    End Sub

    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")
        Select Case s(0)
            Case "new"
                Controller.AddNewItem()
            Case "gen_absen"
                'Controller.GenerateAbsensi(Me.ucAbsensi_list.GetCabang_id, Me.ucAbsensi_list.GetTanggal)
            Case "del_absen"
                'Controller.DeleteAbsensi(Me.ucAbsensi_list.GetCabang_id, Me.ucAbsensi_list.GetTanggal)
            Case "reset"
                Controller.Reset()
            Case "save"
                Controller.SaveImportDataKontrak()

                If Controller.Saved Then
                    Session("_data") = Nothing
                    'Me.ucImportAbsensi_list.BindGrid()
                    Reset()
                    Controller.sMsg = MessageFormatter.GetFormattedSuccessMessage("Simpan Sukses!")
                Else
                    Me.OnEditChanged(Nothing, Nothing)
                End If
            Case "btn_edit"
                Controller.Action = Action.Edit
                Controller.SelectItem(s(1))
            Case "btn_view"
                Controller.Action = Action.View
                Controller.SelectItem(s(1))
            Case "btn_delete"
                Controller.Action = Action.Delete
                Controller.DeleteItem(s(1))
            Case "import"
                'Me.ucImportDataKaryawan.BindGrid()
        End Select
    End Sub
End Class