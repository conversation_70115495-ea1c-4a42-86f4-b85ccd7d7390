﻿Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap
Imports System.Collections.Specialized

Public Class DataDinasController
    Inherits Controller(Of tm_karyawan_datadinas)
    Implements IDataDinasListController, IDataDinasEditController

    Public ReadOnly Property DataListEntriSalary(area_id As String, Optional cabang_id As String = Nothing) As IQueryable(Of tm_karyawan_datadinas) Implements IDataDinasListController.DataListEntriSalary
        Get
            Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
            Dim r = Task.Run(Function() serv.GetKaryawansAsync()).Result

            If r.Success Then
                Dim os As IQueryable(Of tm_karyawan) = r.Output
                os = os.Where(Function(f) Not f.Berhenti_b)
                os = os.Where(Function(f) f.Area_id = area_id)
                If cabang_id IsNot Nothing Then
                    os = os.Where(Function(f) f.Cabang_id = cabang_id)
                End If
                Dim osDN = os.Select(Function(s) s.tm_karyawan_datadinas.OrderByDescending(Function(d) d.Id).Take(1).FirstOrDefault()).AsQueryable()
                Return osDN
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property DataListEntriSACuti(cabang_id As String) As IQueryable(Of tm_karyawan_datadinas) Implements IDataDinasListController.DataListEntriSACuti
        Get
            Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
            Dim r = Task.Run(Function() serv.GetKaryawansAsync()).Result

            If r.Success Then
                Dim os As IQueryable(Of tm_karyawan) = r.Output
                os = os.Where(Function(f) Not f.Berhenti_b And f.Cabang_id = cabang_id)
                Dim osDN = os.Select(Function(s) s.tm_karyawan_datadinas.OrderByDescending(Function(d) d.Id).Take(1).FirstOrDefault()).AsQueryable()
                Return osDN
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Private mDataListImport As IList(Of tm_karyawan_datadinas)
    Public ReadOnly Property DataListImport As List(Of tm_karyawan_datadinas) Implements IDataDinasListController.DataListImport
        Get
            If mDataListImport Is Nothing Then
                mDataListImport = New List(Of tm_karyawan_datadinas)
            End If
            Return mDataListImport
        End Get
    End Property

    Public ReadOnly Property DataList As IQueryable(Of tm_karyawan_datadinas) Implements IDataDinasListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of DataDinasService)()
            Dim r = Serv.GetDataDinassAsync.GetAwaiter.GetResult
            If r.Success Then

                Dim _user = AuthHelper.GetLoggedInUserInfo

                Dim os As IQueryable(Of tm_karyawan_datadinas) = r.Output
                os = os.Where(Function(f) _user.AreaIdPermisionList.Contains(f.Area_id))
                Select Case _user.PermisionArea
                    Case 0
                        os = os.Where(Function(f) f.Cabang_id = _user.Cabang_id)
                        'Case 1
                        '    os = os.Where(Function(f) f.Area_id = _user.Area_id)
                End Select


                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tm_karyawan_datadinas With {.Tanggal = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()
        Dim r = serv.GetDataDinasByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()
        Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_karyawan_datadinas)

        'ExpressMapper.Mapper.Register(Of tm_jabatan, tm_jabatan)()
        'Dim o = ExpressMapper.Mapper.Map(Of tm_jabatan, tm_jabatan)(TEntity)

        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()

        If TEntity.Tetap <> True Then
            If TEntity.NoKontrakKerja = Nothing Then
                sMsg = MessageFormatter.GetFormattedMessage("No Kontrak Kerja harus diisi")
                Saved = False
                Return
            End If
        End If
        TEntity.tm_area = Nothing
        TEntity.tm_cabang = Nothing
        TEntity.tm_department = Nothing
        TEntity.tm_jabatan = Nothing
        TEntity.tm_karyawan = Nothing

        If TEntity.Id <= 0 Then
            TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
            If TEntity.Pd_GajiPokok = 0 Then
                Dim rOlds = serv.GetDataDinassAsync.GetAwaiter.GetResult
                If rOlds.Success Then
                    Dim dnOlds As IQueryable(Of tm_karyawan_datadinas) = rOlds.Output
                    Dim oldDN = dnOlds.Where(Function(f) f.Pd_GajiPokok > 0 And f.Karyawan_id = TEntity.Karyawan_id).OrderByDescending(Function(x) x.Id).Take(1).FirstOrDefault
                    If oldDN IsNot Nothing Then

                        With TEntity
                            .Pd_GajiPokok = oldDN.Pd_GajiPokok
                            .Pd_T_Jabatan = oldDN.Pd_T_Jabatan
                            .Pd_T_Makan = oldDN.Pd_T_Makan
                            .Pd_T_PremiHadir = oldDN.Pd_T_PremiHadir
                            .Pd_T_Susu = oldDN.Pd_T_Susu
                            .Pd_T_Transport = oldDN.Pd_T_Transport
                            .Pd_T_Kontrak = oldDN.Pd_T_Kontrak
                            .Pd_T_Probation = oldDN.Pd_T_Probation
                            .Pd_T_PremiAss = oldDN.Pd_T_PremiAss
                            .Pd_T_Pajak = oldDN.Pd_T_Pajak
                            .Pd_T_JKK = oldDN.Pd_T_JKK
                            .Pd_T_JKM = oldDN.Pd_T_JKM
                            .Pd_T_JHT = oldDN.Pd_T_JHT
                            .Pd_T_JPK = oldDN.Pd_T_JPK
                            .Pd_T_JP = oldDN.Pd_T_JP
                            .TotalPendapatan = oldDN.TotalPendapatan
                            .Pt_P_JKK = oldDN.Pt_P_JKK
                            .Pt_P_JKM = oldDN.Pt_P_JKM
                            .Pt_P_JHT = oldDN.Pt_P_JHT
                            .Pt_P_JPK = oldDN.Pt_P_JPK
                            .Pt_P_JP = oldDN.Pt_P_JP
                            .Pt_K_JHT = oldDN.Pt_K_JHT
                            .Pt_K_JPK = oldDN.Pt_K_JPK
                            .Pt_K_JP = oldDN.Pt_K_JP
                            .Pt_K_JPK_Mandiri = oldDN.Pt_K_JPK_Mandiri
                            .Pt_PPH21 = oldDN.Pt_PPH21
                            .Pt_SP = oldDN.Pt_SP
                            .Pt_SerPekerja = oldDN.Pt_SerPekerja
                            .TotalPotongan = oldDN.TotalPotongan
                            .THP = oldDN.THP
                            .SaldoAwalCuti = oldDN.SaldoAwalCuti
                        End With
                    End If
                End If

            End If
        Else
            TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.ModifiedDate = Now
        End If


        Dim r = serv.UpsertAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Public Sub UpdateDataCuti(id As Integer, saCuti As Integer) Implements IDataDinasListController.UpdateDataCuti

        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()

        Dim r = serv.GetDataDinasByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then

            Dim o As tm_karyawan_datadinas = r.Output
            o.SaldoAwalCuti = saCuti
            Dim r2 = serv.UpsertAsync(o).GetAwaiter.GetResult

            If Not r2.Success Then
                sMsg = MessageFormatter.GetFormattedErrorMessage(r2.Message)
            End If
        Else

        End If

    End Sub

    Public Sub UpdateDataCutiBulk(updates As List(Of KeyValuePair(Of String, String))) Implements IDataDinasListController.UpdateDataCutiBulk
        Using dbContext As New HRDEntities ' Replace YourDbContext with your actual DbContext
            For Each updatePair In updates
                Dim id As Integer = Integer.Parse(updatePair.Key)
                Dim newCuti As Integer = Integer.Parse(updatePair.Value)
                Dim record = dbContext.tm_karyawan_datadinas.FirstOrDefault(Function(d) d.Id = id)
                If record IsNot Nothing Then
                    record.SaldoAwalCuti = newCuti
                End If
            Next
            dbContext.SaveChanges()
        End Using
    End Sub

    Public Sub SaveImport()
        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()
        Dim r = serv.SaveImportAsync(DataListImport).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Public Function GetDataDinasByNIK(nik As String) As tm_karyawan_datadinas Implements IDataDinasListController.GetDataDinasByNIK
        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()
        Dim r = serv.GetDataDinasByNIKAsync(nik).GetAwaiter.GetResult
        If r.Success Then
            Return r.Output
        End If
        Return Nothing
    End Function

    Public Sub SaveImportDataKontrak()
        Dim serv = ObjectFactory.GetInstance(Of DataDinasService)()
        Dim r = serv.SaveImportDataKontrakAsync(DataListImport).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IDataDinasListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataListEntriSalary(area_id As String, Optional cabang_id As String = Nothing) As IQueryable(Of tm_karyawan_datadinas)
    ReadOnly Property DataListEntriSACuti(cabang_id As String) As IQueryable(Of tm_karyawan_datadinas)
    ReadOnly Property DataListImport As List(Of tm_karyawan_datadinas)
    Sub UpdateDataCuti(id As Integer, saCuti As Integer)
    ' In IDataDinasListController.vb
    Sub UpdateDataCutiBulk(updates As List(Of KeyValuePair(Of String, String)))
    Function GetDataDinasByNIK(nik As String) As tm_karyawan_datadinas
    ReadOnly Property DataList() As IQueryable(Of tm_karyawan_datadinas)
End Interface
Public Interface IDataDinasEditController
    Inherits IControllerMain, IControllerEdit(Of tm_karyawan_datadinas)

End Interface