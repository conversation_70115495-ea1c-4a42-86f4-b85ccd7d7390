﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmEventSettingRelease.aspx.vb" Inherits="HRDv23.frmEventSettingRelease" %>

<%@ Register Src="~/Views/HRDandPayroll/Master/ucEventSetting_release.ascx" TagPrefix="uc1" TagName="ucEventSetting_release" %>
<%@ Register Src="~/Views/HRDandPayroll/Master/ucEventSetting_edit.ascx" TagPrefix="uc1" TagName="ucEventSetting_edit" %>


<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_event_setting_release" Width="100%">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Caption="List" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucEventSetting_release runat="server" ID="ucEventSetting_release" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Edit" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucEventSetting_edit runat="server" ID="ucEventSetting_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
