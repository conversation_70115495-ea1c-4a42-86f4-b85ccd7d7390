﻿Imports System.IO
Imports DevExpress.Web
Imports HRD.Helpers
Imports OfficeOpenXml

Public Class ucImportAbsensi_list
    Inherits System.Web.UI.UserControl

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
        Else
            Me.BindGrid()
        End If
    End Sub
    Sub BindGrid()
        Me.ASPxGridView1.DataSource = Session("_data")
        Me.ASPxGridView1.DataBind()
    End Sub
    Private Sub ASPxUploadControl1_FileUploadComplete(sender As Object, e As FileUploadCompleteEventArgs) Handles ASPxUploadControl1.FileUploadComplete
        'Dim upload As ASPxUploadControl = TryCast(sender, ASPxUploadControl)
        'If upload.HasFile Then
        '    If Path.GetExtension(upload.FileName).Equals(".xlsx") Then

        '        Dim excel = New ExcelPackage(upload.FileContent)

        '        Dim dt = excel.ToDataTable
        '        Session("_data") = dt
        '    End If
        '    If Path.GetExtension(upload.FileName).Equals(".xls") Then
        '        Dim excel = New ExcelPackage(upload.FileContent)

        '        Dim dt = excel.ToDataTable
        '        Session("_data") = dt
        '    End If
        'End If


        Dim fileName As String = e.UploadedFile.FileName
        Dim fileExtension As String = System.IO.Path.GetExtension(fileName)
        Dim savePath As String = Server.MapPath("~/Content/Uploads/")
        Dim fileSavePath As String = savePath & fileName
        If Not System.IO.Directory.Exists(savePath) Then
            System.IO.Directory.CreateDirectory(savePath)
        End If
        e.UploadedFile.SaveAs(fileSavePath)
        Dim dt As DataTable = MyMethod.ReadExcelFile(fileSavePath)



        Session("_data") = dt
    End Sub
End Class