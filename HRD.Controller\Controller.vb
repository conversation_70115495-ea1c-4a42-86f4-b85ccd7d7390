﻿

Imports HRD.Controller
Imports HRD.Domain
Public MustInherit Class Controller(Of T)
    Implements IController(Of T)

    Public Property Action As Action Implements IControllerMain.Action

    Private _sMsg As String
    Public Property sMsg As String Implements IControllerMain.sMsg
        Get
            Return _sMsg
        End Get
        Set(value As String)
            _sMsg = value
            RaiseEvent OnMsgChanged(Me, EventArgs.Empty)
        End Set
    End Property

    Public Property Saved As Boolean Implements IControllerMain.Saved

    Private _SelectedItem As T
    Public Property SelectedItem As T Implements IControllerEdit(Of T).SelectedItem
        Get
            Return _SelectedItem
        End Get
        Set(value As T)
            _SelectedItem = value
            RaiseEvent OnEditChanged(Me, EventArgs.Empty)
        End Set
    End Property

    Private _SelectedPrint As T
    Public Property SelectedPrint As T
        Get
            Return _SelectedPrint
        End Get
        Set(value As T)
            _SelectedPrint = value
            RaiseEvent OnPrintChanged(Me, EventArgs.Empty)
        End Set
    End Property


    Public Property ObjectID As String Implements IControllerEdit(Of T).ObjectID

    Public Event OnMsgChanged As EventHandler Implements IControllerMain.OnMsgChanged
    Public Event OnListChanged As EventHandler Implements IControllerList.OnListChanged
    Public Event OnReset As EventHandler Implements IControllerEdit(Of T).OnReset
    Public Event OnEditChanged As EventHandler Implements IControllerEdit(Of T).OnEditChanged
    Public Event OnPrintChanged As EventHandler
    Public MustOverride Sub AddNewItem() Implements IControllerList.AddNewItem


    Public MustOverride Sub SelectItem(id As String) Implements IControllerList.SelectItem

    Public MustOverride Sub DeleteItem(id As String) Implements IControllerList.DeleteItem

    Public MustOverride Sub Saving() Implements IControllerEdit(Of T).Saving

    Public Sub Reset() Implements IControllerEdit(Of T).Reset
        RaiseEvent OnReset(Me, EventArgs.Empty)
        If sMsg <> "" Then
            sMsg = ""
        End If
        If SelectedItem IsNot Nothing Then
            SelectedItem = Nothing
        End If
        RaiseEvent OnListChanged(Me, EventArgs.Empty)
    End Sub

    Public Function GetMessageErrCtx(mCtx As HRDEntities) As String
        Dim s As String = Nothing
        For Each x In mCtx.GetValidationErrors
            For Each y In x.ValidationErrors
                s &= String.Format("{0}{1}", y.ErrorMessage, "</BR>")
            Next
        Next
        Return s
    End Function

    Public MustOverride Sub Saving(TEntity As T) Implements IControllerEdit(Of T).Saving

End Class


Public Interface IController(Of T)
    Inherits IControllerMain, IControllerList, IControllerEdit(Of T)

End Interface
Public Interface IControllerMain
    Event OnMsgChanged As EventHandler

    Property Action As Action
    Property sMsg As String
    Property Saved As Boolean


End Interface
Public Interface IControllerList

    Event OnListChanged As EventHandler
    Sub AddNewItem()
    Sub SelectItem(id As String)
    Sub DeleteItem(id As String)
End Interface
Public Interface IControllerEdit(Of T)
    Event OnReset As EventHandler
    Event OnEditChanged As EventHandler
    Property SelectedItem As T
    Property ObjectID As String
    Sub Saving()
    Sub Saving(TEntity As T)
    Sub Reset()
End Interface
Public Enum Action
    None
    AddNew
    Edit
    Delete
    View
    Posting
    Repos
    Invoice
    ReValue
    ApprovalDisc
    Booking
    SuratJalan
    Produce
    Closed
    Cancel
    UnPosting
    ChangePassword
End Enum