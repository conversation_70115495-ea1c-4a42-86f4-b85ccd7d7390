﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web

Public Class ucRegPinjaman_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IRegisterPinjamanListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.DefaultSorting = "Id Desc"

        e.QueryableSource = Controller.DataList(chk_release.Checked, cb_status.Value).AsQueryable
    End Sub

    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim b As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Posted")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(b, DevExpress.Utils.DefaultBoolean.False, DevExpress.Utils.DefaultBoolean.True)
            Case "btn_view"
                e.Visible = If(b, DevExpress.Utils.DefaultBoolean.True, DevExpress.Utils.DefaultBoolean.False)


        End Select
    End Sub
End Class