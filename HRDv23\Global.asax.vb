﻿Imports System.Reflection
Imports System.Security.Principal
Imports System.Web.Routing
Imports System.Web.Services.Description
Imports System.Web.SessionState
Imports DevExpress.Entity.Model.Metadata
Imports DevExpress.Web
Imports HRD.Application
Imports HRD.Domain
Imports HRD.Infrastructure

Public Class Global_asax
    Inherits System.Web.HttpApplication
    Sub Application_Start(ByVal sender As Object, ByVal e As EventArgs)
        AddHandler DevExpress.Web.ASPxWebControl.CallbackError, AddressOf Application_Error
        DevExpress.Security.Resources.AccessSettings.DataResources.SetRules(
            DevExpress.Security.Resources.DirectoryAccessRule.Allow(Server.MapPath("~/Content")),
            DevExpress.Security.Resources.UrlAccessRule.Allow()
        )

        ASPxWebControl.BackwardCompatibility.DataEditorsPreventLoadClientValuesForReadOnlyControls = False

        RegisterRoutes(RouteTable.Routes)


        BootStrapper.ConfigureDependencies()



        MapperConfig.RegisterMappings()

        DevExpress.XtraReports.Web.ASPxWebDocumentViewer.StaticInitialize()







        DevExpress.XtraReports.Web.ASPxWebDocumentViewer.StaticInitialize()

    End Sub


    Sub Session_Start(ByVal sender As Object, ByVal e As EventArgs)
        ' Fires when the session is started
    End Sub

    Sub Application_BeginRequest(ByVal sender As Object, ByVal e As EventArgs)
        ' Fires at the beginning of each request
    End Sub

    Sub Application_AuthenticateRequest(ByVal sender As Object, ByVal e As EventArgs)
        '' Fires upon attempting to authenticate the user
        'Dim user = HttpContext.Current.User

        'If user IsNot Nothing AndAlso user.Identity.IsAuthenticated AndAlso TypeOf user.Identity Is FormsIdentity Then
        '    Dim id As FormsIdentity = DirectCast(user.Identity, FormsIdentity)
        '    Dim ticket As FormsAuthenticationTicket = id.Ticket

        '    ' Get the stored user-data, in this case, our roles
        '    Dim userData As String = ticket.UserData
        '    Dim roles As String() = userData.Split(","c)
        '    HttpContext.Current.User = New GenericPrincipal(id, roles)
        'End If

        ' Fires upon attempting to authenticate the use
        If HttpContext.Current.User IsNot Nothing Then
            If HttpContext.Current.User.Identity.IsAuthenticated Then
                If TypeOf HttpContext.Current.User.Identity Is FormsIdentity Then
                    Dim id As FormsIdentity = DirectCast(HttpContext.Current.User.Identity, FormsIdentity)
                    Dim ticket As FormsAuthenticationTicket = id.Ticket

                    ' Get the stored user-data, in this case, our roles
                    Dim userData As String = ticket.UserData
                    Dim roles As String() = userData.Split(","c)
                    HttpContext.Current.User = New GenericPrincipal(id, roles)
                End If
            End If
        End If
    End Sub

    Sub Application_Error(ByVal sender As Object, ByVal e As EventArgs)
        ' Fires when an error occurs
    End Sub

    Sub Session_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Fires when the session ends
    End Sub

    Sub Application_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Fires when the application ends
    End Sub
    Private Shared Sub RegisterRoutes(ByVal routes As RouteCollection)
        routes.Ignore("{resource}.axd/{*pathInfo}")

        routes.Add("Login", New Route("Login", New PageRouteHandler("~/Account/SignIn.aspx")))
        routes.Add("GantiPassword", New Route("GantiPassword", New PageRouteHandler("~/Account/frmGantiPassword.aspx")))

        routes.Add("Role", New Route("Setting/Role", New PageRouteHandler("~/Forms/Setting/frmRole.aspx")))
        routes.Add("User", New Route("Setting/User", New PageRouteHandler("~/Forms/Setting/frmUser.aspx")))
        routes.Add("Cabang", New Route("Setting/Cabang", New PageRouteHandler("~/Forms/Setting/frmCabang.aspx")))
        routes.Add("Area", New Route("Setting/Area", New PageRouteHandler("~/Forms/Setting/frmArea.aspx")))

        'routes.Add("GL-Setup", New Route("GL/Master/GLSetup", New PageRouteHandler("~/Forms/BankDanGL/Master/frmGLSetting.aspx")))

        'routes.Add("Inv-Transaksi-MutasiKeluar", New Route("Inv/Transaksi/MutasiKeluar", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmMutasiKeluar.aspx")))
        'routes.Add("Inv-Transaksi-MutasiKeluar-Release", New Route("Inv/Transaksi/MutasiKeluar/Release", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmMutasiKeluarRelease.aspx")))
        'routes.Add("Inv-Transaksi-MutasiMasuk", New Route("Inv/Transaksi/MutasiMasuk", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmMutasiMasuk.aspx")))
        'routes.Add("Inv-Transaksi-MutasiMasukRelease", New Route("Inv/Transaksi/MutasiMasuk/Release", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmMutasiMasukRelease.aspx")))
        'routes.Add("Inv-Transaksi-Adjustment", New Route("Inv/Transaksi/AdjustmentQty", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmAdjustmentQty.aspx")))
        'routes.Add("Inv-Transaksi-Adjustment-Release", New Route("Inv/Transaksi/AdjustmentQty/Release", New PageRouteHandler("~/Forms/Inventory/Transaksi/frmAdjustmentQtyRelease.aspx")))

        'routes.Add("Inv-Maintenance-ItemMaster", New Route("Inv/Maintenance/ItemMaster", New PageRouteHandler("~/Forms/Inventory/Maintenance/frmItemMaster.aspx")))
#Region "Purchasing"
        'routes.Add("Purch-PO", New Route("Purchasing/Transaksi/PO", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPO.aspx")))
        'routes.Add("Purch-POImport", New Route("Purchasing/Transaksi/POImport", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOImport.aspx")))
        'routes.Add("Purch-PO-Release", New Route("Purchasing/Transaksi/PO/Release", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPORelease.aspx")))
        'routes.Add("Purch-POImport-Release", New Route("Purchasing/Transaksi/POImport/Release", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOImportRelease.aspx")))
        'routes.Add("Purch-POReceive", New Route("Purchasing/Transaksi/POReceive", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOReceive.aspx")))
        'routes.Add("Purch-POReceive-Release", New Route("Purchasing/Transaksi/POReceive/Release", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOReceiveRelease.aspx")))
        'routes.Add("Purch-POInvoice", New Route("Purchasing/Transaksi/POInvoice", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOInvoice.aspx")))
        'routes.Add("Purch-POInvoice-Release", New Route("Purchasing/Transaksi/POInvoice/Release", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOInvoiceRelease.aspx")))
        'routes.Add("Purch-POPayment", New Route("Purchasing/Transaksi/POPayment", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOPayment.aspx")))
        'routes.Add("Purch-POPayment-Release", New Route("Purchasing/Transaksi/POPayment/Release", New PageRouteHandler("~/Forms/Pembelian/Transaksi/frmPOPaymentRelease.aspx")))

        'routes.Add("Purch-Laporan-ListToBeOrder", New Route("Purchasing/Laporan/ListToBeOrder", New PageRouteHandler("~/Forms/Pembelian/Laporan/frmLapToBeOrder.aspx")))

        'routes.Add("Purch-Maintenance-Supplier", New Route("Purchasing/Maintenance/Supplier", New PageRouteHandler("~/Forms/Pembelian/Maintenance/frmSupplier.aspx")))
        'routes.Add("Purch-Maintenance-GrupSupplier", New Route("Purchasing/Maintenance/GrupSupplier", New PageRouteHandler("~/Forms/Pembelian/Maintenance/frmGrupSupplier.aspx")))
#End Region

#Region "Sales"
        'routes.Add("Sales-Transaksi-SO", New Route("Sales/Transaksi/SO", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSO.aspx")))
        'routes.Add("Sales-Transaksi-SO-NeedApproval", New Route("Sales/Transaksi/SO/NeedApproval", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSONeedApproval.aspx")))
        'routes.Add("Sales-Transaksi-SO-Release", New Route("Sales/Transaksi/SO/Release", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSORelease.aspx")))
        'routes.Add("Sales-Transaksi-SuratJalan", New Route("Sales/Transaksi/SuratJalan", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSJ.aspx")))
        'routes.Add("Sales-Transaksi-SuratJalan-Release", New Route("Sales/Transaksi/SuratJalan/Release", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSJRelease.aspx")))
        'routes.Add("Sales-Transaksi-SOInvoice", New Route("Sales/Transaksi/SOInvoice", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSOInvoice.aspx")))
        'routes.Add("Sales-Transaksi-SOInvoice-Release", New Route("Sales/Transaksi/SOInvoice/Release", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSOInvoiceRelease.aspx")))
        'routes.Add("Sales-Transaksi-SOPayment", New Route("Sales/Transaksi/SOPayment", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSOPayment.aspx")))
        'routes.Add("Sales-Transaksi-SOPayment-Release", New Route("Sales/Transaksi/SOPayment/Release", New PageRouteHandler("~/Forms/Penjualan/Transaksi/frmSOPaymentRelease.aspx")))

        'routes.Add("Produksi-BOM", New Route("Produksi/BOM", New PageRouteHandler("~/Forms/Produksi/frmBOM.aspx")))
        'routes.Add("Produksi-BOM-Release", New Route("Produksi/BOM/Release", New PageRouteHandler("~/Forms/Produksi/frmBOMRelease.aspx")))
        'routes.Add("Produksi-SPKProduksi", New Route("Produksi/SPKProduksi", New PageRouteHandler("~/Forms/Produksi/frmSPKProduksi.aspx")))
        'routes.Add("Produksi-SPKProduksi-Release", New Route("Produksi/SPKProduksi/Release", New PageRouteHandler("~/Forms/Produksi/frmSPKProduksiRelease.aspx")))
        'routes.Add("Produksi-Produksi", New Route("Produksi/Produksi", New PageRouteHandler("~/Forms/Produksi/frmProduksi.aspx")))
        'routes.Add("Produksi-Produksi-Release", New Route("Produksi/Produksi/Release", New PageRouteHandler("~/Forms/Produksi/frmProduksiRelease.aspx")))
        'routes.Add("Inv-Inquery-Stock", New Route("Inv/Inquery/Stock", New PageRouteHandler("~/Forms/Inventory/InqueryDanReport/frmInqueryStock.aspx")))

        'routes.Add("Sales-Retur-Retur", New Route("Sales/Retur/Retur", New PageRouteHandler("~/Forms/Penjualan/Retur/frmSOReturn.aspx")))
        'routes.Add("Sales-Retur-Retur-Release", New Route("Sales/Retur/Retur/Release", New PageRouteHandler("~/Forms/Penjualan/Retur/frmSOReturRelease.aspx")))
        'routes.Add("Sales-Retur-ReturPayment", New Route("Sales/Retur/ReturPayment", New PageRouteHandler("~/Forms/Penjualan/Retur/frmSOReturnPayment.aspx")))
        'routes.Add("Sales-Retur-ReturPayment-Release", New Route("Sales/Retur/ReturPayment/Release", New PageRouteHandler("~/Forms/Penjualan/Retur/frmSOReturnPaymentRelease.aspx")))

        'routes.Add("Sales-Laporan-AR", New Route("Sales/Laporan/AR", New PageRouteHandler("~/Forms/Penjualan/Laporan/frmLaporanAR.aspx")))
        'routes.Add("Sales-Laporan-Penjualan", New Route("Sales/Laporan/Penjualan", New PageRouteHandler("~/Forms/Penjualan/Laporan/frmLaporanPenjualan.aspx")))
        'routes.Add("Sales-Laporan-Pencairan", New Route("Sales/Laporan/Pencairan", New PageRouteHandler("~/Forms/Penjualan/Laporan/frmLaporanPencairan.aspx")))

        'routes.Add("Sales-Maintenance-GrupCustomer", New Route("Sales/Maintenance/GrupCustomer", New PageRouteHandler("~/Forms/Penjualan/Maintenance/frmGrupCustomer.aspx")))
        'routes.Add("Sales-Maintenance-Customer", New Route("Sales/Maintenance/Customer", New PageRouteHandler("~/Forms/Penjualan/Maintenance/frmCustomer.aspx")))
        'routes.Add("Sales-Maintenance-Price", New Route("Sales/Maintenance/Price", New PageRouteHandler("~/Forms/Penjualan/Maintenance/frmPrice.aspx")))
        'routes.Add("Sales-Maintenance-Price-Release", New Route("Sales/Maintenance/Price/Release", New PageRouteHandler("~/Forms/Penjualan/Maintenance/frmPriceRelease.aspx")))
        'routes.Add("Sales-Maintenance-Salesman", New Route("Sales/Maintenance/Salesman", New PageRouteHandler("~/Forms/Penjualan/Maintenance/frmSalesman.aspx")))
#End Region

#Region "Bank Dan GL"
        'routes.Add("GL-Transaksi-BankKeluar", New Route("GL/Transaksi/BankKeluar", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmBankKeluar.aspx")))
        'routes.Add("GL-Transaksi-BankKeluar-Release", New Route("GL/Transaksi/BankKeluar/Release", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmBankKeluarRelease.aspx")))
        'routes.Add("GL-Transaksi-BankMasuk", New Route("GL/Transaksi/BankMasuk", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmBankMasuk.aspx")))
        'routes.Add("GL-Transaksi-BankMasuk-Release", New Route("GL/Transaksi/BankMasuk/Release", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmBankMasukRelease.aspx")))
        'routes.Add("GL-Transaksi-JU", New Route("GL/Transaksi/JU", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmJU.aspx")))
        'routes.Add("GL-Transaksi-JU-Release", New Route("GL/Transaksi/JU/Release", New PageRouteHandler("~/Forms/BankDanGL/Transaksi/frmJURelease.aspx")))

        'routes.Add("GL-Inquery-InqueryGL", New Route("GL/Inquery/InqueryGL", New PageRouteHandler("~/Forms/BankDanGL/InqueryDanReport/frmInqueryGL.aspx")))
        'routes.Add("GL-Laporan-TransaksiPerAkun", New Route("GL/Laporan/TransaksiPerAkun", New PageRouteHandler("~/Forms/BankDanGL/InqueryDanReport/frmTransaksiPerAkun.aspx")))
        'routes.Add("GL-Laporan-Persediaan", New Route("GL/Laporan/Persediaan", New PageRouteHandler("~/Forms/BankDanGL/InqueryDanReport/frmLapPersediaan.aspx")))
        'routes.Add("GL-Laporan-KeuTahunan", New Route("GL/Laporan/KeuTahunan", New PageRouteHandler("~/Forms/BankDanGL/InqueryDanReport/frmLapKeuanganTahunan.aspx")))

        'routes.Add("GL-Kasir-TTUMS", New Route("GL/Kasir/TTUMS", New PageRouteHandler("~/Forms/BankDanGL/Kasir/frmTTUMS.aspx")))
        'routes.Add("GL-Kasir-TTUMS-Release", New Route("GL/Kasir/TTUMS/Release", New PageRouteHandler("~/Forms/BankDanGL/Kasir/frmTTUMSRelease.aspx")))

        'routes.Add("GL-Master-Bank", New Route("GL/Master/Bank", New PageRouteHandler("~/Forms/BankDanGL/Master/frmBank.aspx")))
#End Region


#Region "HRD And Payroll"
        routes.Add("HRD-Master-Karyawan", New Route("HRD/Master/Karyawan", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmKaryawan.aspx")))
        routes.Add("HRD-Master-HLibur", New Route("HRD/Master/HLibur", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmLiburNasional.aspx")))
        routes.Add("HRD-Master-Pendidikan", New Route("HRD/Master/Pendidikan", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmPendidikan.aspx")))
        routes.Add("HRD-Master-Department", New Route("HRD/Master/Department", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmDepartment.aspx")))
        routes.Add("HRD-Master-Jabatan", New Route("HRD/Master/Jabatan", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmJabatan.aspx")))
        routes.Add("HRD-Master-Cuti", New Route("HRD/Master/Cuti", New PageRouteHandler("~/Forms/HRD/Master/frmCuti.aspx")))
        routes.Add("HRD-Master-SACuti", New Route("HRD/Master/SACuti", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmSACuti.aspx")))
        routes.Add("HRD-Master-SettingAbsen", New Route("HRD/Master/SettingAbsen", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmSettingAbsen.aspx")))
        routes.Add("HRD-Master-StatusPerkawinan", New Route("HRD/Master/StatusPerkawinan", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmStatusPerkawinan.aspx")))
        routes.Add("HRD-Master-JadwalStore", New Route("HRD/Master/JadwalStore", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmJadwalStore.aspx")))
        routes.Add("HRD-Master-JadwalStore-Release", New Route("HRD/Master/JadwalStore/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmJadwalStoreRelease.aspx")))
        routes.Add("HRD-Master-SalaryEntry", New Route("HRD/Master/SalaryEntry", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmSalaryEntry.aspx")))
        routes.Add("HRD-Master-TunjanganOneTime", New Route("HRD/Master/TunjanganOneTime", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmTunjanganOneTime.aspx")))
        routes.Add("HRD-Master-ImportDataKaryawan", New Route("HRD/Master/ImportDataKaryawan", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmImportDataKaryawan.aspx")))
        routes.Add("HRD-Master-ImportDataDinas", New Route("HRD/Master/ImportDataDinas", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmImportDataDinas.aspx")))
        routes.Add("HRD-Master-EventSetting", New Route("HRD/Master/EventSetting", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmEventSetting.aspx")))
        routes.Add("HRD-Master-EventSetting-Release", New Route("HRD/Master/EventSetting/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmEventSettingRelease.aspx")))
        routes.Add("HRD-Master-ShiftAbsensi", New Route("HRD/Master/ShiftAbsensi", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmShiftAbsensi.aspx")))
        routes.Add("HRD-Master-BonusTHR", New Route("HRD/Master/BonusTHR", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmMasterBonusTHR.aspx")))
        routes.Add("HRD-Master-ImportDataKontrak", New Route("HRD/Master/ImportKontrak", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmImportDataKontrak.aspx")))
        routes.Add("HRD-Master-SettingAbsenHariKhusus", New Route("HRD/Master/SettingAbsenHariKhusus", New PageRouteHandler("~/Forms/HRDandPayroll/Master/frmSettingAbsenHariKhusus.aspx")))

        routes.Add("HRD-Transaksi-Absensi", New Route("HRD/Transaksi/Absensi", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmAbsensi.aspx")))
        routes.Add("HRD-Transaksi-ImportAbsensi", New Route("HRD/Transaksi/ImportAbsensi", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmImportAbsen.aspx")))
        routes.Add("HRD-Transaksi-Ijin", New Route("HRD/Transaksi/Ijin", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmIjin.aspx")))
        routes.Add("HRD-Transaksi-IjinRelease", New Route("HRD/Transaksi/IjinRelease", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmIjinRelease.aspx")))
        routes.Add("HRD-Transaksi-SPKLembur", New Route("HRD/Transaksi/SPKLembur", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmSPKLembur.aspx")))
        routes.Add("HRD-Transaksi-SPKLemburRelease", New Route("HRD/Transaksi/SPKLembur/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmSPKLemburRelease.aspx")))
        routes.Add("HRD-Transaksi-Gaji", New Route("HRD/Transaksi/Gaji", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmGaji.aspx")))
        routes.Add("HRD-Transaksi-Gaji-Release", New Route("HRD/Transaksi/Gaji/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmGajiRelease.aspx")))
        routes.Add("HRD-Transaksi-GajiPayment", New Route("HRD/Transaksi/GajiPayment", New PageRouteHandler("~/Forms/HRD/Transaksi/frmGajiPayment.aspx")))
        routes.Add("HRD-Transaksi-GajiPayment-Release", New Route("HRD/Transaksi/GajiPayment/Release", New PageRouteHandler("~/Forms/HRD/Transaksi/frmGajiPaymentRelease.aspx")))
        routes.Add("HRD-Transaksi-Lembur", New Route("HRD/Transaksi/Lembur", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmLembur.aspx")))
        routes.Add("HRD-Transaksi-Lembur-Release", New Route("HRD/Transaksi/Lembur/Release", New PageRouteHandler("~/Forms/HRD/Transaksi/frmLemburRelease.aspx")))
        routes.Add("HRD-Transaksi-LemburPayment", New Route("HRD/Transaksi/LemburPayment", New PageRouteHandler("~/Forms/HRD/Transaksi/frmLemburPayment.aspx")))
        routes.Add("HRD-Transaksi-LemburPayment-Release", New Route("HRD/Transaksi/LemburPayment/Release", New PageRouteHandler("~/Forms/HRD/Transaksi/frmLemburPaymentRelease.aspx")))
        routes.Add("HRD-Transaksi-KaryawanBerhenti", New Route("HRD/Transaksi/KaryawanBerhenti", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmKaryawanBerhenti.aspx")))
        routes.Add("HRD-Transaksi-THR", New Route("HRD/Transaksi/THR", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmThr.aspx")))
        routes.Add("HRD-Transaksi-THR-Release", New Route("HRD/Transaksi/THR/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmThrRelease.aspx")))
        routes.Add("HRD-Transaksi-THRPayment", New Route("HRD/Transaksi/THRPayment", New PageRouteHandler("~/Forms/HRD/Transaksi/frmTHRPayment.aspx")))
        routes.Add("HRD-Transaksi-THRPayment-Release", New Route("HRD/Transaksi/THRPayment/Release", New PageRouteHandler("~/Forms/HRD/Transaksi/frmTHRPaymentRelease.aspx")))
        routes.Add("HRD-Transaksi-UbahJamMasuk", New Route("HRD/Transaksi/UbahJamMasuk", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmUbahJamMasuk.aspx")))
        routes.Add("HRD-Transaksi-KompensasiKontrak", New Route("HRD/Transaksi/KompensasiKontrak", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmKompensasiKontrak.aspx")))
        routes.Add("HRD-Transaksi-KompensasiKontrak-Release", New Route("HRD/Transaksi/KompensasiKontrak/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Transaksi/frmKompensasiKontrakRelease.aspx")))

        routes.Add("HRD-Pinjaman-RegPinjaman", New Route("HRD/Pinjaman/RegPinjaman", New PageRouteHandler("~/Forms/HRDandPayroll/Pinjaman/frmRegPinjaman.aspx")))
        routes.Add("HRD-Pinjaman-RegPinjaman-Release", New Route("HRD/Pinjaman/RegPinjaman/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Pinjaman/frmRegPinjamanRelease.aspx")))
        routes.Add("HRD-Pinjaman-BayarPinjaman", New Route("HRD/Pinjaman/BayarPinjaman", New PageRouteHandler("~/Forms/HRDandPayroll/Pinjaman/frmBayarPinjaman.aspx")))
        routes.Add("HRD-Pinjaman-BayarPinjaman-Release", New Route("HRD/Pinjaman/BayarPinjaman/Release", New PageRouteHandler("~/Forms/HRDandPayroll/Pinjaman/frmBayarPinjamanRelease.aspx")))



        'routes.Add("HRD-RewardPoduksi-ProsesRewardProduksi", New Route("HRD/RewardPoduksi/ProsesRewardProduksi", New PageRouteHandler("~/Forms/HRD/RewardProduksi/frmProsesRewardProduksi.aspx")))
        'routes.Add("HRD-RewardPoduksi-RewardProduksiPayment", New Route("HRD/RewardPoduksi/RewardProduksiPayment", New PageRouteHandler("~/Forms/HRD/RewardProduksi/frmRewardProduksiPayment.aspx")))

        routes.Add("HRD-Laporan-Absensi-PerKaryawan", New Route("HRD/Laporan/Absensi/PerKaryawan", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Absensi/frmLapAbsensiPerKaryawan.aspx")))
        routes.Add("HRD-Laporan-Absensi-Rekap", New Route("HRD/Laporan/Absensi/Rekap", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Absensi/frmLapRekapAbsen.aspx")))
        routes.Add("HRD-Laporan-Absensi-CutiPerKaryawan", New Route("HRD/Laporan/Absensi/CutiPerKaryawan", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Absensi/frmLapCutiPerKaryawan.aspx")))

        routes.Add("HRD-Laporan-Covid19Lap", New Route("HRD/Laporan/Covid19Lap", New PageRouteHandler("~/Forms/HRD/Laporan/frmCovid19Lap.aspx")))
        routes.Add("HRD-Laporan-KaryawanHabisKontrak", New Route("HRD/Laporan/KaryawanHabisKontrak", New PageRouteHandler("~/Forms/HRD/Laporan/frmKaryawanHabisKontrak.aspx")))
        routes.Add("HRD-Laporan-Gaji-SlipGaji", New Route("HRD/Laporan/Gaji/SlipGaji", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmSlipGaji.aspx")))
        routes.Add("HRD-Laporan-Gaji-RekapGajiBank", New Route("HRD/Laporan/Gaji/RekapGajiBank", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmRekapGajiKeBank.aspx")))
        routes.Add("HRD-Laporan-Gaji-RekapThrBank", New Route("HRD/Laporan/Gaji/RekapThrBank", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmRekapThrKeBank.aspx")))
        routes.Add("HRD-Laporan-Gaji-PajakPerKaryawan", New Route("HRD/Laporan/Gaji/PajakPerKaryawan", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmLapPajakPerKaryawan.aspx")))
        routes.Add("HRD-Laporan-Gaji-PajakBulanan", New Route("HRD/Laporan/Gaji/PajakBulanan", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmLapPajakBulanan.aspx")))
        routes.Add("HRD-Laporan-Gaji-PajakTahunan", New Route("HRD/Laporan/Gaji/PajakTahunan", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmLapPajakTahunan.aspx")))
        routes.Add("HRD-Laporan-Gaji-SlipThr", New Route("HRD/Laporan/Gaji/SlipThr", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Gaji/frmSlipThr.aspx")))

        routes.Add("HRD-Laporan-Pinjaman-Rekap", New Route("HRD/Laporan/Pinjaman/Rekap", New PageRouteHandler("~/Forms/HRDandPayroll/Laporan/Pinjaman/frmLapRekapPinjaman.aspx")))

        routes.Add("HRD-Mutasi-DataDinas", New Route("HRD/Mutasi/DataDinas", New PageRouteHandler("~/Forms/HRDandPayroll/Mutasi/frmMutasiDataDinas.aspx")))

        routes.Add("HRD-Recruitment-Pelamar", New Route("HRD/Recruitment/Pelamar", New PageRouteHandler("~/Forms/HRD/Recruitment/frmPelamar.aspx")))
#End Region

#Region "Produksi"
        'routes.Add("Produksi-Maintenance-KodeProduksi", New Route("Produksi/Maintenance/KodeProduksi", New PageRouteHandler("~/Forms/Produksi/Maintenance/frmKodeProduksi.aspx")))
#End Region



    End Sub
End Class