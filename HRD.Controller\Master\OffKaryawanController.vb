﻿Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports StructureMap
Imports Z.EntityFramework.Plus

Public Class OffKaryawanController
    Inherits Controller(Of tm_off_karyawan)
    Implements IOffKaryawanListController, IOffKaryawanEditController

    Public ReadOnly Property DataList(bPosted As Boolean) As IQueryable(Of tm_off_karyawan) Implements IOffKaryawanListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of OffKaryawanService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tm_off_karyawan) = r.Output
                os = os.Where(Function(f) f.Posted = bPosted)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_off_karyawan With {.TglBerlaku = Now}

    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of OffKaryawanService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of OffKaryawanService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_off_karyawan)
        'ExpressMapper.Mapper.Register(Of tm_status_perkawinan, tm_status_perkawinan)() _
        '    .Ignore(Function(x) x.tm_karyawan)

        'Dim o = ExpressMapper.Mapper.Map(Of tm_status_perkawinan, tm_status_perkawinan)(TEntity)
        If TEntity.Id <= 0 Then
            TEntity.Createdby = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        Else
            If Action = Action.Posting Then
                TEntity.Posted = True
                TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.PostedDate = Now
                QueryCacheManager.ExpireTag("abs_off")
            Else
                TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.ModifiedDate = Now
            End If
        End If
        Dim Serv = ObjectFactory.GetInstance(Of OffKaryawanService)()

        Dim r = Serv.UpsertAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IOffKaryawanListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bPosted As Boolean) As IQueryable(Of tm_off_karyawan)
End Interface
Public Interface IOffKaryawanEditController
    Inherits IControllerMain, IControllerEdit(Of tm_off_karyawan)

End Interface