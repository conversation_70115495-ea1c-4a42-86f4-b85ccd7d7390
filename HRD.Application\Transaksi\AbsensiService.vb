﻿Imports System.Data.Entity
Imports HRD.Application
Imports HRD.Domain
Imports Z.EntityFramework.Plus
Imports System.Data.SqlClient
Imports Tanneryd.BulkOperations.EF6
Imports Tanneryd.BulkOperations.EF6.Model

Public Class AbsensiService
    Implements IAbsensiService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction
    Private ReadOnly _validationService As ValidationService

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, ByVal myFunction As IMyFunction, validationService As ValidationService)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
        _validationService = validationService
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Absensi Service", method, msg)
    End Sub

    Public Async Function GetAbsensisAsync() As Task(Of ResponseModel) Implements IAbsensiService.GetAbsensisAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_absensi)().TableNoTracking.OrderBy(Function(t) t.Id) '.ToListAsync()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetAbsensisAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetAbsensiByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IAbsensiService.GetAbsensiByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_absensi)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetAbsensiByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_absensi) As Task(Of ResponseModel) Implements IAbsensiService.UpsertAsync
        Try
            ' Check if payroll records exist for the month and year
            If _validationService.IsPayrollPosted(o.Cabang_id, o.Tanggal) Then
                Return ResponseModel.FailureResponse($"Cannot delete records for {o.Tanggal.ToString("MMMM yyyy")} as payroll has already been generated and posted.")
            End If
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_absensi)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tr_absensi)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Function UpdateJamMasukSelectedItem(getSelectedItem As List(Of Object), jammasuk As DateTime) As ResponseModel
        Try
            Dim repo = _unitOfWork.Repository(Of tr_absensi)
            ' Mengonversi objek ke dalam daftar ID yang akan dihapus
            Dim idsToUpdate As New List(Of Integer)
            For Each item As Object In getSelectedItem
                ' Anda harus mengganti "PropertyID" dengan properti yang sesuai untuk mendapatkan ID dari objek Anda
                Dim id As Integer = Convert.ToInt32(item) ' Ubah "PropertyID" dengan properti yang sesuai
                idsToUpdate.Add(id)
            Next

            repo.Table.Where(Function(f) idsToUpdate.Contains(f.Id)) _
    .Update(Function(u) New tr_absensi With {
        .MsJamMasuk = DbFunctions.CreateDateTime(u.MsJamMasuk.Value.Year, u.MsJamMasuk.Value.Month, u.MsJamMasuk.Value.Day, jammasuk.Hour, jammasuk.Minute, jammasuk.Second),
        .Terlambat = If(DbFunctions.DiffMinutes(DbFunctions.CreateDateTime(u.MsJamMasuk.Value.Year, u.MsJamMasuk.Value.Month, u.MsJamMasuk.Value.Day, jammasuk.Hour, jammasuk.Minute, jammasuk.Second), u.JamMasuk) < 0,
                        0,
                        DbFunctions.DiffMinutes(DbFunctions.CreateDateTime(u.MsJamMasuk.Value.Year, u.MsJamMasuk.Value.Month, u.MsJamMasuk.Value.Day, jammasuk.Hour, jammasuk.Minute, jammasuk.Second), u.JamMasuk))
    })

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)

        Catch ex As Exception
            Log(NameOf(Me.DeleteSelectedItem), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IAbsensiService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_absensi)().Get(Id)
            ' Check if payroll records exist for the month and year
            If _validationService.IsPayrollPosted(o.Cabang_id, o.Tanggal) Then
                Return ResponseModel.FailureResponse($"Cannot delete records for {o.Tanggal.ToString("MMMM yyyy")} as payroll has already been generated and posted.")
            End If
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_karyawan_datadinas).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Public Async Function SaveImport(dt As DataTable) As Task(Of ResponseModel) Implements IAbsensiService.SaveImport
        Try
            If dt.Rows.Count > 0 Then
                dt.Rows.RemoveAt(0)
            End If

            Dim listTask As New List(Of Task(Of ResponseModel))
            Dim rList As New List(Of tr_absensi)()

            ' Preload all distinct NIKs from the DataTable
            Dim nikList = dt.AsEnumerable().Select(Function(row) row(1).ToString().Trim()).Distinct().ToList()

            ' Preload karyawan data based on NIK
            Dim karyawanList = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking _
            .Where(Function(k) nikList.Contains(k.NIK)).ToList

            ' Get all existing attendance records for the date range in one query
            Dim datesToCheck = dt.AsEnumerable().Select(Function(row) CType(row(6), DateTime).Date).Distinct().ToList()
            Dim existingAbsensi = _unitOfWork.Repository(Of tr_absensi).TableNoTracking _
            .Where(Function(f) datesToCheck.Contains(f.Tanggal)).ToList()

            For Each row As DataRow In dt.Rows
                If String.IsNullOrWhiteSpace(row(1).ToString()) Then
                    Continue For
                End If
                If row(0).ToString.ToLower = "pin" Then
                    Continue For
                End If
                listTask.Add(ImportPerkaryawan(row, existingAbsensi, karyawanList))
            Next

            ' Await all tasks to complete
            Dim r = Await Task.WhenAll(listTask)

            ' Handle successful imports
            'rList.AddRange(results.Where(Function(x) x.Output IsNot Nothing AndAlso x.Success).ToList)
            For Each x In r
                If x.Output IsNot Nothing And x.Success Then
                    rList.Add(CType(x.Output, tr_absensi))
                End If
            Next
            If rList.Count > 0 Then
                Dim r_add = Await _unitOfWork.Repository(Of tr_absensi).AddRangeAsync(rList)

                _unitOfWork.Save()
            End If
            Dim oErr = r.Where(Function(f) f.Success <> True)
            Dim sTextErr As New Text.StringBuilder
            If oErr.Count > 0 Then
                For Each x In oErr
                    sTextErr.Append($"{x.Message}</BR>")
                Next
                Return ResponseModel.FailureResponse(sTextErr.ToString)
            End If

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Log(NameOf(Me.SaveImport), ex.Message)
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function ImportPerkaryawan(row As DataRow, existingAbsensi As List(Of tr_absensi), karyawanList As List(Of tm_karyawan)) As Task(Of ResponseModel)
        Try
            ' Get employee data
            Dim nik As String = row(1).ToString().Trim()
            'Dim oKar = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking.FirstOrDefault(Function(f) f.NIK = nik)
            ' Find the employee data from the preloaded list
            Dim oKar = karyawanList.FirstOrDefault(Function(k) k.NIK = nik)

            If oKar Is Nothing Then
                Return ResponseModel.FailureResponse($"Karyawan untuk NIK : {nik} tidak ada di database")
            End If

            ' Get attendance date
            Dim tgl As DateTime = CType(row(6), DateTime)
            Dim firstTanggal As DateTime = tgl.Date
            Dim lastTanggal As DateTime = tgl.Date.AddDays(1).AddSeconds(-1)

            ' Check if absensi record exists
            Dim abs = existingAbsensi.FirstOrDefault(Function(f) f.karyawan_id = oKar.Id AndAlso f.Tanggal = tgl.Date)

            ' If record exists but is marked as Alpha, update it with import data
            If abs IsNot Nothing Then
                If abs.Alpha_b = True Then
                    ' Update existing Alpha record with import data
                    ' Continue with the rest of the method to update the record
                Else
                    ' If not Alpha, skip this record
                    Return ResponseModel.SuccessResponse("ok", Nothing)
                End If
            Else
                ' Create new record if it doesn't exist
                abs = New tr_absensi With {
                    .Area_id = oKar.Area_id,
                    .Cabang_id = oKar.Cabang_id,
                    .karyawan_id = oKar.Id,
                    .Tanggal = tgl.Date
                }
            End If


            ' Get attendance settings based on date
            Dim shift As Integer = If(oKar.Ship.HasValue, oKar.Ship.Value, 0)
            Dim setAbs = _myFunction.tm_absensi_setting_by_date(oKar.Cabang_id, tgl.Date, shift)

            If setAbs Is Nothing Then
                Return ResponseModel.FailureResponse($"Setting Absen untuk cabang {oKar.tm_cabang.NamaCabang} belum di setting!")
            End If

            ' Calculate work hours
            Dim msMasuk As DateTime = tgl.Date.Add(setAbs.JamMasuk.TimeOfDay)
            Dim msKeluar As DateTime = tgl.Date.Add(setAbs.JamKeluar.TimeOfDay)

            ' Handle special day settings (Friday, Saturday, Sunday)
            If _myFunction.CekHariJumat(tgl) Then
                If setAbs.JamMasukJumat.HasValue Then msMasuk = tgl.Date.Add(setAbs.JamMasukJumat.Value.TimeOfDay)
                If setAbs.JamKeluarJumat.HasValue Then msKeluar = tgl.Date.Add(setAbs.JamKeluarJumat.Value.TimeOfDay)
            End If
            If _myFunction.CekHariSabtu(tgl) Then
                If setAbs.JamMasukSabtu.HasValue Then msMasuk = tgl.Date.Add(setAbs.JamMasukSabtu.Value.TimeOfDay)
                If setAbs.JamKeluarSabtu.HasValue Then msKeluar = tgl.Date.Add(setAbs.JamKeluarSabtu.Value.TimeOfDay)
            End If
            If _myFunction.CekHariMinggu(tgl) Then
                If setAbs.JamMasukMinggu.HasValue Then msMasuk = tgl.Date.Add(setAbs.JamMasukMinggu.Value.TimeOfDay)
                If setAbs.JamKeluarMinggu.HasValue Then msKeluar = tgl.Date.Add(setAbs.JamKeluarMinggu.Value.TimeOfDay)
            End If

            ' Handle absences
            Dim b_OFF As Boolean = False
            If oKar.TipeAbsensiOFF = 1 AndAlso Not oKar.Ship.HasValue Then
                Dim setOff = _myFunction.GetSettingAbsensi_OFF(oKar.Id, tgl)
                If Not String.IsNullOrEmpty(setOff.sKeterangan) Then
                    Return ResponseModel.FailureResponse(setOff.Item4)
                ElseIf setOff.bOff Then
                    b_OFF = True
                Else
                    msMasuk = setOff.dMSmasuk
                    msKeluar = setOff.dMSkeluar
                End If
            End If



            ' Update existing record or create new one
            If abs.Id = 0 Then
                ' This is a new record, set basic properties
                abs.Area_id = oKar.Area_id
                abs.Cabang_id = oKar.Cabang_id
                abs.karyawan_id = oKar.Id
                abs.Tanggal = tgl
            End If

            ' Always update these fields
            abs.MsJamMasuk = msMasuk
            abs.MsJamKeluar = msKeluar

            ' Handle attendance status
            If String.IsNullOrEmpty(row(7).ToString()) Then
                If b_OFF Then
                    abs.Off_b = True
                Else
                    abs.Alpha_b = True
                End If
            Else
                abs.Masuk_b = True
                abs.Alpha_b = False
                abs.JamMasuk = DateTime.Parse(tgl.Date.ToString("yyyy-MM-dd") & " " & row(7).ToString())
            End If

            If Not String.IsNullOrEmpty(row(8).ToString()) Then
                abs.JamKeluar = DateTime.Parse(tgl.Date.ToString("yyyy-MM-dd") & " " & row(8).ToString())
            End If

            ' Calculate delays
            If oKar.TipeAbsensiOFF = 2 Then
                If abs.JamMasuk.HasValue AndAlso abs.JamKeluar.HasValue Then
                    abs.Terlambat = DateDiff(DateInterval.Minute, abs.JamMasuk.Value, abs.JamKeluar.Value)
                    abs.Terlambat = abs.Terlambat - (8 * 60)
                    If abs.Terlambat < 0 Then
                        abs.Terlambat = abs.Terlambat * (-1)
                    Else
                        abs.Terlambat = 0
                    End If
                End If
            Else
                If abs.JamMasuk.HasValue Then
                    abs.Terlambat = DateDiff(DateInterval.Minute, msMasuk, abs.JamMasuk.Value)
                    If abs.Terlambat < 0 Then
                        abs.Terlambat = 0
                    End If

                    If abs.JamKeluar.HasValue Then
                        abs.PulangCepat = DateDiff(DateInterval.Minute, abs.JamKeluar.Value, msKeluar)
                        If abs.PulangCepat < 0 Then
                            abs.PulangCepat = 0
                        End If
                    End If
                End If
            End If

            ' Update overtime data
            If _myFunction.UpdateAbsensiDataLembur(abs, oKar) Then
                ' Overtime logic (if needed)
            End If

            ' If this is an existing record that was Alpha, update it in the database
            If abs.Id > 0 Then
                _unitOfWork.Repository(Of tr_absensi)().Update(abs)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse("Record updated successfully", abs)
            Else
                ' Return the new record to be added in batch
                Return ResponseModel.SuccessResponse("ok", abs)
            End If
        Catch ex As Exception
            Log(NameOf(Me.ImportPerkaryawan), ex.Message)
            Return ResponseModel.FailureResponse($"NIK: {row(1).ToString.Trim()} Tanggal: {row(6).ToString()} Error: {ex.Message}")
        End Try
    End Function

    Public Async Function GenerateAbsensi(cabang_id As String, startDate As Date, endDate As Date) As Task(Of ResponseModel) Implements IAbsensiService.GenerateAbsensi
        Try
            Dim tgl1 As Date = startDate.Date
            Dim tgl2 As Date = endDate.Date

            ' Preload all necessary data in one go to reduce database queries
            Dim cab = _myFunction.tm_cabang(cabang_id)

            ' Get all employees for this branch in one query
            Dim kars = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking.Where(Function(f) f.Cabang_id = cabang_id And (f.Berhenti_b <> True Or (f.Berhenti_b = True And f.Tgl_Berhenti > startDate))).Include("tm_karyawan_datadinas").ToList()

            ' Get all holidays for this date range in one query
            Dim liburs = _unitOfWork.Repository(Of tm_libur_nasional).TableNoTracking.Where(Function(f) f.Area_id = cab.Area_id AndAlso f.StartDate <= tgl2 AndAlso f.EndDate >= tgl1).ToList()

            ' Get all existing attendance records for this date range in one query
            Dim existingAbsensi = _unitOfWork.Repository(Of tr_absensi).TableNoTracking.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Tanggal >= tgl1 AndAlso f.Tanggal <= tgl2).ToList().GroupBy(Function(a) a.karyawan_id).ToDictionary(Function(g) g.Key, Function(g) g.ToList())

            ' Get all leave requests for this date range in one query
            Dim ijins = _unitOfWork.Repository(Of tr_ijin).TableNoTracking.Where(Function(f) f.Posted AndAlso f.StartDate <= tgl2 AndAlso f.EndDate >= tgl1).ToList().GroupBy(Function(i) i.Karyawan_id).ToDictionary(Function(g) g.Key, Function(g) g.ToList(), EqualityComparer(Of Integer).Default)

            ' Get all overtime records for this date range in one query
            Dim lemburs = _unitOfWork.Repository(Of tr_spk_lembur_line).TableNoTracking.Where(Function(f) f.tr_spk_lembur.Posted AndAlso f.tr_spk_lembur.Tanggal >= tgl1 AndAlso f.tr_spk_lembur.Tanggal <= tgl2).ToList().GroupBy(Function(l) l.Karyawan_id).ToDictionary(Function(g) g.Key, Function(g) g.ToList(), EqualityComparer(Of Integer).Default)

            ' Process employees in batches to avoid memory issues
            Const batchSize As Integer = 50
            Dim results As New List(Of ResponseModel)()

            For i As Integer = 0 To kars.Count - 1 Step batchSize
                Dim batch = kars.Skip(i).Take(batchSize).ToList()
                Dim batchTasks = batch.Select(Function(kar) GenerateAbsensiPerKaryawan(
                    kar,
                    startDate,
                    endDate,
                    liburs,
                    If(existingAbsensi.ContainsKey(kar.Id), existingAbsensi(kar.Id), New List(Of tr_absensi)()),
                    If(ijins.ContainsKey(kar.Id), ijins(kar.Id), New List(Of tr_ijin)()),
                    If(lemburs.ContainsKey(kar.Id), lemburs(kar.Id), New List(Of tr_spk_lembur_line)())
                ))

                Dim batchResults = Await Task.WhenAll(batchTasks)
                results.AddRange(batchResults)

                ' Save after each batch to reduce memory usage
                _unitOfWork.Save()
            Next

            ' Check the result of each task
            If results.All(Function(r) r.Success) Then
                ' Bulk update operations
                _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Tanggal >= startDate AndAlso f.Tanggal <= endDate And f.Masuk_b = True And f.Keterangan <> Nothing).Update(Function(u) New tr_absensi With {.Keterangan = ""})

                _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Tanggal >= startDate AndAlso f.Tanggal <= endDate And f.Alpha_b = True And Not f.Keterangan.Contains("Alpha")).Update(Function(u) New tr_absensi With {.Keterangan = "Alpha"})

                _unitOfWork.GetCurrentContext.tr_absensi.Where(Function(f) f.Cabang_id = cabang_id AndAlso f.Tanggal >= startDate AndAlso f.Tanggal <= endDate And f.tm_karyawan.Tgl_Berhenti <= startDate).Delete()

                _unitOfWork.Save()

                Return ResponseModel.SuccessResponse("Sukses", Nothing)
            Else
                Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
            End If

        Catch ex As Exception
            Log(NameOf(Me.GenerateAbsensi), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Async Function GenerateAbsensiPerKaryawan(kar As tm_karyawan, startDate As Date, endDate As Date,
                                                 liburs As List(Of tm_libur_nasional),
                                                 existingAbsensi As List(Of tr_absensi),
                                                 ijins As List(Of tr_ijin),
                                                 lemburs As List(Of tr_spk_lembur_line)) As Task(Of ResponseModel)
        Try
            ' Get attendance settings based on date
            Dim shift As Integer = If(kar.Ship.HasValue, kar.Ship.Value, 0)
            Dim settingAbs = _myFunction.tm_absensi_setting_by_date(kar.Cabang_id, startDate.Date, shift)

            ' Log warning if settingAbs is Nothing
            If settingAbs Is Nothing Then
                Log(NameOf(Me.GenerateAbsensiPerKaryawan), $"Warning: No attendance settings found for cabang {kar.Cabang_id}, date {startDate.Date}, shift {shift}")
            End If

            Dim repo = _unitOfWork.Repository(Of tr_absensi)
            Dim tgl1 As Date = startDate.Date
            Dim tgl2 As Date = endDate.Date

            ' Filter existing absensi for Alpha records
            Dim alphaAbsensi = existingAbsensi.Where(Function(f) f.Alpha_b = True AndAlso f.FromAbsensi <> True).ToList()

            ' Process existing records in one batch
            Dim taskUpdate = UpdateExistingAbsensi(alphaAbsensi, lemburs, ijins, liburs, kar)

            ' Generate list of all dates in the range
            Dim daysDiff As Integer = (endDate - startDate).Days
            Dim allDates = Enumerable.Range(0, daysDiff + 1).Select(Function(offset) startDate.AddDays(offset)).ToList()

            ' Get all dates that already have attendance records
            Dim existingDates = existingAbsensi.Select(Function(a) a.Tanggal.Date).Distinct().ToList()

            ' Find dates that need new attendance records
            Dim missingDates = allDates.Except(existingDates).ToList()

            ' Process new attendance records in batches
            Dim newAbsensiRecords As New List(Of tr_absensi)()
            Dim _dicAlpha As New Dictionary(Of Integer, Integer)()
            Dim absensiToUpdate As New List(Of tr_absensi)()

            ' Process missing dates to reduce memory usage
            For Each missingDate In missingDates
                Dim absensiData = DetermineAbsensiData(kar, missingDate, settingAbs, _dicAlpha)
                If absensiData IsNot Nothing Then
                    ' Apply leave, overtime, and holiday rules
                    Dim ijinForDate = ijins.FirstOrDefault(Function(i) i.StartDate <= missingDate AndAlso i.EndDate >= missingDate)
                    If ijinForDate IsNot Nothing Then
                        ' Apply leave rules
                        absensiData.Ijin_b = If(ijinForDate.tm_ijin_master.KodeIjin.Substring(0, 1) <> "C", True, False)
                        absensiData.Keterangan = ijinForDate.tm_ijin_master.NamaIjin
                        absensiData.Cuti_b = If(ijinForDate.tm_ijin_master.IsCuti = True, True, False)
                        absensiData.PotongGaji = If(ijinForDate.tm_ijin_master.PotongGaji = True, True, False)
                        absensiData.Alpha_b = False
                        absensiData.Ijin_id = ijinForDate.Id
                    Else
                        Dim lemburForDate = lemburs.FirstOrDefault(Function(l) l.tr_spk_lembur.Tanggal.Date = missingDate.Date)
                        If lemburForDate IsNot Nothing Then
                            ' Apply overtime rules
                            absensiData.LemburLine_id = lemburForDate.Id
                            _myFunction.UpdateAbsensiDataLembur(absensiData, kar)
                        Else
                            Dim liburForDate = liburs.FirstOrDefault(Function(l) l.StartDate <= missingDate AndAlso l.EndDate >= missingDate)
                            If liburForDate IsNot Nothing Then
                                ' Apply holiday rules
                                absensiData.LiburNasional_id = liburForDate.Id
                                absensiData.Off_b = True
                                absensiData.Alpha_b = False
                                absensiData.Ijin_b = False
                                absensiData.Ijin_id = Nothing
                                absensiData.Cuti_b = liburForDate.CutiBersama
                                absensiData.PotongGaji = False
                                absensiData.Keterangan = liburForDate.NamaHariLibur
                                absensiData.CutiBersama = liburForDate.CutiBersama
                                absensiData.LiburNasional = True
                            End If
                        End If
                    End If
                    newAbsensiRecords.Add(absensiData)
                End If
            Next

            ' Process the update task
            Dim r = Await taskUpdate
            If r.Success AndAlso r.Output IsNot Nothing Then
                ' Get records to update
                absensiToUpdate = DirectCast(r.Output, List(Of tr_absensi))
            End If

            ' Using Tanneryd BulkOperations for bulk operations
            If newAbsensiRecords.Any() OrElse absensiToUpdate.Any() Then
                ' Get the DbContext
                Dim context = _unitOfWork.GetCurrentContext

                ' Perform bulk insert if there are new records
                If newAbsensiRecords.Any() Then
                    ' Use Tanneryd BulkInsertAll with inline options
                    Dim request = New BulkInsertRequest(Of tr_absensi) With {
                        .Entities = newAbsensiRecords,
                        .Recursive = True
                    }

                    ' Execute the bulk insert operation
                    context.BulkInsertAll(request)
                End If

                ' Perform bulk update if there are records to update
                If absensiToUpdate.Any() Then
                    ' Use Tanneryd BulkUpdateAll with inline request
                    Dim updateRequest = New BulkUpdateRequest() With {
                        .Entities = absensiToUpdate
                    }

                    ' Execute the bulk update operation
                    context.BulkUpdateAll(updateRequest)
                End If
            End If

            Return ResponseModel.SuccessResponse("Sukses", Nothing)
        Catch ex As Exception
            Log(NameOf(Me.GenerateAbsensiPerKaryawan), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Async Function UpdateExistingAbsensiLembur(existingAbsensi As List(Of tr_absensi), lemburs As List(Of tr_spk_lembur_line)) As Task(Of List(Of ResponseModel))
        Dim lemburTasks = New List(Of Task(Of ResponseModel))()
        For Each x In lemburs
            Dim abs = existingAbsensi.Where(Function(f) f.LemburLine_id.HasValue AndAlso f.LemburLine_id = x.Id).FirstOrDefault
            If abs Is Nothing Then
                abs = existingAbsensi.Where(Function(f) f.Tanggal = x.tr_spk_lembur.Tanggal).FirstOrDefault
                If abs IsNot Nothing Then
                    lemburTasks.Add(UpdateLembur(abs, abs.tm_karyawan))
                End If

            End If
        Next
        Dim r = Await Task.WhenAll(lemburTasks)
        Return r.ToList
    End Function
    Private Async Function UpdateExistingAbsensiIjin(existingAbsensi As List(Of tr_absensi), ijins As List(Of tr_ijin)) As Task(Of List(Of ResponseModel))
        Dim ijinTasks = New List(Of Task(Of ResponseModel))()
        For Each x In ijins
            Dim abs = existingAbsensi.Where(Function(f) f.Ijin_id.HasValue AndAlso f.Ijin_id = x.Id).FirstOrDefault
            If abs Is Nothing Then
                Dim absList = existingAbsensi.Where(Function(f) f.Tanggal >= x.StartDate And f.Tanggal <= x.EndDate)
                For Each a In absList
                    ijinTasks.Add(UpdateIjin(a, ijins, a.Tanggal, a.tm_karyawan))
                Next
            End If
        Next
        Dim r = Await Task.WhenAll(ijinTasks)
        Return r.ToList
    End Function
    Private Async Function UpdateExistingAbsensLiburNasional(existingAbsensi As List(Of tr_absensi), liburNasionals As List(Of tm_libur_nasional)) As Task(Of List(Of ResponseModel))
        Dim liburTasks = New List(Of Task(Of ResponseModel))()
        For Each x In liburNasionals
            Dim abs = existingAbsensi.Where(Function(f) f.Tanggal = x.StartDate AndAlso f.Tanggal <= x.EndDate).FirstOrDefault
            If abs IsNot Nothing Then
                Dim ketLiburNasional = x.NamaHariLibur
                Dim bCutiBersama = x.CutiBersama
                If _myFunction.CekHariLibur(x.Area_id, abs.tm_karyawan, abs.Tanggal, ketLiburNasional, bCutiBersama) Then
                    liburTasks.Add(UpdateLibur(abs, ketLiburNasional, bCutiBersama))
                End If
            End If
        Next
        Dim r = Await Task.WhenAll(liburTasks)
        Return r.ToList
    End Function
    Private Async Function UpdateRangeAbsensi(abs As List(Of tr_absensi)) As Task
        Try
            For Each x In abs
                _unitOfWork.Repository(Of tr_absensi)().Update(x)
            Next

        Catch ex As Exception
            Throw New Exception($"Failed to update record UpdateRangeAbsensi: {ex.Message}")
        End Try
    End Function


    Private Async Function UpdateExistingAbsensi(existings As List(Of tr_absensi), lemburs As List(Of tr_spk_lembur_line), ijins As List(Of tr_ijin), liburs As List(Of tm_libur_nasional), kar As tm_karyawan) As Task(Of ResponseModel)
        Try
            Dim rList = New List(Of tr_absensi)
            'Update Libur Nasional
            For Each x In liburs
                Dim tgl1 As Date = x.StartDate
                Dim tgl2 As Date = x.EndDate.AddDays(1)
                Dim existingsabsensi = existings.Where(Function(f) f.Tanggal >= tgl1 And f.Tanggal < tgl2 And (f.LiburNasional_id.HasValue <> True Or (f.LiburNasional_id.HasValue And f.LiburNasional_id <> x.Id)))
                For Each existing In existingsabsensi
                    existing.LiburNasional_id = x.Id
                    Dim r = Await UpdateLibur(existing, x.NamaHariLibur, x.CutiBersama)
                    If r.Success Then
                        rList.Add(r.Output)
                    Else
                        Return ResponseModel.FailureResponse(r.Message)
                    End If
                Next
            Next

            'Update Lembur
            For Each x In lemburs
                Dim tgl1 As Date = x.tr_spk_lembur.Tanggal
                Dim tgl2 As Date = tgl1.AddDays(1)
                Dim existing = existings.Where(Function(f) f.Tanggal >= tgl1 And f.Tanggal < tgl2 And (f.LemburLine_id.HasValue = False Or (f.LemburLine_id.HasValue And f.LemburLine_id <> x.Id))).FirstOrDefault
                If existing IsNot Nothing Then
                    Dim r = Await UpdateLembur(existing, kar)
                    If r.Success Then
                        rList.Add(r.Output)
                    Else
                        Return ResponseModel.FailureResponse(r.Message)
                    End If
                End If

            Next

            'Update Ijin
            For Each x In ijins
                Dim tgl1 As Date = x.StartDate
                Dim tgl2 As Date = x.EndDate.AddDays(1)
                Dim existingabsensi = existings.Where(Function(f) f.Tanggal >= tgl1 And f.Tanggal < tgl2 And (f.Ijin_id.HasValue = False Or (f.Ijin_id.HasValue And f.Ijin_id <> x.Id)))
                For Each existing In existingabsensi
                    Dim r = Await UpdateIjin(existing, ijins, existing.Tanggal, kar)
                    If r IsNot Nothing Then
                        If r.Success Then
                            rList.Add(r.Output)
                        Else
                            Return ResponseModel.FailureResponse(r.Message)
                        End If
                    End If
                Next

            Next



            Return ResponseModel.SuccessResponse("Sukses", rList)

        Catch ex As Exception
            Log(NameOf(Me.UpdateExistingAbsensi), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try


    End Function

    Private Async Function AddNewAbsensi(kar As tm_karyawan, lemburs As List(Of tr_spk_lembur_line), tgl As Date, settingAbs As Object, _dicAlpha As Dictionary(Of Integer, Integer), ijins As List(Of tr_ijin), liburs As List(Of tm_libur_nasional)) As Task(Of ResponseModel)
        ' Get the appropriate attendance settings for this specific date
        Dim shift As Integer = If(kar.Ship.HasValue, kar.Ship.Value, 0)
        Dim settingAbsForDate = _myFunction.tm_absensi_setting_by_date(kar.Cabang_id, tgl.Date, shift)

        ' Log warning if settingAbsForDate is Nothing
        If settingAbsForDate Is Nothing Then
            Log(NameOf(Me.AddNewAbsensi), $"Warning: No attendance settings found for cabang {kar.Cabang_id}, date {tgl.Date}, shift {shift}")
        End If

        Try
            Dim absensiData = DetermineAbsensiData(kar, tgl, settingAbsForDate, _dicAlpha)
            If absensiData IsNot Nothing Then
                Dim ijinAdd = ijins.FirstOrDefault(Function(f) f.StartDate <= tgl AndAlso f.EndDate >= tgl)
                If ijinAdd IsNot Nothing Then
                    Dim r = Await UpdateIjin(absensiData, ijins, tgl, kar)
                    If r IsNot Nothing Then
                        If r.Success Then
                            Return r
                        Else
                            Return ResponseModel.FailureResponse(r.Message)
                        End If
                    End If
                Else
                    Dim lembur = lemburs.FirstOrDefault(Function(f) f.tr_spk_lembur.Tanggal = tgl)
                    If lembur IsNot Nothing Then
                        Dim r = Await UpdateLembur(absensiData, kar)
                        If r.Success Then
                            Return r
                        Else
                            Return ResponseModel.FailureResponse(r.Message)
                        End If
                    Else
                        Dim ketLiburNasional As String = Nothing
                        Dim bCutiBersama As Boolean = False
                        Dim bTipeAbsOFF As Boolean = Not (kar.TipeAbsensiOFF = 0)
                        Dim liburNasional = liburs.FirstOrDefault(Function(f) f.StartDate <= tgl AndAlso f.EndDate >= tgl)
                        If liburNasional IsNot Nothing Then
                            absensiData.LiburNasional_id = liburNasional.Id
                        End If
                        If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ketLiburNasional, bCutiBersama) Then
                            Dim r = Await UpdateLibur(absensiData, ketLiburNasional, bCutiBersama)
                            If r.Success Then
                                Return r
                            Else
                                Return ResponseModel.FailureResponse(r.Message)
                            End If

                        End If
                    End If

                End If
            End If
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, absensiData)
        Catch ex As Exception
            Log(NameOf(Me.AddNewAbsensi), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function


    Private Async Function UpdateLibur(existing As tr_absensi, ketLiburNasional As String, bCutiBersama As Boolean) As Task(Of ResponseModel)
        existing.Off_b = True
        existing.Alpha_b = False
        existing.Ijin_b = False
        existing.Ijin_id = Nothing
        existing.Cuti_b = bCutiBersama
        existing.PotongGaji = False
        existing.Keterangan = ketLiburNasional
        existing.CutiBersama = bCutiBersama
        existing.LiburNasional = True


        Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, existing)
    End Function
    Private Async Function UpdateLembur(abs As tr_absensi, oKar As tm_karyawan) As Task(Of ResponseModel)
        Try

            If _myFunction.UpdateAbsensiDataLembur(abs, oKar) Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, abs)
            Else
                Return Nothing
            End If

        Catch ex As Exception
            Log(NameOf(Me.UpdateLembur), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function
    Private Async Function UpdateIjin(existing As tr_absensi, ijins As List(Of tr_ijin), tgl As Date, kar As tm_karyawan) As Task(Of ResponseModel)
        Try
            If existing.Alpha_b Then
                Dim ijinAdd = ijins.FirstOrDefault(Function(f) f.StartDate <= tgl AndAlso f.EndDate >= tgl)

                If ijinAdd IsNot Nothing Then
                    existing.Ijin_b = If(ijinAdd.tm_ijin_master.KodeIjin.Substring(0, 1) <> "C", True, False)
                    existing.Keterangan = ijinAdd.tm_ijin_master.NamaIjin
                    existing.Cuti_b = If(ijinAdd.tm_ijin_master.IsCuti = True, True, False)
                    existing.PotongGaji = If(ijinAdd.tm_ijin_master.PotongGaji = True, True, False)
                    existing.Alpha_b = False
                    existing.Ijin_id = ijinAdd.Id
                    'existing.FromAbsensi = True

                    Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, existing)
                Else
                    If kar.TipeAbsensiOFF = 3 Then
                        existing.Masuk_b = True
                        existing.Keterangan = Nothing
                    Else
                        existing.Off_b = False
                        existing.Keterangan = "Alpha"
                    End If

                    Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, existing)
                End If

            End If



            Return Nothing
        Catch ex As Exception
            Log(NameOf(Me.UpdateIjin), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Function DetermineAbsensiData(kar As tm_karyawan, tgl As Date, settingAbs As tm_absensi_setting, ByRef dicAlpha As Dictionary(Of Integer, Integer)) As tr_absensi
        Dim ket As String = Nothing
        Dim bCutiBersama As Boolean = False
        Dim absensiData As tr_absensi
        If kar.TipeAbsensiOFF = 1 Then
            Dim setOff = _myFunction.GetSettingAbsensi_OFF(kar.Id, tgl)
            If setOff.sKeterangan <> Nothing Then
                Return Nothing
            End If
            If setOff.bOff Then
                absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = "OFF", .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .MsJamKeluar = Nothing}
            Else
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket) Then
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .MsJamKeluar = Nothing}
                Else
                    absensiData = New tr_absensi With {.Alpha_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = "Alpha", .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .MsJamKeluar = Nothing}
                End If

            End If
        ElseIf kar.TipeAbsensiOFF = 0 Then

            ' Check if settingAbs is Nothing (null)
            If settingAbs Is Nothing Then
                ' Log the issue
                Log(NameOf(Me.DetermineAbsensiData), $"Warning: settingAbs is Nothing for karyawan {kar.Id} on date {tgl}")

                ' Create absensi data without time settings
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket, bCutiBersama) Then
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing, .Cuti_b = bCutiBersama}
                Else
                    absensiData = New tr_absensi With {.Alpha_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = "Alpha", .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
                End If
            Else
                ' Use settingAbs normally
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket, bCutiBersama) Then
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamMasuk, "HH:mm:ss")), .JamKeluar = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamKeluar, "HH:mm:ss")), .Cuti_b = bCutiBersama}
                Else
                    absensiData = New tr_absensi With {.Alpha_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = "Alpha", .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamMasuk, "HH:mm:ss")), .JamKeluar = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamKeluar, "HH:mm:ss"))}
                End If
            End If

        ElseIf kar.TipeAbsensiOFF = 3 Then
            ' Check if settingAbs is Nothing (null)
            If settingAbs Is Nothing Then
                ' Log the issue
                Log(NameOf(Me.DetermineAbsensiData), $"Warning: settingAbs is Nothing for karyawan {kar.Id} on date {tgl}")

                ' Create absensi data without time settings
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket) Then
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
                Else
                    absensiData = New tr_absensi With {.Masuk_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = Nothing, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
                End If
            Else
                ' Use settingAbs normally
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket) Then
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamMasuk, "HH:mm:ss")), .JamKeluar = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamKeluar, "HH:mm:ss"))}
                Else
                    absensiData = New tr_absensi With {.Masuk_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = Nothing, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamMasuk, "HH:mm:ss")), .JamKeluar = String.Format("{0} {1}", Format(tgl, "yyyy-MM-dd"), Format(settingAbs.JamKeluar, "HH:mm:ss"))}
                End If
            End If

        Else

            If getJumlahOffMingguan(kar.Id, tgl, dicAlpha) > 2 Then
                ' Jika sudah lebih dari 2 hari OFF dalam minggu ini
                If _myFunction.CekHariLibur(kar.Area_id, kar, tgl, ket) Then
                    ' Jika hari libur nasional, tetap tandai sebagai OFF
                    absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
                Else
                    ' Jika bukan hari libur dan sudah melebihi 2 hari OFF, tandai sebagai Alpha
                    absensiData = New tr_absensi With {.Alpha_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = "Alpha", .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
                End If
            Else
                ' Jika belum melebihi 2 hari OFF dalam minggu ini, tandai sebagai OFF
                ket = "OFF"
                absensiData = New tr_absensi With {.Off_b = True, .Area_id = kar.Area_id, .Cabang_id = kar.Cabang_id, .karyawan_id = kar.Id, .Keterangan = ket, .Tanggal = DateTime.Parse(tgl), .MsJamMasuk = Nothing, .JamKeluar = Nothing}
            End If
        End If

        'Dim ln = _myFunction.tm_libur_nasional(tgl)
        'If ln IsNot Nothing Then
        '    absensiData.LiburNasional = True
        '    absensiData.CutiBersama = ln.CutiBersama
        'End If

        Return absensiData
    End Function
    Private Function getJumlahOffMingguan(kar_id As String, tgl As Date, ByRef _dicAlpha As Dictionary(Of Integer, Integer)) As Integer
        ' Menggunakan dictionary untuk caching hasil
        Dim weekOfMonth As Integer = GetWeekOfMonth(tgl)
        Dim _key As Integer = weekOfMonth

        ' Jika sudah ada di cache, gunakan nilai yang sudah dihitung
        If _dicAlpha.ContainsKey(_key) Then
            Dim jmlAlpha As Integer = _dicAlpha(_key)
            jmlAlpha += 1
            _dicAlpha(_key) = jmlAlpha
            Return jmlAlpha
        End If

        ' Menghitung tanggal awal minggu (Senin)
        Dim startDate As Date = tgl.AddDays((-1) * tgl.DayOfWeek + 1)
        Dim endDate As Date = tgl

        If tgl < startDate Then
            startDate = startDate.AddDays(-7)
            weekOfMonth = GetWeekOfMonth(startDate)
            _key = weekOfMonth
        End If

        ' Hitung jumlah hari OFF dalam minggu ini
        Dim abs = _unitOfWork.Repository(Of tr_absensi).TableNoTracking.Where(Function(f) f.karyawan_id = kar_id And f.Tanggal >= startDate And f.Tanggal <= endDate And f.Off_b = True).Count()

        ' Tambahkan ke cache
        Dim jmlAlphaCount As Integer = abs + 1
        _dicAlpha.Add(_key, jmlAlphaCount)

        Return jmlAlphaCount
    End Function
    Public Function GetWeekOfMonth(_date As DateTime) As Integer
        Dim firstDayOfMonth = New DateTime(_date.Year, _date.Month, 1)
        Return Math.Ceiling((_date.Day + firstDayOfMonth.DayOfWeek) / 7.0)
    End Function

    Function DeleteSelectedItem(items As IList(Of Object)) As ResponseModel
        Try
            Dim repo = _unitOfWork.Repository(Of tr_absensi)

            ' Mengonversi objek ke dalam daftar ID yang akan dihapus
            Dim idsToDelete As New List(Of Integer)
            For Each item As Object In items
                Dim id As Integer = Convert.ToInt32(item)
                idsToDelete.Add(id)
            Next

            ' Process IDs in batches to avoid timeout
            Const batchSize As Integer = 100
            Dim context = _unitOfWork.GetCurrentContext
            Dim payrollPostedError As String = Nothing

            ' Check if any records can't be deleted due to posted payroll
            ' Process in smaller batches to avoid timeout
            For i As Integer = 0 To idsToDelete.Count - 1 Step batchSize
                ' Get the current batch of IDs
                Dim batchIds = idsToDelete.Skip(i).Take(batchSize).ToList()

                Try
                    ' Use a more efficient query with a smaller batch
                    Dim batchRecords = repo.TableNoTracking.
                        Where(Function(f) batchIds.Contains(f.Id)).
                        Select(Function(f) New With {
                            .Id = f.Id,
                            .Cabang_id = f.Cabang_id,
                            .Tanggal = f.Tanggal
                        }).ToList()

                    ' Check if payroll is posted for any record in this batch
                    For Each record In batchRecords
                        If _validationService.IsPayrollPosted(record.Cabang_id, record.Tanggal) Then
                            payrollPostedError = $"Cannot delete records for {record.Tanggal.ToString("MMMM yyyy")} as payroll has already been generated and posted."
                            Exit For
                        End If
                    Next

                    If Not String.IsNullOrEmpty(payrollPostedError) Then
                        Exit For
                    End If
                Catch ex As Exception
                    ' Log the error but continue with deletion
                    Log(NameOf(Me.DeleteSelectedItem), $"Error checking payroll status: {ex.Message}. Will proceed with deletion.")
                End Try
            Next

            ' If any record can't be deleted due to posted payroll, return error
            If Not String.IsNullOrEmpty(payrollPostedError) Then
                Return ResponseModel.FailureResponse(payrollPostedError)
            End If

            ' Untuk performance terbaik, gunakan Z.EntityFramework.Plus batch delete
            ' yang sudah diimpor di awal file: Imports Z.EntityFramework.Plus
            Try
                ' Metode 1: Batch delete menggunakan Z.EntityFramework.Plus (paling efisien)
                ' Process in smaller batches to avoid timeout
                Log(NameOf(Me.DeleteSelectedItem), $"Deleting {idsToDelete.Count} records using batch operation")
                Dim deleteCount = 0

                ' Process in batches to avoid timeout
                For i As Integer = 0 To idsToDelete.Count - 1 Step batchSize
                    ' Get the current batch of IDs
                    Dim batchIds = idsToDelete.Skip(i).Take(batchSize).ToList()

                    ' Delete this batch
                    deleteCount += repo.Table.Where(Function(f) batchIds.Contains(f.Id)).Delete()

                    ' Log progress for large batches
                    If idsToDelete.Count > 1000 AndAlso (i + batchSize) Mod 1000 < batchSize Then
                        Log(NameOf(Me.DeleteSelectedItem), "Deleted " & Math.Min(i + batchSize, idsToDelete.Count) & " of " & idsToDelete.Count & " records")
                    End If
                Next

                _unitOfWork.Save()

                If deleteCount > 0 Then
                    Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
                Else
                    ' Jika tidak ada record yang dihapus, coba pendekatan lain
                    Log(NameOf(Me.DeleteSelectedItem), $"No records were deleted using batch operation, trying SqlQuery approach")

                    ' Metode 2: Execute SQL statement langsung (efficient untuk jumlah yang sangat besar)
                    ' Kelompokkan ID dalam batches untuk menghindari parameter limit di SQL
                    Dim sqlBatchSize = 500  ' Smaller batch size for SQL to avoid parameter limits
                    Dim totalDeleted = 0
                    ' Use the existing context variable from outer scope

                    For i As Integer = 0 To idsToDelete.Count - 1 Step sqlBatchSize
                        Try
                            ' Ambil batch dari ID
                            Dim currentBatch = idsToDelete.Skip(i).Take(sqlBatchSize).ToList()

                            ' Use parameterized query to avoid SQL injection and improve performance
                            Dim parameters = New List(Of SqlParameter)()
                            Dim paramNames = New List(Of String)()

                            For j As Integer = 0 To currentBatch.Count - 1
                                Dim paramName = $"@p{j}"
                                paramNames.Add(paramName)
                                parameters.Add(New SqlParameter(paramName, currentBatch(j)))
                            Next

                            ' Build the SQL query with parameters
                            Dim sqlQuery = $"DELETE FROM tr_absensi WHERE Id IN ({String.Join(",", paramNames)})"

                            ' Execute raw SQL delete with parameters
                            totalDeleted += context.Database.ExecuteSqlCommand(sqlQuery, parameters.ToArray())

                            ' Log progress for large batches
                            If idsToDelete.Count > 1000 AndAlso (i + sqlBatchSize) Mod 1000 < sqlBatchSize Then
                                Log(NameOf(Me.DeleteSelectedItem), "SQL Delete: Processed " & Math.Min(i + sqlBatchSize, idsToDelete.Count) & " of " & idsToDelete.Count & " records")
                            End If
                        Catch ex As Exception
                            ' Log error but continue with next batch
                            Log(NameOf(Me.DeleteSelectedItem), "Error in SQL batch delete: " & ex.Message & ". Continuing with next batch.")
                        End Try
                    Next

                    If totalDeleted > 0 Then
                        Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
                    Else
                        ' Metode 3: Fallback ke penghapusan individual dengan batch
                        Log(NameOf(Me.DeleteSelectedItem), $"SQL delete approach failed, trying individual deletion in batches")

                        ' Use a smaller batch size for entity deletion to avoid memory issues
                        Dim entityBatchSize = 50
                        Dim totalEntitiesDeleted = 0

                        ' Process IDs in smaller batches to avoid loading too many entities at once
                        For i As Integer = 0 To idsToDelete.Count - 1 Step entityBatchSize
                            Try
                                ' Get a small batch of IDs
                                Dim batchIds = idsToDelete.Skip(i).Take(entityBatchSize).ToList()

                                ' Fetch only this batch of entities
                                Dim currentBatch = repo.Table.Where(Function(f) batchIds.Contains(f.Id)).ToList()

                                If currentBatch.Any() Then
                                    ' Set context tracking behavior to improve performance
                                    _unitOfWork.GetCurrentContext.Configuration.AutoDetectChangesEnabled = False

                                    For Each _Entity In currentBatch
                                        ' Mark entity as deleted
                                        _unitOfWork.GetCurrentContext.Entry(_Entity).State = EntityState.Deleted
                                        totalEntitiesDeleted += 1
                                    Next

                                    ' Turn back on to avoid potential issues elsewhere
                                    _unitOfWork.GetCurrentContext.Configuration.AutoDetectChangesEnabled = True
                                    _unitOfWork.Save()

                                    ' Log progress for large batches
                                    If idsToDelete.Count > 500 AndAlso (i + entityBatchSize) Mod 500 < entityBatchSize Then
                                        Log(NameOf(Me.DeleteSelectedItem), "Entity Delete: Processed " & Math.Min(i + entityBatchSize, idsToDelete.Count) & " of " & idsToDelete.Count & " records")
                                    End If
                                End If
                            Catch ex As Exception
                                ' Log error but continue with next batch
                                Log(NameOf(Me.DeleteSelectedItem), "Error in entity batch delete: " & ex.Message & ". Continuing with next batch.")
                            End Try
                        Next

                        ' Return success if any entities were deleted
                        If totalEntitiesDeleted > 0 Then
                            Log(NameOf(Me.DeleteSelectedItem), "Successfully deleted " & totalEntitiesDeleted & " entities using entity batch approach")
                        End If

                        Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
                    End If
                End If
            Catch ex As Exception
                Log(NameOf(Me.DeleteSelectedItem), $"Error during batch deletion: {ex.Message}. Trying fallback approach.")

                ' Fallback: Hapus secara individual dengan batches jika semua pendekatan gagal
                Try
                    ' Penghapusan item-by-item tetapi dalam batch untuk menghindari out of memory
                    Dim finalBatchSize = 50
                    Dim totalFinalDeleted = 0

                    For i As Integer = 0 To idsToDelete.Count - 1 Step finalBatchSize
                        Try
                            Dim currentBatchIds = idsToDelete.Skip(i).Take(finalBatchSize).ToList()

                            ' Delete batch secara terpisah untuk mengurangi memory pressure
                            Dim b = repo.Delete(Function(f) currentBatchIds.Contains(f.Id)).GetAwaiter().GetResult()
                            If b Then
                                totalFinalDeleted += currentBatchIds.Count
                            End If
                            _unitOfWork.Save()

                            ' Log progress for large batches
                            If idsToDelete.Count > 500 AndAlso (i + finalBatchSize) Mod 500 < finalBatchSize Then
                                Log(NameOf(Me.DeleteSelectedItem), "Final Delete: Processed " & Math.Min(i + finalBatchSize, idsToDelete.Count) & " of " & idsToDelete.Count & " records")
                            End If
                        Catch batchEx As Exception
                            Log(NameOf(Me.DeleteSelectedItem), "Error in final batch delete at position " & i & ": " & batchEx.Message & ". Continuing with next batch.")
                        End Try
                    Next

                    Log(NameOf(Me.DeleteSelectedItem), "Final fallback method deleted " & totalFinalDeleted & " records")

                    Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
                Catch innerEx As Exception
                    Log(NameOf(Me.DeleteSelectedItem), "Fallback deletion also failed: " & innerEx.Message)
                    Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
                End Try
            End Try
        Catch ex As Exception
            Log(NameOf(Me.DeleteSelectedItem), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

End Class
