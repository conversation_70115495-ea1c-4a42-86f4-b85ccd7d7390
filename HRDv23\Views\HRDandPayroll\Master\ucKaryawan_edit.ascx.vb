﻿Imports DevExpress.Web
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports ILS.MVVM
Public Class ucKaryawan_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IKaryawanEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            Me.ctrl_karyawan_datadinas1.Data = oDataDinas
            Me.ctrl_karyawan_datadinas1.DataBind()

            Me.ctrl_karyawan_datadinas1.Enabled = False

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            'opt_status_karyawan.Enabled = (Controller.Action = Action.AddNew)

            If Controller.Action = Action.Posting Then
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_karyawan.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_karyawan.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction


        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)

        If oSelectItem.Photo IsNot Nothing Then
            oSelectItem.Photo = myFunction.CreateThumbnail(oSelectItem.Photo, 1500)
        End If

        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tm_karyawan
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tm_karyawan)
        End Get
        Set(value As tm_karyawan)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    ReadOnly Property oDataDinas As tm_karyawan_datadinas
        Get
            Dim o = oSelectItem.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault
            'If o Is Nothing Then
            '    o = New tm_karyawan_datadinas
            'End If
            Return o
        End Get

    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub cb_jeniskelamin_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_jeniskelamin.ItemRequestedByValue
        MyMethod.JenisKelamin_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_jeniskelamin_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_jeniskelamin.ItemsRequestedByFilterCondition
        MyMethod.JenisKelamin_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_agama_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_agama.ItemRequestedByValue
        MyMethod.Agama_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_agama_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_agama.ItemsRequestedByFilterCondition
        MyMethod.Agama_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_status_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_status.ItemRequestedByValue
        MyMethod.StatusPTKP_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_status_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_status.ItemsRequestedByFilterCondition
        MyMethod.StatusPTKP_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_pendidikan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_pendidikan.ItemRequestedByValue
        MyMethod.Pendidikan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_pendidikan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_pendidikan.ItemsRequestedByFilterCondition
        MyMethod.Pendidikan_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_jurusan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_jurusan.ItemRequestedByValue
        MyMethod.Jurusan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_jurusan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_jurusan.ItemsRequestedByFilterCondition
        MyMethod.Jurusan_ItemsRequestedByFilterCondition(source, e, cb_pendidikan.Value)
    End Sub

    Protected Sub cb_namabank_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_namabank.ItemRequestedByValue
        MyMethod.NamaBank_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_namabank_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_namabank.ItemsRequestedByFilterCondition
        MyMethod.NamaBank_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_status_perkawinan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_status_perkawinan.ItemRequestedByValue
        MyMethod.StatusPerkawinan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_status_perkawinan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_status_perkawinan.ItemsRequestedByFilterCondition
        MyMethod.StatusPerkawinan_ItemsRequestedByFilterCondition(source, e)
    End Sub
End Class