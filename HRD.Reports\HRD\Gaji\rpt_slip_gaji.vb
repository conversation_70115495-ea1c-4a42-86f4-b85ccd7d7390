﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Imports HRD.Helpers

Public Class rpt_slip_gaji

    Private Sub Detail1_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail1.BeforePrint
        Dim o = CType(Me.DetailReport.GetCurrentRow, tr_gaji_line)

        Dim t As New Terbilang
        t.Text = Math.Round(o.THP, 0).ToString.Replace(",", ".")
        If o.THP < 0 Then
            txt_terbilang.Text = $"TERBILANG : MINUS {t.Text.ToUpper}"
        Else
            txt_terbilang.Text = $"TERBILANG : {t.Text.ToUpper}"
        End If

    End Sub
End Class