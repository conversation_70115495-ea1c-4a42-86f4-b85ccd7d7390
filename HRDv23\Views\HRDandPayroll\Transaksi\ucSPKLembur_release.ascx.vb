Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Public Class ucSPKLembur_release
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ISPKLemburListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = Nothing 'AuthHelper.GetLoggedInUserInfo.Cabang_id
        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.DefaultSorting = "Id Desc"
        e.QueryableSource = Controller.DataList(False, cb_area.Value, cb_cabang.Value).AsQueryable
    End Sub

    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim b As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Posted")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(b, 1, 0)
            Case "btn_view"
                e.Visible = If(b, 0, 1)
        End Select
    End Sub

    Protected Sub cp_spk_lembur_release_Callback(sender As Object, e As CallbackEventArgsBase)
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If

        Dim parameters() As String = e.Parameter.Split(";")
        Dim command As String = parameters(0)

        Select Case command
            Case "btn_release"
                ' Handle single release
                Dim id As Integer = Convert.ToInt32(parameters(1))
                ReleaseItem(id)

            Case "release_selected"
                ' Handle multiple releases
                If parameters.Length > 1 Then
                    Dim selectedIdsString As String = parameters(1)
                    Dim selectedIds() As String = selectedIdsString.Split(",")
                    
                    For Each idStr As String In selectedIds
                        Dim id As Integer
                        If Integer.TryParse(idStr, id) Then
                            ReleaseItem(id)
                        End If
                    Next
                End If
        End Select
    End Sub

    Private Sub ReleaseItem(id As Integer)
        Try
            ' Get the SPK Lembur controller from the main form
            Dim mainController = TryCast(Me.Page.Session("frmSPKLemburRelease"), SPKLemburController)
            
            If mainController IsNot Nothing Then
                mainController.Action = Action.Posting
                mainController.SelectItem(id)
                
                ' Create a dummy instance of the edit user control to call its Saving method
                Dim editCtrl = TryCast(Me.Page.LoadControl("~/Views/HRDandPayroll/Transaksi/ucSPKLembur_edit.ascx"), ucSPKLembur_edit)
                
                If editCtrl IsNot Nothing Then
                    editCtrl.Controller = mainController
                    editCtrl.Page = Me.Page
                    editCtrl.Saving()
                    
                    If Not mainController.Saved Then
                        ' Handle error if needed
                    End If
                End If
                
                mainController.Reset()
            End If
        Catch ex As Exception
            ' Handle exception
        End Try
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub
End Class