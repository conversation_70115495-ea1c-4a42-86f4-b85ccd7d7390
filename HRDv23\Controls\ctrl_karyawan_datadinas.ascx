﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ctrl_karyawan_datadinas.ascx.vb" Inherits="HRDv23.ctrl_karyawan_datadinas" %>

<script type="text/javascript">
    var bHrd = true;

    function hitungDurasiKontrak(tglAwal, tglAkhir) {
    // Konversi string tanggal ke objek Date
    const tanggalAwal = new Date(tglAwal);
    const tanggalAkhir = new Date(tglAkhir);

    // Hitung jumlah tahun dan bulan
    let tahun = tanggalAkhir.getFullYear() - tanggalAwal.getFullYear();
    let bulan = tanggalAkhir.getMonth() - tanggalAwal.getMonth();

    // Jika bulan negatif, kurangi satu tahun dan tambahkan 12 bulan
    if (bulan < 0) {
        tahun--;
        bulan += 12;
    }

    // Total durasi dalam bulan
    const durasiBulan = (tahun * 12) + bulan;

    return durasiBulan;
}

    function CalculateAssuransi(gp, id_kar, kantor_id) {
        $.ajax({
            type: "POST",
            url: "../../Service/HRDWebService.asmx/CalculateAssuransi",
            data: "{'gajiPokok':'" + gp + "','id_kar':'" + id_kar + "','kantor_id':'" + kantor_id + "'}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (response) {
                var d = response.d;
                txt_pd_jkk.SetValue(d.Pd_JKK);
                txt_pd_jkm.SetValue(d.Pd_JKM);
                txt_pd_jht.SetValue(d.Pd_JHT);
                txt_pd_jpk.SetValue(d.Pd_JPK);
                txt_pd_jp.SetValue(d.Pd_JP);

                txt_pt_p_jkk.SetValue(d.Pt_P_JKK);
                txt_pt_p_jkm.SetValue(d.Pt_P_JKM);
                txt_pt_p_jht.SetValue(d.Pt_P_JHT);
                txt_pt_p_jpk.SetValue(d.Pt_P_JPK);
                txt_pt_p_jp.SetValue(d.Pt_P_JP);

                txt_pt_k_jht.SetValue(d.Pt_K_JHT);
                txt_pt_k_jpk.SetValue(d.Pt_K_JPK);
                txt_pt_k_jp.SetValue(d.Pt_K_JP);

                //txt_pt_k_jpk_mandiri.SetValue(d.Pt_K_Jpk_Mandiri);

                CalcSummary();
            },
            failure: function (msg) {
                alert(msg);
            }
        })
    }

    function GetPPH21(totalPendapatan, id_kar) {
        var pph21 = 0;
        $.ajax({
            type: "POST",
            url: "../../Service/HRDWebService.asmx/CalculatePPH21",
            data: "{'totalPendapatan':'" + totalPendapatan + "','id_kar':'" + id_kar + "'}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (response) {
                var d = response.d;
                txt_pt_pph21.SetValue(d.PPH21);
                if (txt_pt_pph21.GetValue() < 0) {
                    txt_pt_pph21.SetValue(0);
                }
                var totalPotongan = txt_pt_p_jkk.GetValue() + txt_pt_p_jkm.GetValue() + txt_pt_p_jht.GetValue() + txt_pt_p_jpk.GetValue() + txt_pt_p_jp.GetValue() + txt_pt_k_jht.GetValue() + txt_pt_k_jpk.GetValue() + txt_pt_k_jp.GetValue()  + txt_pt_pph21.GetValue()+txt_pt_sp.GetValue()+txt_pt_ser_pekerja.GetValue();
                txt_total_pend.SetValue(totalPendapatan);
                txt_total_pot.SetValue(totalPotongan);
                txt_net.SetValue(totalPendapatan - totalPotongan);
                },
            failure: function (msg) {
                alert(msg);
            }
        })
    }

    function CalcSummary() {
        var totalPendapatan = txt_gajipokok.GetValue()+txt_pd_jabatan.GetValue()+txt_pd_makan.GetValue()+txt_pd_premihadir.GetValue()+txt_pd_susu.GetValue()+txt_pd_jkk.GetValue()+txt_pd_jkm.GetValue()+txt_pd_jht.GetValue()+txt_pd_jpk.GetValue()+txt_pd_jp.GetValue()+txt_pd_transport.GetValue()+txt_pd_kontrak.GetValue()+txt_pd_probation.GetValue()+txt_pd_premi_ass.GetValue()+txt_pd_pajak.GetValue();
        //txt_pot_zakat.SetValue((2.5 / 100) * totalPendapatan);
        //txt_pot_sukarela.SetValue(0 );
        GetPPH21(totalPendapatan, cb_karyawan.GetValue());
        
    }

    function StatusTetapChanged() {
        txt_tgl_tetap.SetEnabled(chk_tetap.GetValue());
        txt_tgl_awal_kontrak.SetEnabled(!chk_tetap.GetValue());
        txt_tgl_akhir_kontrak.SetEnabled(!chk_tetap.GetValue());
        txt_nokontrak.SetEnabled(!chk_tetap.GetValue());
        txt_reload_cuti.SetEnabled(!chk_tetap.GetValue());

        if (chk_tetap.GetValue() != true) {
            txt_tgl_tetap.SetValue(null);
        } else {
            txt_tgl_awal_kontrak.SetValue(null);
            txt_tgl_akhir_kontrak.SetValue(null);
            txt_nokontrak.SetValue(null);
            txt_reload_cuti.SetValue(null);
            txt_durasi_kontrak.SetValue(0);
        }
    }

    function GetDataDinas(id_kar) {
        $.ajax({
            type: "POST",
            url: "../../Service/HRDWebService.asmx/GetDataDinas",
            data: "{'id_kar':'" + id_kar + "'}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (response) {
                var d = response.d;
                cb_department.SetValue(d.Department_id);
                cb_jabatan.SetValue(d.Jabatan_id);
                chk_tetap.SetValue(d.Tetap);
                if (d.TglTetap) {
                   txt_tgl_tetap.SetValue(new Date(d.TglTetap));
                } else {
                    txt_tgl_tetap.SetValue(null);
                }
                if (d.TglAwalKontrak) {
                   txt_tgl_awal_kontrak.SetValue(new Date(d.TglAwalKontrak));
                } else {
                    txt_tgl_awal_kontrak.SetValue(null);
                }
                 if (d.TglAkhirKontrak) {
                   txt_tgl_akhir_kontrak.SetValue(new Date(d.TglAkhirKontrak));
                } else {
                    txt_tgl_akhir_kontrak.SetValue(null);
                }
                if (d.TglReloadCuti) {
                   txt_reload_cuti.SetValue(new Date(d.TglReloadCuti));
                } else {
                    txt_reload_cuti.SetValue(null);
                }
                txt_nokontrak.SetValue(d.NoKontrakKerja);
                txt_durasi_kontrak.SetValue(hitungDurasiKontrak(txt_tgl_awal_kontrak.GetValue(),txt_tgl_akhir_kontrak.GetValue()));
                if (bHrd) return;
                txt_gajipokok.SetValue(d.Pd_GajiPokok);
                txt_pd_jabatan.SetValue(d.Pd_T_Jabatan);
                txt_pd_transport.SetValue(d.Pd_T_Transport);
                txt_pd_makan.SetValue(d.Pd_T_Makan);
                txt_pd_premihadir.SetValue(d.Pd_T_PremiHadir);
                txt_pd_susu.SetValue(d.Pd_T_Susu);
                txt_pd_kontrak.SetValue(d.Pd_T_Kontrak);
                txt_pd_probation.SetValue(d.Pd_T_Probation);
                txt_pd_premi_ass.SetValue(d.Pd_T_PremiAss);
                txt_pd_pajak.SetValue(d.Pd_T_Pajak);
                txt_pt_sp.SetValue(d.Pt_SP);
                txt_pt_ser_pekerja.SetValue(d.Pt_SerPekerja);

                StatusTetapChanged();
                CalculateAssuransi(txt_gajipokok.GetValue(),cb_karyawan.GetValue(),cb_cabang.GetValue());
                
            },
            failure: function (msg) {
                alert(msg);
            }
        })
    }
</script>

<dx:ASPxFormLayout runat="server" ID="ASPxFormLayout2">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="DATA DINAS INFO" ColCount="2" ColumnCount="2" ColSpan="1">
            <Items>
                <dx:LayoutItem Caption="Perusahaan" ColSpan="1" FieldName="Area_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="KodeArea">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn FieldName="NamaArea">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_karyawan.PerformCallback();
}" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="KodeCabang">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn FieldName="NamaCabang">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Karyawan" ColSpan="1" FieldName="Karyawan_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_karyawan" runat="server" CallbackPageSize="10" ClientInstanceName="cb_karyawan" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	GetDataDinas(cb_karyawan.GetValue());
	
}" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="NIK">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn FieldName="Nama">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem FieldName="Tanggal" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit runat="server" EditFormat="Custom" EditFormatString="dd-MM-yyyy" DisplayFormatString="dd MMM yyyy" ID="txt_tanggal">
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Department" FieldName="Department_id" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_department" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0}" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" ClientInstanceName="cb_department">
                                <ClientSideEvents Init="onInitCB" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="NamaDepartemen">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Jabatan" FieldName="Jabatan_id" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_jabatan" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0}" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" ClientInstanceName="cb_jabatan">
                                <ClientSideEvents Init="onInitCB" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="Jabatan">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Karyawan Tetap" FieldName="Tetap" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxCheckBox runat="server" CheckState="Unchecked" ID="chk_tetap" ClientInstanceName="chk_tetap">
                                <ClientSideEvents ValueChanged="function(s, e) {
	StatusTetapChanged();
}" />
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                            </dx:ASPxCheckBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem FieldName="TglTetap" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit runat="server" ID="txt_tgl_tetap" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy" ClientInstanceName="txt_tgl_tetap">
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                                <DisabledStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="Black">
                                </DisabledStyle>
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="KONTRAK INFO" ColCount="2" ColumnCount="2" ColSpan="1">
            <Items>
                <dx:LayoutItem FieldName="TglAwalKontrak" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit runat="server" ID="txt_tgl_awal_kontrak" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy" ClientInstanceName="txt_tgl_awal_kontrak">
                                <ClientSideEvents ValueChanged="function(s, e) {
var durasi=hitungDurasiKontrak(s.GetValue(),txt_tgl_akhir_kontrak.GetValue());
txt_durasi_kontrak.SetValue(durasi);
}" />
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                                <DisabledStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="Black">
                                </DisabledStyle>
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem FieldName="TglAkhirKontrak" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit runat="server" ID="txt_tgl_akhir_kontrak" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy" ClientInstanceName="txt_tgl_akhir_kontrak">
                                <ClientSideEvents ValueChanged="function(s, e) {
	var durasi=hitungDurasiKontrak(txt_tgl_awal_kontrak.GetValue(),s.GetValue());
	txt_durasi_kontrak.SetValue(durasi);
}" />
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                                <DisabledStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="Black">
                                </DisabledStyle>
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" FieldName="NoKontrakKerja">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxTextBox ID="txt_nokontrak" runat="server" ClientInstanceName="txt_nokontrak">
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" />
                                </ValidationSettings>
                                <ReadOnlyStyle BackColor="#CCCCCC">
                                </ReadOnlyStyle>
                                <DisabledStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="Black">
                                </DisabledStyle>
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Tgl Setahun Di/Reload Cuti" ColSpan="1" FieldName="TglReloadCuti">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit ID="txt_reload_cuti" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy" ClientInstanceName="txt_reload_cuti">
                                <ReadOnlyStyle BackColor="#CCCCCC" Font-Bold="True">
                                </ReadOnlyStyle>
                                <DisabledStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="Black">
                                </DisabledStyle>
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Durasi Kontrak (Bulan)" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxSpinEdit ID="txt_durasi_kontrak" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_durasi_kontrak" DisplayFormatString="n0" Increment="0" Number="0" ReadOnly="True">
                                <SpinButtons ShowIncrementButtons="False">
                                </SpinButtons>
                                <ReadOnlyStyle BackColor="#CCCCCC" Font-Bold="True" ForeColor="#333333">
                                </ReadOnlyStyle>
                            </dx:ASPxSpinEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="PENDAPATAN DAN POTONGAN" ColCount="2" ColumnCount="2" ColSpan="1" Name="lg_pendapatan">
            <Items>
                <dx:LayoutGroup Caption="PENDAPATAN" ColSpan="1">
                    <Items>
                        <dx:LayoutItem Caption="Gaji Pokok" FieldName="Pd_GajiPokok" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_gajipokok" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_gajipokok">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalculateAssuransi(s.GetValue(),cb_karyawan.GetValue(),cb_cabang.GetValue());
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Jabatan" FieldName="Pd_T_Jabatan" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pd_jabatan" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_pd_jabatan">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Transport" ColSpan="1" FieldName="Pd_T_Transport">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pd_transport" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_transport" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Makan" FieldName="Pd_T_Makan" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pd_makan" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_pd_makan">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Premi Hadir" FieldName="Pd_T_PremiHadir" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pd_premihadir" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_pd_premihadir">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Susu" FieldName="Pd_T_Susu" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pd_susu" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_pd_susu">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Kontrak" ColSpan="1" FieldName="Pd_T_Kontrak">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pd_kontrak" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_kontrak" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Probation" ColSpan="1" FieldName="Pd_T_Probation">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pd_probation" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_probation" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Premi Ass (Non BPJS)" ColSpan="1" FieldName="Pd_T_PremiAss">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pd_premi_ass" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_premi_ass" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="T. Pajak" ColSpan="1" FieldName="Pd_T_Pajak">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pd_pajak" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_pajak" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="Assuransi" ColSpan="1">
                            <Items>
                                <dx:LayoutItem Caption="T. JKK" ColSpan="1" FieldName="Pd_T_JKK">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pd_jkk" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_jkk" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="T. JKM" ColSpan="1" FieldName="Pd_T_JKM">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pd_jkm" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_jkm" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="T. JHT" ColSpan="1" FieldName="Pd_T_JHT">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pd_jht" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_jht" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="T. JPK" ColSpan="1" FieldName="Pd_T_JPK">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pd_jpk" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_jpk" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="T. JP" ColSpan="1" FieldName="Pd_T_JP">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pd_jp" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pd_jp" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="POTONGAN" ColSpan="1">
                    <Items>
                        <dx:LayoutGroup Caption="Assuransi Ditanggung Perusahaan" ColSpan="1">
                            <Items>
                                <dx:LayoutItem Caption="Pot. P. JKK" ColSpan="1" FieldName="Pt_P_JKK">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_p_jkk" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_p_jkk" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. P. JKM" ColSpan="1" FieldName="Pt_P_JKM">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_p_jkm" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_p_jkm" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. P. JHT" ColSpan="1" FieldName="Pt_P_JHT">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_p_jht" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_p_jht" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. P. JPK" ColSpan="1" FieldName="Pt_P_JPK">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_p_jpk" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_p_jpk" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. P. JP" ColSpan="1" FieldName="Pt_P_JP">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_p_jp" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_p_jp" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                        <dx:LayoutGroup Caption="Assuransi Ditanggung Karyawan" ColSpan="1">
                            <Items>
                                <dx:LayoutItem Caption="Pot. K. JHT" ColSpan="1" FieldName="Pt_K_JHT">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_k_jht" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_k_jht" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. K. JPK" ColSpan="1" FieldName="Pt_K_JPK">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_k_jpk" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_k_jpk" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                                <dx:LayoutItem Caption="Pot. K. JP" ColSpan="1" FieldName="Pt_K_JP">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxSpinEdit ID="txt_pt_k_jp" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_k_jp" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                                <SpinButtons ShowIncrementButtons="False">
                                                </SpinButtons>
                                                <ReadOnlyStyle BackColor="#CCCCCC">
                                                </ReadOnlyStyle>
                                            </dx:ASPxSpinEdit>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                        <dx:LayoutItem Caption="Pot. PPH21" FieldName="Pt_PPH21" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pt_pph21" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ReadOnly="True" ClientInstanceName="txt_pt_pph21">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Pot. SP" FieldName="Pt_SP" ColSpan="1">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit runat="server" Number="0" ID="txt_pt_sp" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" ClientInstanceName="txt_pt_sp">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Pot. Ser Pekerja" ColSpan="1" FieldName="Pt_SerPekerja">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_pt_ser_pekerja" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_pt_ser_pekerja" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalcSummary();
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="SUMMARY" ColCount="3" ColSpan="2" ColumnCount="3" ColumnSpan="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="TotalPendapatan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_total_pend" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_total_pend" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="TotalPotongan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_total_pot" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_total_pot" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="THP">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_net" runat="server" AllowMouseWheel="False" ClientInstanceName="txt_net" DisplayFormatString="n2" Increment="0" Number="0" ReadOnly="True">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

