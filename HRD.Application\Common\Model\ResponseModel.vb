﻿Public Class ResponseModel
    'INSTANT VB NOTE: In the following line, Instant VB substituted 'Object' for 'dynamic' - this will work in VB with Option Strict Off:
    'INSTANT VB WARNING: Nullable reference types have no equivalent in VB:
    'ORIGINAL LINE: internal ResponseModel(bool success, string message, Object? output)
    Friend Sub New(ByVal success As Boolean, ByVal message As String, ByVal output As Object)
        Me.Success = success
        Me.Message = message
        Me.Output = output
    End Sub

    Friend Sub New(ByVal success As Boolean, ByVal message As String)
        Me.Success = success
        Me.Message = message
    End Sub

    Public Property Success() As Boolean

    Public Property Message() As String

    'INSTANT VB NOTE: In the following line, Instant VB substituted 'Object' for 'dynamic' - this will work in VB with Option Strict Off:
    'INSTANT VB WARNING: Nullable reference types have no equivalent in VB:
    'ORIGINAL LINE: public Object? Output {get;set;}
    Public Property Output() As Object

    'INSTANT VB NOTE: In the following line, Instant VB substituted 'Object' for 'dynamic' - this will work in VB with Option Strict Off:
    'INSTANT VB WARNING: Nullable reference types have no equivalent in VB:
    'ORIGINAL LINE: public static ResponseModel SuccessResponse(string message, Object? output)
    Public Shared Function SuccessResponse(ByVal message As String, ByVal output As Object) As ResponseModel
        Return New ResponseModel(True, message, output)
    End Function

    Public Shared Function FailureResponse(ByVal message As String) As ResponseModel
        Return New ResponseModel(False, message)
    End Function

End Class
