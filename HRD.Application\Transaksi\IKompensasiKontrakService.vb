﻿Imports HRD.Domain

Public Interface IKompensasiKontrakService
    Function GetQueryableAsync() As Task(Of ResponseModel)
    Function GetByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_kompensasi_kontrak) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function PostingAsync(ByVal TEntity As tr_kompensasi_kontrak) As Task(Of ResponseModel)
    Function VoidAsync(ByVal Id As Integer, ByVal Reason As String) As Task(Of ResponseModel)
    Function HitungKompensasi(ByVal dn As tm_karyawan_datadinas) As Task(Of ResponseModel)
End Interface