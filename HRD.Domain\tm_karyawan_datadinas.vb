'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Collections.Generic

Partial Public Class tm_karyawan_datadinas
    Public Property Id As Integer
    Public Property Tanggal As Date
    Public Property Karyawan_id As Integer
    Public Property Department_id As Nullable(Of Integer)
    Public Property Jabatan_id As Nullable(Of Integer)
    Public Property Tetap As Boolean
    Public Property TglTetap As Nullable(Of Date)
    Public Property TglAwalKontrak As Nullable(Of Date)
    Public Property TglAkhirKontrak As Nullable(Of Date)
    Public Property Pd_GajiPokok As Decimal
    Public Property Pd_T_Jabatan As Decimal
    Public Property Pd_T_Makan As Decimal
    Public Property Pd_T_PremiHadir As Decimal
    Public Property Pd_T_Susu As Decimal
    Public Property Pd_T_JKK As Decimal
    Public Property Pd_T_JKM As Decimal
    Public Property Pd_T_JHT As Decimal
    Public Property Pd_T_JPK As Decimal
    Public Property Pd_T_JP As Decimal
    Public Property TotalPendapatan As Decimal
    Public Property Pt_P_JKK As Decimal
    Public Property Pt_P_JKM As Decimal
    Public Property Pt_P_JHT As Decimal
    Public Property Pt_P_JPK As Decimal
    Public Property Pt_P_JP As Decimal
    Public Property Pt_K_JHT As Decimal
    Public Property Pt_K_JPK As Decimal
    Public Property Pt_K_JP As Decimal
    Public Property Pt_K_JPK_Mandiri As Decimal
    Public Property Pt_PPH21 As Decimal
    Public Property Pt_SP As Decimal
    Public Property TotalPotongan As Decimal
    Public Property THP As Decimal
    Public Property Area_id As Integer
    Public Property Cabang_id As Integer
    Public Property CreatedBy As String
    Public Property CreatedDate As Nullable(Of Date)
    Public Property ModifiedBy As String
    Public Property ModifiedDate As Nullable(Of Date)
    Public Property NoKontrakKerja As String
    Public Property Pd_T_Transport As Decimal
    Public Property Pd_T_Kontrak As Decimal
    Public Property Pd_T_Probation As Decimal
    Public Property Pd_T_PremiAss As Decimal
    Public Property Pd_T_Pajak As Decimal
    Public Property Pt_SerPekerja As Decimal
    Public Property NIK_Lama As String
    Public Property SaldoAwalCuti As Integer

    Public Overridable Property tm_cabang As tm_cabang
    Public Overridable Property tm_department As tm_department
    Public Overridable Property tm_jabatan As tm_jabatan
    Public Overridable Property tm_karyawan As tm_karyawan
    Public Overridable Property tr_gaji_line As ICollection(Of tr_gaji_line) = New HashSet(Of tr_gaji_line)
    Public Overridable Property tm_area As tm_area
    Public Overridable Property tr_thr_line As ICollection(Of tr_thr_line) = New HashSet(Of tr_thr_line)

End Class
