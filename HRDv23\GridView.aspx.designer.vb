﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated. 
' </auto-generated>
'------------------------------------------------------------------------------


Imports Microsoft.VisualBasic
Imports System

Partial Public Class GridViewModule

    ''' <summary>
    ''' PageToolbar control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected PageToolbar As Global.DevExpress.Web.ASPxMenu

    ''' <summary>
    ''' FilterPanel control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected FilterPanel As Global.DevExpress.Web.ASPxPanel

    ''' <summary>
    ''' SearchButtonEdit control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected SearchButtonEdit As Global.DevExpress.Web.ASPxButtonEdit

    ''' <summary>
    ''' GridView control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected GridView As Global.DevExpress.Web.ASPxGridView

    ''' <summary>
    ''' GridViewDataSource control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected GridViewDataSource As Global.System.Web.UI.WebControls.ObjectDataSource

    ''' <summary>
    ''' ContactsDataSource control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected ContactsDataSource As Global.System.Web.UI.WebControls.ObjectDataSource

    ''' <summary>
    ''' FiltersNavBar control.
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' </remarks>
    Protected FiltersNavBar As Global.DevExpress.Web.ASPxNavBar
End Class