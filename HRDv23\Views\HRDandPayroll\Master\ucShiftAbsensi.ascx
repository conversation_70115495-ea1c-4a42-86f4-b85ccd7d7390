﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucShiftAbsensi.ascx.vb" Inherits="HRDv23.ucShiftAbsensi" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxSplitter ID="ASPxSplitter1" runat="server">
    <Panes>
        <dx:SplitterPane ScrollBars="Auto">
            <ContentCollection>
<dx:SplitterContentControl runat="server">
    <dx:ASPxFormLayout ID="ASPxFormLayout2" runat="server">
        <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
        </SettingsAdaptivity>
        <Items>
            <dx:LayoutGroup Caption="Karyawan Stand By" ColSpan="1">
                <Items>
                    <dx:LayoutItem Caption="Perusahaan" ColSpan="1">
                        <LayoutItemNestedControlCollection>
                            <dx:LayoutItemNestedControlContainer runat="server">
                                <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                    <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
    grd_standBy.Refresh();
	grd_shift.Refresh();
}" />
                                    <Columns>
                                        <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                        </dx:ListBoxColumn>
                                        <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                        </dx:ListBoxColumn>
                                    </Columns>
                                    <ValidationSettings SetFocusOnError="True">
                                        <RequiredField ErrorText="Required" IsRequired="True" />
                                    </ValidationSettings>
                                </dx:ASPxComboBox>
                            </dx:LayoutItemNestedControlContainer>
                        </LayoutItemNestedControlCollection>
                    </dx:LayoutItem>
                    <dx:LayoutItem Caption="Cabang" ColSpan="1">
                        <LayoutItemNestedControlCollection>
                            <dx:LayoutItemNestedControlContainer runat="server">
                                <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                    <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_standBy.Refresh();
	grd_shift.Refresh();
}" />
                                    <Columns>
                                        <dx:ListBoxColumn FieldName="KodeCabang">
                                        </dx:ListBoxColumn>
                                        <dx:ListBoxColumn FieldName="NamaCabang">
                                        </dx:ListBoxColumn>
                                    </Columns>
                                    <ValidationSettings SetFocusOnError="True">
                                        <RequiredField ErrorText="Required" IsRequired="True" />
                                    </ValidationSettings>
                                </dx:ASPxComboBox>
                            </dx:LayoutItemNestedControlContainer>
                        </LayoutItemNestedControlCollection>
                    </dx:LayoutItem>
                    <dx:LayoutItem ColSpan="1" ShowCaption="False">
                        <LayoutItemNestedControlCollection>
                            <dx:LayoutItemNestedControlContainer runat="server">
                                <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_standBy" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id">
                                    <ClientSideEvents EndCallback="function(s, e) {
	if(s.cpUpdate){
		grd_shift.Refresh();
		s.cpUpdate=false;
		s.Refresh();
}else{
	if(!s.cpMsg==null){
		alert(s.cpMsg);
}
}

}" />
                                    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                    <SettingsDataSecurity PreventLoadClientValuesForReadOnlyColumns="False" />
                                    <SettingsPopup>
                                        <HeaderFilter MinHeight="140px">
                                        </HeaderFilter>
                                        <FilterControl AutoUpdatePosition="False">
                                        </FilterControl>
                                    </SettingsPopup>
                                    <Columns>
                                        <dx:GridViewCommandColumn SelectAllCheckboxMode="Page" ShowClearFilterButton="True" ShowInCustomizationForm="True" ShowSelectCheckbox="True" VisibleIndex="0">
                                        </dx:GridViewCommandColumn>
                                        <dx:GridViewDataTextColumn Caption="NIK" FieldName="NIK" ShowInCustomizationForm="True" VisibleIndex="3">
                                        </dx:GridViewDataTextColumn>
                                        <dx:GridViewDataTextColumn Caption="Nama" FieldName="Nama" ShowInCustomizationForm="True" VisibleIndex="4">
                                        </dx:GridViewDataTextColumn>
                                        <dx:GridViewDataTextColumn Caption="Jenis Kelamin" FieldName="tm_jenis_kelamin.JenisKelamin" ShowInCustomizationForm="True" VisibleIndex="8">
                                        </dx:GridViewDataTextColumn>
                                    </Columns>
                                </dx:ASPxGridView>
                                <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tm_karyawan" />
                            </dx:LayoutItemNestedControlContainer>
                        </LayoutItemNestedControlCollection>
                    </dx:LayoutItem>
                </Items>
            </dx:LayoutGroup>
        </Items>
    </dx:ASPxFormLayout>
                </dx:SplitterContentControl>
</ContentCollection>
        </dx:SplitterPane>
        <dx:SplitterPane AutoHeight="True" AutoWidth="True">
            <PaneStyle HorizontalAlign="Center">
            </PaneStyle>
            <ContentCollection>
<dx:SplitterContentControl runat="server">
    <br />
    <br />
    <dx:ASPxButton ID="btn_ke" runat="server" AutoPostBack="False">
        <ClientSideEvents Click="function(s, e) {
	cp_shift_absensi.PerformCallback('mutasi_shift');
}" />
        <Image IconID="scheduling_appointmentendcontinuearrow_svg_16x16">
        </Image>
    </dx:ASPxButton>
    <br />
    <br />
    <br />
    <dx:ASPxButton ID="btn_dari" runat="server" ImagePosition="Right" AutoPostBack="False">
        <ClientSideEvents Click="function(s, e) {
	cp_shift_absensi.PerformCallback('mutasi_standby');
}" />
        <Image IconID="scheduling_appointmentstartcontinuearrow_svg_16x16">
        </Image>
    </dx:ASPxButton>
                </dx:SplitterContentControl>
</ContentCollection>
        </dx:SplitterPane>
        <dx:SplitterPane ScrollBars="Auto">
            <ContentCollection>
                <dx:SplitterContentControl runat="server">
                    <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
                        <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
                        </SettingsAdaptivity>
                        <Items>
                            <dx:LayoutGroup Caption="Shift/Kelompok" ColSpan="1">
                                <Items>
                                    <dx:LayoutItem Caption="Pilih Shift/kelompok" ColSpan="1">
                                        <LayoutItemNestedControlCollection>
                                            <dx:LayoutItemNestedControlContainer runat="server">
                                                <dx:ASPxComboBox ID="cb_shift" runat="server" SelectedIndex="0" ValueType="System.Int32">
                                                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_shift.Refresh();
}" />
                                                    <Items>
                                                        <dx:ListEditItem Selected="True" Text="Shift 1" Value="1" />
                                                        <dx:ListEditItem Text="Shift 2" Value="2" />
                                                        <dx:ListEditItem Text="Shift 3" Value="3" />
                                                    </Items>
                                                </dx:ASPxComboBox>
                                            </dx:LayoutItemNestedControlContainer>
                                        </LayoutItemNestedControlCollection>
                                    </dx:LayoutItem>
                                    <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                        <LayoutItemNestedControlCollection>
                                            <dx:LayoutItemNestedControlContainer runat="server">
                                                <dx:ASPxGridView ID="ASPxGridView2" runat="server" AutoGenerateColumns="False" ClientInstanceName="grd_shift" DataSourceID="EntityServerModeDataSource2" KeyFieldName="Id">
                                                    <ClientSideEvents EndCallback="function(s, e) {
	if(s.cpUpdate){
		grd_standBy.Refresh();
		s.cpUpdate=false;
		s.Refresh();
}else{
	if(!s.cpMsg==null){
		alert(s.cpMsg);
}
}
}" />
                                                    <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                                    <SettingsPopup>
                                                        <HeaderFilter MinHeight="140px">
                                                        </HeaderFilter>
<FilterControl AutoUpdatePosition="False"></FilterControl>
                                                    </SettingsPopup>
                                                    <Columns>
                                                        <dx:GridViewCommandColumn SelectAllCheckboxMode="Page" ShowClearFilterButton="True" ShowInCustomizationForm="True" ShowSelectCheckbox="True" VisibleIndex="0">
                                                        </dx:GridViewCommandColumn>
                                                        <dx:GridViewDataTextColumn Caption="NIK" FieldName="NIK" ShowInCustomizationForm="True" VisibleIndex="3">
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Nama" FieldName="Nama" ShowInCustomizationForm="True" VisibleIndex="4">
                                                        </dx:GridViewDataTextColumn>
                                                        <dx:GridViewDataTextColumn Caption="Jenis Kelamin" FieldName="tm_jenis_kelamin.JenisKelamin" ShowInCustomizationForm="True" VisibleIndex="8">
                                                        </dx:GridViewDataTextColumn>
                                                    </Columns>
                                                </dx:ASPxGridView>
                                                <dx:EntityServerModeDataSource ID="EntityServerModeDataSource2" runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tm_karyawan" />
                                            </dx:LayoutItemNestedControlContainer>
                                        </LayoutItemNestedControlCollection>
                                    </dx:LayoutItem>
                                </Items>
                            </dx:LayoutGroup>
                        </Items>
                    </dx:ASPxFormLayout>
                </dx:SplitterContentControl>
            </ContentCollection>
        </dx:SplitterPane>
    </Panes>
</dx:ASPxSplitter>

