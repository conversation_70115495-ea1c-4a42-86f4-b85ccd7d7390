﻿Public Class RoleManager
    Private ReadOnly daoDactory As IDaoFactory
    Public Sub New(daoDactory As IDaoFactory)
        Me.daoDactory = daoDactory

    End Sub
    ReadOnly Property DataAccess As IRoleDao
        Get
            Return daoDactory.GetRoleDao
        End Get
    End Property
    ReadOnly Property GetMenuDao As IMenuDao
        Get
            Return daoDactory.GetMenuDao
        End Get
    End Property
    ReadOnly Property GetMenuRoleDao As IMenuRoleDao
        Get
            Return daoDactory.GetMenuRoleDao
        End Get
    End Property
End Class
