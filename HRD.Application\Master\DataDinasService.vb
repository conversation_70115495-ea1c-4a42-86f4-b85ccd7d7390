﻿Imports HRD.Application
Imports HRD.Domain
Public Class DataDinasService
    Implements IDataDinasService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Data Dinas Service", method, msg)
    End Sub
    Public Async Function GetDataDinassAsync() As Task(Of ResponseModel) Implements IDataDinasService.GetDataDinassAsync
        Try
            Dim datadinass = _unitOfWork.Repository(Of tm_karyawan_datadinas)().TableNoTracking.OrderBy(Function(t) t.Id) '.ToListAsync()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, datadinass)
        Catch ex As Exception
            Log(NameOf(Me.GetDataDinassAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetDataDinasByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IDataDinasService.GetDataDinasByIdAsync
        Try
            Dim datadinas = Await _unitOfWork.Repository(Of tm_karyawan_datadinas)().Get(Id)

            If datadinas IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, datadinas)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetDataDinasByIdAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_karyawan_datadinas) As Task(Of ResponseModel) Implements IDataDinasService.UpsertAsync
        Try
            If o.Id > 0 Then
                Await _unitOfWork.Repository(Of tm_karyawan_datadinas)().UpdateAsync(o.Id, o)
            Else
                Await _unitOfWork.Repository(Of tm_karyawan_datadinas)().AddAsync(o)

            End If

            If o.TglReloadCuti.HasValue Then
                Dim k = Await _unitOfWork.Repository(Of tm_karyawan).Get(o.Karyawan_id)
                k.TglReloadCuti = o.TglReloadCuti
                _unitOfWork.Repository(Of tm_karyawan).Update(k)
            End If

            If o.Tetap Then
                Dim k = Await _unitOfWork.Repository(Of tm_karyawan).Get(o.Karyawan_id)
                If k.KaryawanTetap <> True Then
                    k.KaryawanTetap = True
                    k.NIK = _myFunction.GetKodeNIK(k)
                    o.NIK_Lama = k.NIK
                    _unitOfWork.Repository(Of tm_karyawan).Update(k)
                End If
            Else
                'If o.TglReloadCuti.HasValue Then
                '    Dim k = Await _unitOfWork.Repository(Of tm_karyawan).Get(o.Karyawan_id)
                '    k.TglReloadCuti = o.TglReloadCuti
                '    _unitOfWork.Repository(Of tm_karyawan).Update(k)
                'End If
            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IDataDinasService.DeleteAsync
        Try
            Dim dn = Await _unitOfWork.Repository(Of tm_karyawan_datadinas)().Get(Id)
            If dn IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_karyawan_datadinas).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function SaveImportDataKontrakAsync(dataListImport As List(Of tm_karyawan_datadinas)) As Task(Of ResponseModel)
        Try
            Dim dataAddList As New List(Of tm_karyawan_datadinas)
            For Each x In dataListImport
                Dim o As New tm_karyawan_datadinas With {
                    .Area_id = x.Area_id,
                    .Cabang_id = x.Cabang_id,
                    .CreatedBy = x.CreatedBy,
                    .CreatedDate = x.CreatedDate,
                    .Department_id = x.Department_id,
                    .Jabatan_id = x.Jabatan_id,
                    .Karyawan_id = x.Karyawan_id,
                    .NIK_Lama = x.NIK_Lama,
                    .TglReloadCuti = x.TglReloadCuti,
                    .Tetap = x.Tetap,
                    .NoKontrakKerja = x.NoKontrakKerja,
                    .Pd_GajiPokok = x.Pd_GajiPokok,
                    .Pd_T_Jabatan = x.Pd_T_Jabatan,
                    .Pd_T_Makan = x.Pd_T_Makan,
                    .Pd_T_PremiHadir = x.Pd_T_PremiHadir,
                    .Pd_T_Susu = x.Pd_T_Susu,
                    .Pd_T_Transport = x.Pd_T_Transport,
                    .Pd_T_Kontrak = x.Pd_T_Kontrak,
                    .Pd_T_Probation = x.Pd_T_Probation,
                    .Pd_T_PremiAss = x.Pd_T_PremiAss,
                    .Pd_T_Pajak = x.Pd_T_Pajak,
                    .Pd_T_JKK = x.Pd_T_JKK,
                    .Pd_T_JKM = x.Pd_T_JKM,
                    .Pd_T_JHT = x.Pd_T_JHT,
                    .Pd_T_JPK = x.Pd_T_JPK,
                    .Pd_T_JP = x.Pd_T_JP,
                    .TotalPendapatan = x.TotalPendapatan,
                    .Pt_P_JKK = x.Pt_P_JKK,
                    .Pt_P_JKM = x.Pt_P_JKM,
                    .Pt_P_JHT = x.Pt_P_JHT,
                    .Pt_P_JPK = x.Pt_P_JPK,
                    .Pt_P_JP = x.Pt_P_JP,
                    .Pt_K_JHT = x.Pt_K_JHT,
                    .Pt_K_JPK = x.Pt_K_JPK,
                    .Pt_K_JP = x.Pt_K_JP,
                    .Pt_K_JPK_Mandiri = x.Pt_K_JPK_Mandiri,
                    .Pt_PPH21 = x.Pt_PPH21,
                    .Pt_SP = x.Pt_SP,
                    .Pt_SerPekerja = x.Pt_SerPekerja,
                    .TotalPotongan = x.TotalPotongan,
                    .THP = x.THP,
                    .SaldoAwalCuti = .SaldoAwalCuti,
                    .Tanggal = x.Tanggal,
                    .TglAkhirKontrak = x.TglAkhirKontrak,
                    .TglAwalKontrak = x.TglAwalKontrak,
                    .TglTetap = x.TglTetap
                }
                dataAddList.Add(o)
            Next

            Await _unitOfWork.Repository(Of tm_karyawan_datadinas)().AddRangeAsync(dataAddList)
            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Log(NameOf(Me.SaveImportDataKontrakAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function SaveImportAsync(dataListImport As List(Of tm_karyawan_datadinas)) As Task(Of ResponseModel)
        Try
            For Each x In dataListImport
                x.tm_area = Nothing
                x.tm_karyawan = Nothing
                x.tm_cabang = Nothing
                x.tm_jabatan = Nothing
                x.tm_department = Nothing
                _unitOfWork.Repository(Of tm_karyawan_datadinas)().Update(x)
            Next

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try

    End Function

    Public Async Function GetDataDinasByNIKAsync(nik As String) As Task(Of ResponseModel)
        Try

            Dim dns = _unitOfWork.Repository(Of tm_karyawan_datadinas)().TableNoTracking
            Dim dn = dns.Where(Function(t) t.tm_karyawan.NIK = nik).OrderByDescending(Function(od) od.Id).FirstOrDefault
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, dn)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try

    End Function
End Class
