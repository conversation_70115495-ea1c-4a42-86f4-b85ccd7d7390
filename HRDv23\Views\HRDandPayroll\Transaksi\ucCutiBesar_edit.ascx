<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucCutiBesar_edit.ascx.vb" Inherits="HRDv23.ucCutiBesar_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Perusahaan" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.String">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullText="ALL" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.String">
                                        <ClientSideEvents Init="onInitCB" />
                                        <Columns>
                                            <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ClearButton DisplayMode="Always">
                                        </ClearButton>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="No. Cuti Besar" ColSpan="1" FieldName="NoCutiBesar">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtNoCutiBesar" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Tanggal" ColSpan="1" FieldName="Tanggal">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="deTanggal" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Tahun" ColSpan="1" FieldName="Tahun">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtTahun" runat="server">
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                            <RegularExpression ErrorText="Please enter a valid year" ValidationExpression="^\d{4}$" />
                                        </ValidationSettings>
                                        <MaskSettings Mask="0000" />
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Status" ColSpan="1" FieldName="Status">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtStatus" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Keterangan" ColSpan="2" FieldName="Keterangan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxMemo ID="txtKeterangan" runat="server" Height="80px">
                                    </dx:ASPxMemo>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Total" ColSpan="1" FieldName="Total">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtTotal" runat="server">
                                        <MaskSettings Mask="&lt;0..999999999999&gt;.&lt;00..99&gt;" />
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="Daftar Detail Cuti Besar" ColSpan="2" ColumnSpan="2">
                            <Items>
                                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxGridView ID="grd_cuti_besar_line" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id">
                                                <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
                                                </SettingsAdaptivity>
                                                <Settings ShowFooter="True" ShowHeaderFilterButton="True" />
                                                <SettingsPopup>
                                                    <FilterControl AutoUpdatePosition="False">
                                                    </FilterControl>
                                                </SettingsPopup>
                                                <Columns>
                                                    <dx:GridViewDataTextColumn Caption="NIK" FieldName="NIK" ShowInCustomizationForm="True" VisibleIndex="1">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn Caption="Nama Karyawan" FieldName="NamaKaryawan" ShowInCustomizationForm="True" VisibleIndex="2">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataDateColumn Caption="Tanggal Mulai" FieldName="TanggalMulai" ShowInCustomizationForm="True" VisibleIndex="3">
                                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                                        </PropertiesDateEdit>
                                                    </dx:GridViewDataDateColumn>
                                                    <dx:GridViewDataDateColumn Caption="Tanggal Selesai" FieldName="TanggalSelesai" ShowInCustomizationForm="True" VisibleIndex="4">
                                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                                        </PropertiesDateEdit>
                                                    </dx:GridViewDataDateColumn>
                                                    <dx:GridViewDataTextColumn Caption="Jumlah Hari" FieldName="JumlahHari" ShowInCustomizationForm="True" VisibleIndex="5">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn Caption="Keterangan" FieldName="Keterangan" ShowInCustomizationForm="True" VisibleIndex="6">
                                                    </dx:GridViewDataTextColumn>
                                                </Columns>
                                                <TotalSummary>
                                                    <dx:ASPxSummaryItem DisplayFormat="{0:n0}" FieldName="JumlahHari" ShowInColumn="Jumlah Hari" SummaryType="Sum" />
                                                </TotalSummary>
                                            </dx:ASPxGridView>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Created By" ColSpan="1" FieldName="CreatedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtCreatedBy" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Created Date" ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="deCreatedDate" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Modified By" ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtModifiedBy" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Modified Date" ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="deModifiedDate" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Posted By" ColSpan="1" FieldName="PostedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txtPostedBy" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Posted Date" ColSpan="1" FieldName="PostedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="dePostedDate" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>        <dx:LayoutGroup Caption="Action" ColCount="4" ColSpan="1" ColumnCount="4">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Simpan" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_cuti_besar.PerformCallback('save');
		s.SetEnabled(false);
	};
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_posting" runat="server" AutoPostBack="False" Text="Posting" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (confirm('Are you sure you want to post this transaction?')) {
		cp_cuti_besar.PerformCallback('posting');
		s.SetEnabled(false);
	};
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_void" runat="server" AutoPostBack="False" Text="Void" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (confirm('Are you sure you want to void this transaction?')) {
		cp_cuti_besar.PerformCallback('void');
		s.SetEnabled(false);
	};
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Kembali" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_cuti_besar.PerformCallback('back');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>
<dx:EntityServerModeDataSource runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tr_cuti_besar_line" EnableDelete="True" EnableInsert="True" EnableUpdate="True" ID="EntityServerModeDataSource1"></dx:EntityServerModeDataSource>


