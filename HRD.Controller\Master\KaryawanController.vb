﻿Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap
Imports Z.EntityFramework.Plus
Imports System.Data.Entity
Public Class KaryawanController
    Inherits Controller(Of tm_karyawan)
    Implements IKaryawanListController, IKaryawanEditController

    Public ReadOnly Property DataList(bBerhenti As Boolean, area_id As String, Optional cabang_id As String = Nothing) As IQueryable(Of tm_karyawan) Implements IKaryawanListController.DataList
        Get
            Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
            Dim r = serv.GetKaryawansAsync().GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tm_karyawan) = r.Output
                ' Include data dinas untuk mendapatkan informasi departemen
                os = os.Include("tm_karyawan_datadinas.tm_department")
                os = os.Where(Function(f) f.Berhenti_b = bBerhenti)
                os = os.Where(Function(f) f.Area_id = area_id)
                If cabang_id IsNot Nothing Then
                    os = os.Where(Function(f) f.Cabang_id = cabang_id)
                End If
                Dim mUser = AuthHelper.GetLoggedInUserInfo
                Dim areIdList = mUser.AreaIdPermisionList
                os = os.Where(Function(f) areIdList.Contains(f.Area_id))
                Select Case mUser.PermisionArea
                    Case 0
                        os = os.Where(Function(f) f.Cabang_id = mUser.Cabang_id)
                        'Case 1
                        '    os = os.Where(Function(f) f.Area_id = mUser.Area_id)
                End Select
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If

        End Get
    End Property

    Public Function UpdateShift(ListIDStandBy As IList(Of Integer), Shift_id As String, ByRef _sMessage As String) As Boolean

        _sMessage = Nothing
        Using ctx As New HRDEntities
            Try
                'For Each x In listID
                '    Dim o As New m_karyawan With {.id = x}
                '    o.Ship = shift_id
                '    ctx.Entry(o).State = Entity.EntityState.Modified
                'Next

                ctx.tm_karyawan.Where(Function(w) ListIDStandBy.Contains(w.Id)).Update(Function(f) New tm_karyawan With {.Ship = Shift_id})
                ctx.SaveChanges()
                Return True
            Catch ex As Exception
                _sMessage = ex.Message
                Return False
            End Try

        End Using

    End Function

    Public Function UpdateStandBy(listID As IList(Of Integer), ByRef sMessage As String) As Boolean
        sMessage = Nothing
        Using ctx As New HRDEntities
            Try

                ctx.tm_karyawan.Where(Function(w) listID.Contains(w.Id)).Update(Function(f) New tm_karyawan With {.Ship = Nothing})
                ctx.SaveChanges()
                Return True
            Catch ex As Exception
                sMessage = ex.Message
                Return False
            End Try

        End Using

    End Function

    Private mDataListImport As IList(Of tm_karyawan)
    Public ReadOnly Property DataListImport As IList(Of tm_karyawan) Implements IKaryawanListController.DataListImport
        Get
            If mDataListImport Is Nothing Then
                mDataListImport = New List(Of tm_karyawan)
            End If
            Return mDataListImport
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_karyawan With {.Berhenti_b = False}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawanByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)

        End If

    End Sub

    Public Overrides Sub DeleteItem(id As String)

        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult

        If r.Success Then
            QueryCacheManager.ExpireTag("c_karyawan")
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If


    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_karyawan)
        TEntity.Umur = DateDiff(DateInterval.Year, TEntity.TglLahir.Value, Now)

        ExpressMapper.Mapper.Register(Of tm_karyawan, tm_karyawan)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_agama) _
            .Ignore(Function(x) x.tm_event_setting_line) _
            .Ignore(Function(x) x.tm_jenis_kelamin) _
            .Ignore(Function(x) x.tm_jurusan) _
            .Ignore(Function(x) x.tm_karyawan_datadinas) _
            .Ignore(Function(x) x.tm_namabank) _
            .Ignore(Function(x) x.tm_off_karyawan_line) _
            .Ignore(Function(x) x.tm_pendidikan) _
            .Ignore(Function(x) x.tm_status_perkawinan) _
            .Ignore(Function(x) x.tm_status_PTKP) _
            .Ignore(Function(x) x.tr_absensi) _
            .Ignore(Function(x) x.tr_gaji_line) _
            .Ignore(Function(x) x.tr_ijin) _
            .Ignore(Function(x) x.tr_pinjaman_bayar) _
            .Ignore(Function(x) x.tr_register_pinjaman) _
            .Ignore(Function(x) x.tr_spk_lembur_line) _
            .Ignore(Function(x) x.tr_thr_line) _
            .Ignore(Function(x) x.tr_tunjangan_onetime) _
            .Ignore(Function(x) x.tr_kompensasi_kontrak_line) _
            .Ignore(Function(x) x.tm_bonus_thr)


        Dim o = ExpressMapper.Mapper.Map(Of tm_karyawan, tm_karyawan)(TEntity)
        If o.Id <= 0 Then
            o.Createdby = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now

        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            QueryCacheManager.ExpireTag("c_karyawan")
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
    Sub AddNewBerhenti()
        SelectedItem = New tm_karyawan With {.Tgl_Berhenti = Now}
    End Sub
    Public Sub SaveBerhenti(TEntity As tm_karyawan) Implements IKaryawanEditController.SaveBerhenti
        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawanByIdAsync(TEntity.Id).GetAwaiter.GetResult
        If r.Success Then
            Dim o As tm_karyawan = r.Output
            o.Berhenti_b = True
            o.Tgl_Berhenti = TEntity.Tgl_Berhenti
            o.AlasanBerhenti = TEntity.AlasanBerhenti
            Dim rSave = serv.UpsertAsync(o).GetAwaiter.GetResult
            If rSave.Success Then
                QueryCacheManager.ExpireTag("c_karyawan")
                Saved = True
            Else
                Saved = False
                sMsg = MessageFormatter.GetFormattedErrorMessage(rSave.Message)
            End If
        End If
    End Sub
    Sub DeleteBerhenti(id As String)

        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawanByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Dim o As tm_karyawan = r.Output
            o.Berhenti_b = False
            o.Tgl_Berhenti = Nothing
            o.AlasanBerhenti = Nothing
            Dim rSave = serv.UpsertAsync(o).GetAwaiter.GetResult
            If rSave.Success Then
                QueryCacheManager.ExpireTag("c_karyawan")
                Reset()
            Else
                Saved = False
                sMsg = MessageFormatter.GetFormattedErrorMessage(rSave.Message)
            End If
        End If
    End Sub

    Public Function CekExistKaryawans(kodePerusahaan As String, kodeCabang As String, nik As String) As Boolean Implements IKaryawanListController.CekExistKaryawans
        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            Dim o = os.Where(Function(x) x.tm_area.KodeArea = kodePerusahaan And x.tm_cabang.KodeCabang = kodeCabang And x.NIK = nik).FirstOrDefault
            If o IsNot Nothing Then
                Return True
            Else
                Return False
            End If
        Else
            Return False
        End If
    End Function
    Sub SaveImport()
        Try

            Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()

            Dim r = serv.ImportAsync(DataListImport).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Saved = False
            End If
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Function KaryawanNonShift(cabang_id As String) As IQueryable(Of tm_karyawan) Implements IKaryawanListController.KaryawanNonShift
        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            os = os.Where(Function(x) x.Cabang_id = cabang_id And x.Ship.HasValue <> True)
            Return os
        Else
            Return Nothing
        End If
    End Function

    Public Function KaryawanByShip(cabang_id As String, shift As String) As IQueryable(Of tm_karyawan) Implements IKaryawanListController.KaryawanByShip
        Dim serv = ObjectFactory.GetInstance(Of KaryawanService)()
        Dim r = serv.GetKaryawansAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_karyawan) = r.Output
            os = os.Where(Function(x) x.Cabang_id = cabang_id And x.Ship = shift)
            Return os
        Else
            Return Nothing
        End If
    End Function
End Class
Public Interface IKaryawanListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bBerhenti As Boolean, area_id As String, Optional cabang_id As String = Nothing) As IQueryable(Of tm_karyawan)
    ReadOnly Property DataListImport() As IList(Of tm_karyawan)
    Function CekExistKaryawans(kodePerusahaan As String, kodeCabang As String, nik As String) As Boolean
    Function KaryawanNonShift(cabang_id As String) As IQueryable(Of tm_karyawan)
    Function KaryawanByShip(cabang_id As String, shift As String) As IQueryable(Of tm_karyawan)
End Interface
Public Interface IKaryawanEditController
    Inherits IControllerMain, IControllerEdit(Of tm_karyawan)

    Sub SaveBerhenti(TEntity As tm_karyawan)
End Interface