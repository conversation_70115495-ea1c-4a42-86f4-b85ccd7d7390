﻿Imports HRD.Application
Imports HRD.Domain
Imports MediatR
Imports Tanneryd.BulkOperations.EF6.Model
Imports Tanneryd.BulkOperations.EF6
Imports Z.EntityFramework.Plus

Public Class GajiService
    Implements IGajiService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction




    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        Me._myFunction = myFunction


    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Gaji Service", method, msg)
    End Sub

    Public Async Function GetGajisAsync() As Task(Of ResponseModel) Implements IGajiService.GetGajisAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_gaji)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetGajisAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetGajiByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IGajiService.GetGajiByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_gaji)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetGajiByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Public Async Function UpsertAsync(o As tr_gaji) As Task(Of ResponseModel) Implements IGajiService.UpsertAsync
        Try
            Dim queryAllCabang As New GetAllTmCabangQuery
            Dim _cabangCommandHandler = New TmCabangQueryHandler(_unitOfWork)
            Dim cabangs = Await _cabangCommandHandler.Handle(queryAllCabang)
            cabangs = cabangs.Where(Function(f) f.Area_id = o.Area_id)
            If o.Cabang_id <> Nothing Then
                cabangs = cabangs.Where(Function(f) f.Id = o.Cabang_id)
            End If
            Dim cabangList = cabangs.ToList()

            Dim listGajiNew As New List(Of tr_gaji)

            For Each x In cabangList
                Dim gj = _unitOfWork.Repository(Of tr_gaji).Table.
                Where(Function(d) d.Cabang_id = x.Id AndAlso d.Tahun = o.Tahun AndAlso d.Bulan = o.Bulan).
                FirstOrDefault()

                If gj IsNot Nothing Then
                    If gj.Posted Then
                        Continue For
                    End If
                    Await _unitOfWork.Repository(Of tr_gaji_line).Delete(Function(d) d.Gaji_id = gj.Id)
                    Await _unitOfWork.Repository(Of tr_gaji).DeleteAsync(gj.Id)
                End If

                gj = New tr_gaji With {
                .Cabang_id = x.Id,
                .Tahun = Format(o.Tanggal, "yyyy"),
                .Bulan = Format(o.Tanggal, "MM"),
                .Tanggal = o.Tanggal,
                .Area_id = o.Area_id,
                .Keterangan = o.Keterangan,
                .CreatedBy = o.CreatedBy,
                .CreatedDate = o.CreatedDate
            }

                listGajiNew.Add(gj)

                Dim g = Await ProsesGajiCabangs(gj)

                If Not g.Success Then
                    Return ResponseModel.FailureResponse(g.Message)
                End If
            Next

            _unitOfWork.Save()

            If listGajiNew.Any() Then
                Dim request = New BulkInsertRequest(Of tr_gaji) With {
                .Entities = listGajiNew,
                .Recursive = True
            }
                _unitOfWork.GetCurrentContext.BulkInsertAll(request)
            End If



            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function


    'Public Async Function UpsertAsync(o As tr_gaji) As Task(Of ResponseModel) Implements IGajiService.UpsertAsync
    '    Try



    '        Dim queryAllCabang As New GetAllTmCabangQuery
    '        Dim _cabangCommandHandler = New TmCabangQueryHandler(_unitOfWork)
    '        Dim cabangs = Await _cabangCommandHandler.Handle(queryAllCabang)
    '        cabangs = cabangs.Where(Function(f) f.Area_id = o.Area_id)
    '        If o.Cabang_id <> Nothing Then
    '            cabangs = cabangs.Where(Function(f) f.Id = o.Cabang_id)
    '        End If
    '        Dim cabangList = cabangs.ToList

    '        Dim listGajiNew As New List(Of tr_gaji)
    '        Dim listGajiUpdate As New List(Of tr_gaji)

    '        For Each x In cabangList
    '            Dim gj = _unitOfWork.Repository(Of tr_gaji).Table.Where(Function(d) d.Cabang_id = x.Id AndAlso d.Tahun = o.Tahun AndAlso d.Bulan = o.Bulan).FirstOrDefault
    '            If gj IsNot Nothing Then
    '                If gj.Posted Then
    '                    Continue For
    '                End If
    '                Await _unitOfWork.Repository(Of tr_gaji_line).Delete(Function(d) d.Gaji_id = gj.Id)

    '                gj.Keterangan = o.Keterangan
    '                gj.ModifiedBy = o.ModifiedBy
    '                gj.ModifiedDate = o.ModifiedDate

    '                listGajiUpdate.Add(gj)
    '                '_unitOfWork.Repository(Of tr_gaji)().Update(gj)
    '            Else

    '                gj = New tr_gaji

    '                gj.Cabang_id = x.Id
    '                gj.Tahun = Format(o.Tanggal, "yyyy")
    '                gj.Bulan = Format(o.Tanggal, "MM")
    '                gj.Tanggal = o.Tanggal
    '                gj.Area_id = o.Area_id
    '                gj.Keterangan = o.Keterangan


    '                gj.CreatedBy = o.CreatedBy
    '                gj.CreatedDate = o.CreatedDate

    '                'Await _unitOfWork.Repository(Of tr_gaji)().AddAsync(gj)
    '                listGajiNew.Add(gj)
    '            End If



    '            Dim g = Await ProsesGajiCabangs(gj)

    '            If g.Success Then

    '            Else
    '                Return ResponseModel.FailureResponse(g.Message)
    '            End If
    '        Next

    '        Dim request = New BulkInsertRequest(Of tr_gaji) With {
    '            .Entities = listGajiNew
    '            }
    '        _unitOfWork.GetCurrentContext.BulkInsertAll(request)

    '        Dim requestUpdate = New BulkUpdateRequest() With {
    '            .Entities = listGajiUpdate
    '            }
    '        _unitOfWork.GetCurrentContext.BulkUpdateAll(requestUpdate)

    '        _unitOfWork.Save()



    '        Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
    '    Catch ex As Exception
    '        Log(NameOf(Me.UpsertAsync), ex.Message)

    '        Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
    '    End Try
    'End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IGajiService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_gaji)().Get(Id)
            If o IsNot Nothing Then
                'Dim pinj = _unitOfWork.Repository(Of tr_pinjaman_move).Table.Where(Function(f) f.tr_gaji_line.Gaji_id = o.Id)
                'For Each x In pinj
                '    Dim regPinj = _unitOfWork.Repository(Of tr_register_pinjaman).Table.Where(Function(f) f.Id = x.Pinjaman_id).FirstOrDefault
                '    If regPinj IsNot Nothing Then
                '        regPinj.Saldo += (x.Amount * (-1))
                '        _unitOfWork.Repository(Of tr_register_pinjaman).Update(regPinj)
                '    End If

                'Next
                'Await _unitOfWork.Repository(Of tr_pinjaman_move).Delete(Function(d) d.tr_gaji_line.Gaji_id = o.Id)
                Await _unitOfWork.Repository(Of tr_gaji_line).Delete(Function(d) d.Gaji_id = o.Id)
                Await _unitOfWork.Repository(Of tr_gaji).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Public Async Function Posting(o As tr_gaji, gjLines As List(Of tr_gaji_line)) As Task(Of ResponseModel)
        Try
            ' Update the main gaji object
            _unitOfWork.Repository(Of tr_gaji)().Update(o)

            ' Define batch size
            Const batchSize As Integer = 50

            ' Split gjLines into batches
            Dim batches = gjLines.
            Select(Function(gjLine, index) New With {.Index = index, .GajiLine = gjLine}).
            GroupBy(Function(x) x.Index \ batchSize).
            Select(Function(g) g.Select(Function(x) x.GajiLine).ToList()).
            ToList()

            Dim sTextErr As New Text.StringBuilder

            ' Process each batch sequentially
            For Each batch In batches
                Dim taskList = batch.Select(Function(s) PostingGajiPerKaryawan(o, s)).ToList()
                Dim results = Await Task.WhenAll(taskList)

                ' Handle errors within the current batch
                Dim errors = results.Where(Function(r) Not r.Success)
                For Each errorResult In errors
                    sTextErr.Append($"{errorResult.Message}</BR>")
                Next
            Next

            ' Check if there were any errors across all batches
            If sTextErr.Length > 0 Then
                Return ResponseModel.FailureResponse(sTextErr.ToString())
            End If

            ' Update related records in tr_tunjangan_onetime
            _unitOfWork.Repository(Of tr_tunjangan_onetime).
            Table.
            Where(Function(f) Not f.Posted AndAlso f.PeriodeGaji.Year = o.Tanggal.Year AndAlso f.PeriodeGaji.Month = o.Tanggal.Month).
            Update(Function(u) New tr_tunjangan_onetime With {.Posted = True})

            ' Save changes to the database
            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            ' Log exception and return failure response
            Log(NameOf(Me.Posting), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    'Public Async Function Posting(o As tr_gaji, gjLines As List(Of tr_gaji_line)) As Task(Of ResponseModel)
    '    Try



    '        _unitOfWork.Repository(Of tr_gaji)().Update(o)

    '        Dim taskList = gjLines.Select(Function(s) PostingGajiPerKaryawan(o, s))

    '        Dim r = Await Task.WhenAll(taskList)

    '        Dim oErr = r.Where(Function(f) f.Success <> True)
    '        Dim sTextErr As New Text.StringBuilder
    '        If oErr.Count > 0 Then
    '            For Each x In oErr
    '                sTextErr.Append($"{x.Message}</BR>")
    '            Next
    '            Return ResponseModel.FailureResponse(sTextErr.ToString)
    '        End If

    '        _unitOfWork.Repository(Of tr_tunjangan_onetime).Table.Where(Function(f) f.Posted <> True And f.PeriodeGaji.Year = o.Tanggal.Year And f.PeriodeGaji.Month = o.Tanggal.Month).Update(Function(u) New tr_tunjangan_onetime With {.Posted = True})

    '        _unitOfWork.Save()

    '        Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
    '    Catch ex As Exception
    '        Log(NameOf(Me.Posting), ex.Message)

    '        Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
    '    End Try
    'End Function
    Private Async Function PostingGajiPerKaryawan(gj As tr_gaji, gjline As tr_gaji_line) As Task(Of ResponseModel)

        Try
            Dim tgl1 As DateTime = Format(gj.Tanggal, "yyyy-MM-20 23:59:59")

            Dim kasbons = _unitOfWork.Repository(Of tr_register_pinjaman).Table.Where(Function(f) f.Karyawan_id = gjline.Karyawan_id And f.Posted = True And f.Saldo > 0 And f.CicilanPerBulam > 0)
            kasbons = kasbons.Where(Function(f) f.Tanggal <= tgl1)
            For Each x In kasbons
                Dim pMove As New tr_pinjaman_move
                With pMove
                    .Amount = If(x.Saldo > x.CicilanPerBulam, x.CicilanPerBulam, x.Saldo) * (-1)
                    .GajiLine_id = gjline.Id
                    .Memo = $"Proses Gaji Periode {gj.Tahun}-{gj.Bulan}"
                    .Pinjaman_id = x.Id
                    .TipeTrans = "GJ"
                    .TransDate = gj.Tanggal

                    x.Saldo += .Amount
                    x.TotalBayar += .Amount * (-1)
                    If x.Saldo <= 0 Then
                        x.Status = "Completed"
                    Else
                        x.Status = "Partial"
                    End If
                    Await _unitOfWork.Repository(Of tr_pinjaman_move).AddAsync(pMove)
                    _unitOfWork.Repository(Of tr_register_pinjaman).Update(x)
                End With
            Next

            Return ResponseModel.SuccessResponse("ok", Nothing)
        Catch ex As Exception

            Log(NameOf(Me.PostingGajiPerKaryawan), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try


    End Function
    Private Async Function ProsesGajiCabangs(gj As tr_gaji) As Task(Of ResponseModel)
        Try
            ' Define batch size
            Const batchSize As Integer = 50

            ' Calculate relevant date ranges
            'Dim tglStart As Date = Format(gj.Tanggal, "yyyy-MM-20") 'gj.Tanggal.AddDays(-gj.Tanggal.Day + 1) ' First day of the month

            Dim tglEnd As Date = Format(gj.Tanggal, "yyyy-MM-20") 'gj.Tanggal.AddDays(-1) ' Last day of the previous month
            Dim tglStart = tglEnd.AddMonths(-1)
            tglStart = Format(tglStart, "yyyy-MM-21")
            Dim tglBerhenti As Date = Format(gj.Tanggal, "yyyy-MM-21") ' Start date for considering resignations

            ' Fetch eligible employees
            Dim _kars = _unitOfWork.Repository(Of tm_karyawan).
            TableNoTracking().
            Where(Function(f) f.Cabang_id = gj.Cabang_id AndAlso
                              (Not f.Berhenti_b OrElse (f.Berhenti_b AndAlso f.Tgl_Berhenti.Value >= tglBerhenti)) AndAlso
                              f.TglMasuk <= tglEnd).
            ToList()

            ' Break employees into batches
            Dim batches = _kars.
            Select(Function(k, index) New With {.Index = index, .Karyawan = k}).
            GroupBy(Function(x) x.Index \ batchSize).
            Select(Function(g) g.Select(Function(x) x.Karyawan).ToList()).
            ToList()

            Dim gajiLines As New List(Of tr_gaji_line)
            Dim errorMessages As New Text.StringBuilder

            ' Process each batch sequentially
            For Each batch In batches
                Dim tasks = batch.Select(Function(k) ProsesGajiPerKaryawan(gj, k)).ToList()
                Dim results = Await Task.WhenAll(tasks)

                ' Separate success and error responses for the current batch
                Dim successfulResults = results.Where(Function(r) r.Success).ToList()
                Dim failedResults = results.Where(Function(r) Not r.Success).ToList()

                ' Append errors to the log
                For Each r In failedResults
                    errorMessages.AppendLine(r.Message)
                Next

                ' Collect successful gaji lines
                gajiLines.AddRange(successfulResults.Select(Function(r) CType(r.Output, tr_gaji_line)))
            Next

            ' Handle errors if any
            If errorMessages.Length > 0 Then
                Return ResponseModel.FailureResponse(errorMessages.ToString())
            End If

            ' Update gaji with calculated lines
            gj.tr_gaji_line = gajiLines

            ' Aggregate totals
            gj.TotalPendapatan = gajiLines.Sum(Function(l) l.TotalPendapatan)
            gj.TotalPotongan = gajiLines.Sum(Function(l) l.TotalPotongan)
            gj.TotalNet = gajiLines.Sum(Function(l) l.THP)

            Return ResponseModel.SuccessResponse("ok", gj)
        Catch ex As Exception
            ' Log error and return failure response
            Log(NameOf(Me.ProsesGajiCabangs), ex.ToString())
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function


    Private Async Function ProsesGajiPerKaryawan(gj As tr_gaji, k As tm_karyawan) As Task(Of ResponseModel)
        Dim tgl As Date = Format(gj.Tanggal, "yyyy-MM-21")
        Dim tgl1 As Date = DateAdd(DateInterval.Month, -1, tgl)
        Dim tgl2 As Date = DateAdd(DateInterval.Day, -1, tgl)
        Dim tglLembur1 As Date = Format(tgl1, "yyyy-MM-16")
        Dim tglLembur2 As Date = Format(tgl2, "yyyy-MM-15")
        Dim dn = k.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault
        If k.Id = 442 Then
            Dim y = k.Id
        End If
        If dn Is Nothing Then
            Return ResponseModel.FailureResponse($"Data Dinas Untuk Karyawan : {k.NIK} - {k.Nama} Belum Ada")
        End If
        Try
            Dim abs = _myFunction.GetAbsensis(k.Id, tgl1, tgl2)

            Dim jmlOff = abs.Where(Function(f) f.Off_b = True AndAlso f.CutiBersama <> True).Count

            Dim absLembur = _myFunction.GetAbsensis(k.Id, tglLembur1, tglLembur2)
            Dim mAllDays As Integer = 0
            Dim mJmlHariKerja As Integer = _myFunction.CalculateWeekdays(gj.Cabang_id, tgl1, tgl2, mAllDays)
            Dim gantihari = abs.Where(Function(f) f.GantiHari = True).Count
            mJmlHariKerja += gantihari
            'If k.TglMasuk >= tgl1 And k.TglMasuk <= tgl2 Then
            '    mJmlHariKerja = _myFunction.CalculateWeekdays(gj.Cabang_id, k.TglMasuk, tgl2, mAllDays)
            'End If
            Dim gjLine As New tr_gaji_line
            With gjLine
                .JumlahAlpha = abs.Count(Function(s) s.Alpha_b = True)
                .JumlahCuti = abs.Count(Function(s) s.Cuti_b = True)
                .JumlahIjinDispensasi = abs.Where(Function(f) f.Ijin_id.HasValue And f.tr_ijin.tm_ijin_master.KodeIjin = "ID").Count(Function(s) s.Ijin_b)
                .JumlahSakit = abs.Where(Function(f) f.Ijin_id.HasValue And f.tr_ijin.tm_ijin_master.KodeIjin = "S").Count
                .JumlahIjin = abs.Where(Function(f) f.Ijin_id.HasValue And (f.tr_ijin.tm_ijin_master.KodeIjin = "I1" Or f.tr_ijin.tm_ijin_master.KodeIjin = "I2" Or f.tr_ijin.tm_ijin_master.KodeIjin = "IP")).Count(Function(s) s.Ijin_b) + .JumlahIjinDispensasi + .JumlahSakit
                .JumlahLembur = absLembur.Count(Function(s) s.Overtime > 0 Or s.OverTime_HLibur > 0)
                .JumlahPotongGaji = abs.Count(Function(s) s.PotongGaji = True)
                Dim jmlMasuk As Integer = abs.Count(Function(s) s.Masuk_b = True)
                If k.TipeAbsensiOFF = 0 Or k.TipeAbsensiOFF = 3 Then
                    .HariKerja = mJmlHariKerja
                Else
                    .HariKerja = mAllDays - jmlOff

                End If

                .Karyawan_id = k.Id

                .Pd_GajiPokok = dn.Pd_GajiPokok

                Dim cAss = _myFunction.CalculateAssuransi(.Pd_GajiPokok, .Karyawan_id, gj.Cabang_id)

                .Pd_T_Jabatan = dn.Pd_T_Jabatan
                .Pd_T_Transport = dn.Pd_T_Transport
                .Pd_T_JHT = cAss.Pd_JHT
                .Pd_T_JKK = cAss.Pd_JKK
                .Pd_T_JKM = cAss.Pd_JKM
                .Pd_T_JP = cAss.Pd_JP
                .Pd_T_JPK = cAss.Pd_JPK
                .Pd_T_Makan = dn.Pd_T_Makan
                If .JumlahAlpha > 0 Or .JumlahPotongGaji > 0 Then
                    .Pd_T_PremiHadir = 0
                Else
                    .Pd_T_PremiHadir = dn.Pd_T_PremiHadir
                End If

                .Pd_T_Susu = dn.Pd_T_Susu
                .Pd_T_Kontrak = dn.Pd_T_Kontrak
                .Pd_T_Probation = dn.Pd_T_Probation
                .Pd_T_PremiAss = dn.Pd_T_PremiAss

                Dim _pendTambahan = GetPendapatanTambahan(gj.Tanggal, k.Id)
                .Pd_Insentif = _pendTambahan._insentif
                .Pd_T_Lain = _pendTambahan._pendLain

                Dim jmlJamLemburLN As Decimal = If(absLembur.Sum(Function(s) CType(s.OverTime_HLibur, Decimal?)), 0)
                Dim jmlJamLemburBiasa As Decimal = If(absLembur.Sum(Function(s) CType(s.Overtime, Decimal?)), 0)
                .Pd_Lembur = GetUpahLemburHariBiasa(.Pd_GajiPokok + .Pd_T_Jabatan + .Pd_T_Kontrak + .Pd_T_Makan + .Pd_T_Probation + .Pd_T_Susu + .Pd_T_Transport, absLembur) + GetUpahLemburHariLibur(.Pd_GajiPokok + .Pd_T_Jabatan + .Pd_T_Kontrak + .Pd_T_Makan + .Pd_T_Probation + .Pd_T_Susu + .Pd_T_Transport, absLembur)

                .TotalPendapatan = .Pd_GajiPokok + .Pd_T_Jabatan + .Pd_T_Transport + .Pd_T_Makan + .Pd_T_PremiHadir + .Pd_T_Susu + + .Pd_T_Kontrak + .Pd_T_Probation + .Pd_T_PremiAss + .Pd_T_JKK + .Pd_T_JKM + .Pd_T_JHT + .Pd_T_JPK + .Pd_T_JP + .Pd_Insentif + .Pd_T_Lain + .Pd_Lembur
                .TotalPendapatan = Math.Round(.TotalPendapatan, 0)
                .Pot_Alpha = (.JumlahAlpha + .JumlahPotongGaji) * ((.Pd_GajiPokok) / .HariKerja)
                .Pt_Kasbon = _myFunction.GetPotonganPinjaman(.Karyawan_id, gj.Tanggal)
                .Pt_K_JHT = cAss.Pt_K_JHT
                .Pt_K_JP = cAss.Pt_K_JP
                .Pt_K_JPK = cAss.Pt_K_JPK
                .Pt_K_JPK_Mandiri = 0
                If gj.Tanggal.Month = 12 Then
                    Dim totPendapatan = .TotalPendapatan + _myFunction.GetTotalPendapatanTahunan(.Karyawan_id, gj.Tanggal.Year)
                    totPendapatan = Math.Round(totPendapatan, 0)
                    .Pt_PPH21 = _myFunction.PPh21CalculatorTahunan(totPendapatan, 0, k.tm_status_PTKP.KodeStatus)
                    Dim totPph21 = _myFunction.GetTotalPph21Tahunan(.Karyawan_id, gj.Tanggal.Year)
                    .Pt_PPH21 = .Pt_PPH21 - totPph21
                Else
                    Dim mter = _myFunction.PPh21CalculatorBulanan(.TotalPendapatan, k.tm_status_PTKP.KodeStatus)
                    .Pt_PPH21 = mter.nilaiPajak
                    .Ter_pajak_id = mter.ter_pajak_id
                    .Ter_pajak_percent = mter.tarifPercent
                End If
                .StatusPTKP_id = k.StatusPerkawinan_id
                .Pt_P_JHT = cAss.Pt_P_JHT
                .Pt_P_JKK = cAss.Pt_P_JKK
                .Pt_P_JKM = cAss.Pt_P_JKM
                .Pt_P_JP = cAss.Pt_P_JP
                .Pt_P_JPK = cAss.Pt_P_JPK
                .Pt_SP = dn.Pt_SP
                .Pt_SerPekerja = dn.Pt_SerPekerja

                .TotalPotongan = .Pot_Alpha + .Pt_Kasbon + .Pt_K_JHT + .Pt_K_JP + .Pt_K_JPK + .Pt_K_JPK_Mandiri + .Pt_P_JHT + .Pt_P_JKK + .Pt_P_JKM + .Pt_P_JP + .Pt_P_JPK + .Pt_SP + .Pt_SerPekerja
                .TotalPotongan = Math.Round(.TotalPotongan, 0)
                .Pd_T_Pajak = .Pt_PPH21

                .THP = .TotalPendapatan - .TotalPotongan

                .DataDinas_id = dn.Id
            End With

            Return ResponseModel.SuccessResponse("ok", gjLine)
        Catch ex As Exception
            Log(NameOf(Me.ProsesGajiPerKaryawan), ex.Message)
            Return ResponseModel.FailureResponse($"Proses Gagal Proses Per Karyawan : {k.Nama}")
        End Try


    End Function
    Private Function GetPendapatanTambahan(pPeriodeGaji As Date, kar_id As String) As (_insentif As Decimal, _pendLain As Decimal)
        Dim o = _unitOfWork.Repository(Of tr_tunjangan_onetime).TableNoTracking.Where(Function(f) f.PeriodeGaji.Year = pPeriodeGaji.Year And f.PeriodeGaji.Month = pPeriodeGaji.Month And f.Karyawan_id = kar_id).FirstOrDefault
        If o Is Nothing Then
            Return (0, 0)
        End If
        Return (o.TunjanganInsentif, o.TunjanganLain)
    End Function
    Private Function GetUpahLemburHariBiasa(gp As Decimal, abs As IQueryable(Of tr_absensi)) As Decimal
        Dim J1 As Decimal = If(abs.Sum(Function(f) CType(f.Lembur_HKerja_1, Decimal?)), 0) / 60
        Dim J2 As Decimal = If(abs.Sum(Function(f) CType(f.Lembur_HKerja_2, Decimal?)), 0) / 60

        Dim JmlJ1 As Decimal = (J1 * 1.5 * (gp / 173))
        Dim JmlJ2 As Decimal = (J2 * 2 * (gp / 173))

        Return JmlJ1 + JmlJ2
    End Function
    Private Function GetUpahLemburHariLibur(gp As Decimal, abs As IQueryable(Of tr_absensi)) As Decimal

        Dim J8 As Decimal = If(abs.Sum(Function(f) CType(f.Lembur_HLibur_1_7, Decimal?)), 0) / 60
        Dim J9 As Decimal = If(abs.Sum(Function(f) CType(f.Lembur_HLibur_8, Decimal?)), 0) / 60
        Dim J10 As Decimal = If(abs.Sum(Function(f) CType(f.Lembur_HLibur_9_10, Decimal?)), 0) / 60

        Dim JmlJ8 As Decimal = (J8 * 2 * (gp / 173))
        Dim JmlJ9 As Decimal = (J9 * 3 * (gp / 173))
        Dim JmlJ10 As Decimal = (J10 * 4 * (gp / 173))

        Return JmlJ8 + JmlJ9 + JmlJ10



    End Function
End Class
