﻿
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap
Imports HRD.Controller

Public Class EventSettingController
    Inherits Controller(Of tm_event_setting)
    Implements IEventSettingListController, IEventSettingEditController


    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tm_event_setting With {.StartDate = Now, .EndDate = Now, .Area_Id = _user.Area_id, .Cabang_id = _user.Cabang_id}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of EventSettingService)()
        Dim r = Serv.GetEventSettingByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try

            Dim Serv = ObjectFactory.GetInstance(Of EventSettingService)()
            Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
            If r.Success Then
                Reset()
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving()
        Try

            Dim Serv = ObjectFactory.GetInstance(Of EventSettingService)()
            Dim r = Serv.UpsertAsync(SelectedItem).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(TEntity As tm_event_setting)
        Try
            Dim Serv = ObjectFactory.GetInstance(Of EventSettingService)()

            If Action = Action.UnPosting Then

                TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName

                Dim rPos = Serv.Unposting(TEntity).GetAwaiter.GetResult
                If rPos.Success Then
                    Saved = True
                Else
                    sMsg = MessageFormatter.GetFormattedErrorMessage(rPos.Message)
                    Saved = False
                End If

                Return
            End If

            If Action = Action.Posting Then

                TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName

                Dim rPos = Serv.Posting(TEntity).GetAwaiter.GetResult
                If rPos.Success Then
                    Saved = True
                Else
                    sMsg = MessageFormatter.GetFormattedErrorMessage(rPos.Message)
                    Saved = False
                End If

                Return
            End If


            If TEntity.Id <= 0 Then
                TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.CreatedDate = Now
            Else
                TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.ModifiedDate = Now
            End If


            Dim r = Serv.UpsertAsync(TEntity).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Function DataList(bPosted As Boolean) As IQueryable(Of tm_event_setting) Implements IEventSettingListController.DataList

        Dim serv = ObjectFactory.GetInstance(Of EventSettingService)
        Dim r = serv.GetEventSettingsAsync.GetAwaiter.GetResult

        If r.Success Then
            Dim os As IQueryable(Of tm_event_setting) = r.Output
            os = os.Where(Function(f) f.Posted = bPosted)
            Return os.AsQueryable
        End If

        Return Nothing
    End Function
End Class
Public Interface IEventSettingListController
    Inherits IControllerMain, IControllerList

    Function DataList(bPosted As Boolean) As IQueryable(Of tm_event_setting)
End Interface
Public Interface IEventSettingEditController
    Inherits IControllerMain, IControllerEdit(Of tm_event_setting)



End Interface