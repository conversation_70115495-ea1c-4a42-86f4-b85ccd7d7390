﻿Imports System.Data.Entity
Imports System.Runtime.Remoting.Contexts
Imports HRD.Application
Imports HRD.Controller
Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Imports Z.EntityFramework.Plus
Public Class UserController
    Inherits Controller(Of tm_user)
    Implements IUserListController, IUserEditController

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New UserManager(daoFactory)


    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_user With {.isApproved = True}

    End Sub

    Public Overrides Sub SelectItem(id As String)
        mgr.DataAccess.ResetContext()
        SelectedItem = mgr.DataAccess.GetContext.tm_user.Include("tm_user_role").Where(Function(f) f.Id = id).FirstOrDefault

    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim o = mgr.DataAccess.GetById(id)
            mgr.GetUserRoleDao.DeleteRange(o.tm_user_role.ToList)
            mgr.GetUserAreaDao.DeleteRange(o.tm_user_area.ToList)
            mgr.DataAccess.Delete(o)
            mgr.DataAccess.Save()
            Reset()
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving()
        mgr.DataAccess.ResetContext()
        If SelectedItem.tm_user_area.Count <= 0 Then
            sMsg = MessageFormatter.GetFormattedErrorMessage("Minimal 1 Perusahaan di pilih")
            Saved = False
            Return
        End If
        Try
            'ExpressMapper.Mapper.Register(Of tm_user, tm_user) _
            '    .Ignore(Function(f) f.tm_area) _
            '    .Ignore(Function(f) f.tm_cabang) _
            '    .Ignore(Function(f) f.tm_user_role) _
            '    .Ignore(Function(f) f.tm_user_area)
            'Dim o = ExpressMapper.Mapper.Map(Of tm_user, tm_user)(SelectedItem)

            Dim o = mgr.DataAccess.FindBy(Function(f) f.Id = SelectedItem.Id).FirstOrDefault
            If o Is Nothing Then
                o = New tm_user With {.Area_id = SelectedItem.Area_id, .Cabang_id = SelectedItem.Cabang_id, .CreatedBy = SelectedItem.CreatedBy, .CreatedDate = SelectedItem.CreatedDate _
                , .Email = SelectedItem.Email, .Id = SelectedItem.Id, .isApproved = SelectedItem.isApproved, .ModifiedBy = SelectedItem.ModifiedBy, .ModifiedDate = SelectedItem.ModifiedDate _
                , .isLockedOut = SelectedItem.isLockedOut, .Password = SelectedItem.Password, .Username = SelectedItem.Username, .PermisionArea = SelectedItem.PermisionArea}

            End If


            If Action = Action.AddNew Or Action = Action.ChangePassword Then
                o.Password = mgr.EncryptPassword(SelectedItem.Password)
            End If
            If o.Id <= 0 Then
                o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.CreatedDate = Now
                mgr.DataAccess.Add(o)
            Else
                o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.ModifiedDate = Now
                mgr.DataAccess.Edit(o)
                'Dim entry = mgr.DataAccess.GetContext.Entry(o)
                'If entry.State = EntityState.Detached Then
                '    mgr.DataAccess.GetContext.Set(Of tm_user).Attach(o)
                'End If
                'o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                'o.ModifiedDate = Now
                'entry.State = EntityState.Modified
            End If

            For Each x In SelectedItem.tm_user_role

                ExpressMapper.Mapper.Register(Of tm_user_role, tm_user_role) _
                    .Ignore(Function(f) f.tm_role) _
                    .Ignore(Function(f) f.tm_user)

                Dim y = ExpressMapper.Mapper.Map(Of tm_user_role, tm_user_role)(x)

                If y.Deleting Then
                    If y.Id > 0 Then
                        mgr.GetUserRoleDao.Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tm_user = o
                        mgr.GetUserRoleDao.Add(y)
                    Else
                        mgr.GetUserRoleDao.Edit(y)
                    End If
                End If
            Next
            ' Get all existing user areas for this user
            Dim existingUserAreas = mgr.GetUserAreaDao.FindBy(Function(ua) ua.User_id = o.Id).ToList()

            ' Process selected user areas
            For Each x In SelectedItem.tm_user_area
                ExpressMapper.Mapper.Register(Of tm_user_area, tm_user_area) _
                    .Ignore(Function(f) f.tm_area) _
                    .Ignore(Function(f) f.tm_user)

                Dim y = ExpressMapper.Mapper.Map(Of tm_user_area, tm_user_area)(x)

                ' Check if record exists in tm_user_area table
                Dim existingUserArea = existingUserAreas.FirstOrDefault(Function(ua) ua.Area_id = y.Area_id)

                If existingUserArea IsNot Nothing Then
                    ' Update existing record
                    existingUserArea.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    existingUserArea.ModifiedDate = Now
                    mgr.GetUserAreaDao.Edit(existingUserArea)
                Else
                    ' Add new record
                    y.tm_user = o
                    y.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    y.CreatedDate = Now
                    mgr.GetUserAreaDao.Add(y)
                End If
            Next

            ' Delete user areas that are no longer selected
            For Each existingArea In existingUserAreas
                If Not SelectedItem.tm_user_area.Any(Function(x) x.Area_id = existingArea.Area_id) Then
                    mgr.GetUserAreaDao.Delete(existingArea)
                End If
            Next


            mgr.DataAccess.Save()
            Saved = True
            QueryCacheManager.ExpireTag(o.Username)
        Catch ex As Exception
            Saved = False
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(TEntity As tm_user)
        Throw New NotImplementedException()
    End Sub

    Public Function GetAreaList() As IList(Of tm_area) Implements IUserEditController.GetAreaList
        Dim qhandler = ObjectFactory.GetInstance(Of GetAreaHandler)

        Dim result = qhandler.Handle(New GetAreaListQuery, Nothing).GetAwaiter.GetResult


        Return result.ToList
    End Function
End Class
Public Interface IUserListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface IUserEditController
    Inherits IControllerMain, IControllerEdit(Of tm_user)

    Function GetAreaList() As IList(Of Domain.tm_area)
End Interface