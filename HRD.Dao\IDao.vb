﻿Imports System.Linq.Expressions
Imports HRD.Domain
Public Interface IDao(Of T)
    Function GetAll() As IQueryable(Of T)
    Function FindBy(ByVal predicate As Expression(Of Func(Of T, Boolean))) As IQueryable(Of T)
    Sub Add(ByVal entity As T)
    Sub AddRange(ByVal entities As List(Of T))
    Sub Delete(ByVal entity As T)
    Sub DeleteRange(ByVal entities As List(Of T))
    Sub Edit(ByVal entity As T)
    Sub Save()
    Sub ResetContext()
    ReadOnly Property GetContext As HRDEntities
End Interface

