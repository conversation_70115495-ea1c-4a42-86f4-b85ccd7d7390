Public Class CreateAreaHandler
    Implements IRequestHandler(Of CreateAreaCommand, Boolean)

    Private ReadOnly _context As IDbContext

    Public Sub New(context As IDbContext)
        _context = context
    End Sub

    Public Async Function Handle(request As CreateAreaCommand, cancellationToken As CancellationToken) As Task(Of Boolean) Implements IRequestHandler(Of CreateAreaCommand, Boolean).Handle
        Dim area As New tm_area With {
            .Kd_area = request.Kd_area,
            .Nama_area = request.Nama_area,
            .Is_aktif = request.Is_aktif
        }

        _context.tm_area.Add(area)
        Return Await _context.SaveChangesAsync() > 0
    End Function
End Class
