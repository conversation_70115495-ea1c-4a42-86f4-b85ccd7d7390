﻿Imports HRD.Dao
Imports HRD.Domain
Imports Z.EntityFramework.Plus

Public Class TerPajakDao
    Inherits DaoBase(Of tm_ter_pajak)
    Implements ITerPajakDao

    Public ReadOnly Property TER_PAJAK(kode_ter As String, PendBrutoBulanan As Decimal) As (ter_pajak_id As String, tarifPercent As Decimal, nilaiPajak As Decimal) Implements ITerPajakDao.GetTER_PAJAK
        Get
            If PendBrutoBulanan < 0 Then
                Return (Nothing, 0, 0)
            End If
            Dim o = FindBy(Function(f) f.Kode = kode_ter And PendBrutoBulanan >= f.<PERSON> And PendBrutoBulanan <= f.<PERSON><PERSON>i).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
            If o Is Nothing Then
                Return (Nothing, 34, (34) * PendBrutoBulanan / 100)
            End If
            Return (o.Id, o.<PERSON>ri<PERSON>Percent, o.TarifPercent * PendBrutoBulanan / 100)
        End Get
    End Property


End Class
