﻿Imports System.IO
Imports HRD.Application

Public Class ErrorMessageLog
    Implements IErrorMessageLog

    Private ReadOnly _logsPath As String

    Public Sub New()
        Dim rootPath As String = AppDomain.CurrentDomain.BaseDirectory
        _logsPath = Path.Combine(rootPath, "Logs")
    End Sub

    Public Function LogError(ByVal layerName As String, ByVal className As String, ByVal methodName As String, ByVal msg As String) As Boolean Implements IErrorMessageLog.LogError
        Try
            If Not Directory.Exists(_logsPath) Then
                Directory.CreateDirectory(_logsPath)
            End If

            Dim dtNow = DateTime.Now.ToString("yyyy-MM-dd")

            Dim errLogs As String = Path.Combine(_logsPath, dtNow & "_ErrorLogs.txt")

            SyncLock Me
                Using sw = File.AppendText(errLogs)

                    sw.WriteLine("Layer Name :- " & layerName)
                    sw.WriteLine("Class Name :- " & className)
                    sw.WriteLine("Method Name :- " & methodName)
                    sw.WriteLine("Date Time :- " & DateTime.Now)
                    sw.WriteLine("Error Message :- " & msg)
                    sw.WriteLine(New String("-"c, 50))
                    sw.WriteLine(sw.NewLine)
                End Using
            End SyncLock
        Catch e1 As Exception
            Return False
        End Try
        Return True
    End Function
End Class
