﻿Imports HRD.Dao
Imports HRD.Domain
Public Class UserDao
    Inherits DaoBase(Of tm_user)
    Implements IUserDao

    Public Function GetById(id As String) As tm_user Implements IUserDao.GetById
        Return FindBy(Function(f) f.Id = id).FirstOrDefault
    End Function

    Public Function ValidateUser(userName As String, passWord As String) As tm_user Implements IUserDao.ValidateUser
        Dim q = FindBy(Function(f) f.Username = userName And f.Password = passWord And f.isApproved = True And f.isLockedOut <> True).FirstOrDefault
        Return q
    End Function

    Public Function ChangePassword(currentPassword As String, oldPassword As String, userName As String) As Boolean Implements IUserDao.ChangePassword
        Dim q = FindBy(Function(f) f.Username = userName And f.Password = oldPassword).FirstOrDefault
        If q IsNot Nothing Then
            q.Password = currentPassword
            Edit(q)
            Save()
            Return True
        End If
        Return False
    End Function
End Class
