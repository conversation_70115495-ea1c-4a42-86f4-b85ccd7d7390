'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Collections.Generic

Partial Public Class tr_absensi
    Public Property Id As Long
    Public Property Area_id As Integer
    Public Property Cabang_id As Integer
    Public Property karyawan_id As Integer
    Public Property Tanggal As Date
    Public Property MsJamMasuk As Nullable(Of Date)
    Public Property JamMasuk As Nullable(Of Date)
    Public Property MsJamKeluar As Nullable(Of Date)
    Public Property JamKeluar As Nullable(Of Date)
    Public Property Terlambat As Integer
    Public Property Overtime As Integer
    Public Property OverTime_HLibur As Integer
    Public Property Keterangan As String
    Public Property Alpha_b As Boolean
    Public Property Cuti_b As Boolean
    Public Property Ijin_b As Boolean
    Public Property PulangCepat As Integer
    Public Property Lembur_HKerja_1 As Integer
    Public Property Lembur_HKerja_2 As Integer
    Public Property Lembur_HKerja_3 As Integer
    Public Property Lembur_HLibur_1_7 As Integer
    Public Property Lembur_HLibur_8 As Integer
    Public Property Lembur_HLibur_9_10 As Integer
    Public Property Masuk_b As Boolean
    Public Property CreatedBy As String
    Public Property CreatedDate As Nullable(Of Date)
    Public Property ModifiedBy As String
    Public Property ModifiedDate As Nullable(Of Date)
    Public Property Ijin_id As Nullable(Of Integer)
    Public Property Off_b As Boolean
    Public Property PotongGaji As Boolean
    Public Property LemburLine_id As Nullable(Of Integer)
    Public Property FromAbsensi As Boolean
    Public Property LiburNasional As Boolean
    Public Property CutiBersama As Boolean
    Public Property LiburNasional_id As Nullable(Of Integer)
    Public Property GantiHari As Boolean

    Public Overridable Property tm_libur_nasional As tm_libur_nasional
    Public Overridable Property tr_ijin As tr_ijin
    Public Overridable Property tm_cabang As tm_cabang
    Public Overridable Property tr_spk_lembur_line As tr_spk_lembur_line
    Public Overridable Property tm_karyawan As tm_karyawan
    Public Overridable Property tm_area As tm_area

End Class
