﻿Imports System.Linq.Expressions
Imports HRD.Domain
Imports HRD.Helpers
Public Class IjinPermisionSpec
    Inherits CompositeSpecification(Of tr_ijin)

    Private ReadOnly _loggedInUser As ApplicationUser
    Private ReadOnly _posted As Boolean

    Public Sub New(bPosted As Boolean)
        _loggedInUser = AuthHelper.GetLoggedInUserInfo
        _posted = bPosted
    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of tr_ijin, Boolean))
        Get
            Select Case _loggedInUser.PermisionArea
                Case 0
                    Return Function(f) f.Cabang_id = _loggedInUser.Cabang_id And f.Posted = _posted
                    'Case 1
                    '    Return Function(f) f.Area_id = _loggedInUser.Area_id And f.Posted = _posted
                Case Else
                    Return Function(f) f.Posted = _posted And _loggedInUser.AreaIdPermisionList.Contains(f.Area_id)
            End Select
        End Get
    End Property

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of tr_ijin)) As IQueryable(Of tr_ijin)
        If Criteria Is Nothing Then
            Return query
        End If
        Return query.Where(Criteria)
    End Function
End Class
