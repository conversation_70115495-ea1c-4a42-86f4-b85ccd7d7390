﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain

Public Class rpt_pajaktahunan
    Public Periode As Date
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Static i As Integer

        i += 1

        txt_no.Text = $"{i}."
    End Sub

    Private Sub fStatusPajak_GetValue(sender As Object, e As GetValueEventArgs) Handles fStatusPajak.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If



        ' Default to the employee's status
        e.Value = o.tm_status_PTKP.KodeStatus

        ' Find the first posted salary line for the given period
        Dim gjLine = o.tr_gaji_line.FirstOrDefault(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year)

        ' If a salary line is found and it has a StatusPTKP_id, use its status
        If gjLine?.StatusPTKP_id.HasValue Then
            e.Value = gjLine.tm_status_PTKP.KodeStatus
        End If
    End Sub

    Private Sub fMasaPajakAwal_GetValue(sender As Object, e As GetValueEventArgs) Handles fMasaPajakAwal.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        ' Ambil bulan dari gaji pertama yang diposting dalam tahun periode
        e.Value = o.tr_gaji_line.
                    Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                    OrderBy(Function(f) f.tr_gaji.Tanggal).
                    Select(Function(f) f.tr_gaji.Tanggal.Month).
                    FirstOrDefault()

    End Sub

    Private Sub fMasaPajakAkhir_GetValue(sender As Object, e As GetValueEventArgs) Handles fMasaPajakAkhir.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        ' Ambil bulan dari gaji terakhir yang diposting dalam tahun periode
        e.Value = o.tr_gaji_line.
                    Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                    OrderByDescending(Function(f) f.tr_gaji.Tanggal).
                    Select(Function(f) f.tr_gaji.Tanggal.Month).
                    FirstOrDefault()
    End Sub

    Private Sub fTahunPajak_GetValue(sender As Object, e As GetValueEventArgs) Handles fTahunPajak.GetValue
        e.Value = Periode.Year
    End Sub

    Private Sub fJabatan_GetValue(sender As Object, e As GetValueEventArgs) Handles fJabatan.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        ' Ambil jabatan terbaru dari tm_karyawan_datadinas
        Dim latestDinas = o.tm_karyawan_datadinas?.OrderByDescending(Function(od) od.Id).FirstOrDefault
        e.Value = latestDinas?.tm_jabatan?.Jabatan

        ' Ambil gaji terakhir yang sudah diposting untuk tahun periode
        Dim gjLine = o.tr_gaji_line.
                        Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                        OrderByDescending(Function(od) od.tr_gaji.Tanggal).
                        FirstOrDefault

        ' Jika ada data gaji, gunakan jabatan dari tm_karyawan_datadinas di dalamnya
        If gjLine?.tm_karyawan_datadinas IsNot Nothing Then
            e.Value = gjLine.tm_karyawan_datadinas.tm_jabatan?.Jabatan
        End If

    End Sub

    Private Sub fJmlBulanBekerja_GetValue(sender As Object, e As GetValueEventArgs) Handles fJmlBulanBekerja.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return



        e.Value = o.tr_gaji_line.
                        Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                        Count


    End Sub

    Private Sub fGajiPokok_GetValue(sender As Object, e As GetValueEventArgs) Handles fGajiPokok.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        'e.Value = o.tr_gaji_line.
        '          Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
        '          OrderByDescending(Function(od) od.tr_gaji.Tanggal).
        '          Select(Function(s) s.Pd_GajiPokok).
        '          FirstOrDefault()


        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                  Sum(Function(s) CType(s.Pd_GajiPokok, Decimal?)), 0)



    End Sub

    Private Sub fTunjanganLain_GetValue(sender As Object, e As GetValueEventArgs) Handles fTunjanganLain.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                  Sum(Function(s) CType(s.Pd_Lembur + s.Pd_T_Lain, Decimal?)), 0)


    End Sub

    Private Sub fInsentif_GetValue(sender As Object, e As GetValueEventArgs) Handles fInsentif.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                  Sum(Function(s) CType(s.Pd_Insentif, Decimal?)), 0)

    End Sub

    Private Sub fBPJS_GetValue(sender As Object, e As GetValueEventArgs) Handles fBPJS.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                  Sum(Function(s) CType(s.Pd_T_JKK + s.Pd_T_JKM + s.Pd_T_JPK, Decimal?)), 0)
    End Sub

    Private Sub fTHR_GetValue(sender As Object, e As GetValueEventArgs) Handles fTHR.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_thr_line.
                  Where(Function(f) f.tr_thr.Posted AndAlso f.tr_thr.PeriodeDate.Year = Periode.Year).
                  Sum(Function(s) CType(s.Total, Decimal?)), 0)
    End Sub

    Private Sub fJHT_JP_GetValue(sender As Object, e As GetValueEventArgs) Handles fJHT_JP.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                  Sum(Function(s) CType(s.Pd_T_JHT + s.Pd_T_JP, Decimal?)), 0)
    End Sub

    Private Sub fPPh_Jan_Nov_GetValue(sender As Object, e As GetValueEventArgs) Handles fPPh_Jan_Nov.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year AndAlso f.tr_gaji.Tanggal.Month <= 11).
                  Sum(Function(s) CType(s.Pt_PPH21, Decimal?)), 0)
    End Sub

    Private Sub fPPh21_Des_GetValue(sender As Object, e As GetValueEventArgs) Handles fPPh21_Des.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        e.Value = If(o.tr_gaji_line.
                  Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year AndAlso f.tr_gaji.Tanggal.Month = 12).
                  Sum(Function(s) CType(s.Pt_PPH21, Decimal?)), 0)
    End Sub

    Private Sub fTglPemotongan_GetValue(sender As Object, e As GetValueEventArgs) Handles fTglPemotongan.GetValue
        Dim o = TryCast(e.Row, tm_karyawan)
        If o Is Nothing Then Return

        Dim tgl = o.tr_gaji_line.
                    Where(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).
                    OrderByDescending(Function(f) f.tr_gaji.Tanggal).
                    Select(Function(f) f.tr_gaji.Tanggal).
                    FirstOrDefault()

        If tgl = Nothing Then
            Return
        End If
        ' Hitung tanggal terakhir bulan berdasarkan periode
        Dim lastDayOfMonth As Integer = DateTime.DaysInMonth(tgl.Year, tgl.Month)

        ' Format tanggal menjadi "yyyy-MM-dd" dengan hari terakhir bulan
        e.Value = New DateTime(tgl.Year, tgl.Month, lastDayOfMonth)
    End Sub
End Class