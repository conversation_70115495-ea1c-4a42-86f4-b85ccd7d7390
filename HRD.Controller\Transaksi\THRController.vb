﻿Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports StructureMap
Imports HRD.Controller

Public Class THRController
    Inherits Controller(Of tr_thr)
    Implements ITHRListController, ITHREditController

    Public ReadOnly Property DataList(area_id As String, bPosted As Boolean) As IQueryable(Of tr_thr) Implements ITHRListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of ThrService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_thr) = r.Output
                'Dim _user = AuthHelper.GetLoggedInUserInfo
                If area_id = "ALL" Then
                    os = os.Where(Function(f) f.Posted = bPosted)
                Else
                    os = os.Where(Function(f) f.Posted = bPosted And f.Area_id = area_id)
                End If

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property DataListLine(thr_id As String) As IQueryable(Of tr_thr_line) Implements ITHREditController.DataListLine
        Get
            Dim Serv = ObjectFactory.GetInstance(Of ThrLineService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_thr_line) = r.Output
                'Dim _user = AuthHelper.GetLoggedInUserInfo
                os = os.Where(Function(f) f.Thr_id = thr_id)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tr_thr With {.PeriodeDate = Now, .GajiPokok_b = True}

    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of ThrService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of ThrService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_thr)


        If Action = Action.AddNew Then
            TEntity.Periode = Format(TEntity.PeriodeDate, "yyyy")

        End If

        Dim o As New tr_thr With {.Area_id = TEntity.Area_id, .Cabang_id = TEntity.Cabang_id, .CreatedBy = TEntity.CreatedBy, .CreatedDate = TEntity.CreatedDate _
            , .Id = TEntity.Id, .isCompleted = TEntity.isCompleted, .Keterangan = TEntity.Keterangan, .KodeTHR = TEntity.KodeTHR, .ModifiedBy = TEntity.ModifiedBy _
            , .ModifiedDate = TEntity.ModifiedDate, .Periode = TEntity.Periode, .PeriodeDate = TEntity.PeriodeDate, .Posted = TEntity.Posted, .PostedBy = TEntity.PostedBy _
            , .PostedDate = TEntity.PostedDate, .Total = TEntity.Total, .GajiPokok_b = SelectedItem.GajiPokok_b, .TipeTHR = SelectedItem.TipeTHR _
            , .T_Jabatan_b = SelectedItem.T_Jabatan_b, .T_Kontrak_b = SelectedItem.T_Kontrak_b, .T_Makan_b = SelectedItem.T_Makan_b, .T_Susu_b = SelectedItem.T_Susu_b _
            , .T_Transport_b = SelectedItem.T_Transport_b, .T_Probation_b = SelectedItem.T_Probation_b}

        Dim Serv = ObjectFactory.GetInstance(Of ThrService)()

        If Action = Action.Posting Then
            o.Posted = True
            o.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.PostedDate = Now

            Dim rPos = Serv.Posting(o, TEntity.tr_thr_line.ToList).GetAwaiter.GetResult
            If rPos.Success Then
                Saved = True
                Return
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(rPos.Message)
                Saved = False
                Return
            End If
        End If



        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface ITHRListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(area_id As String, bPosted As Boolean) As IQueryable(Of tr_thr)
End Interface
Public Interface ITHREditController
    Inherits IControllerMain, IControllerEdit(Of tr_thr)

    ReadOnly Property DataListLine(thr_id As String) As IQueryable(Of tr_thr_line)
End Interface