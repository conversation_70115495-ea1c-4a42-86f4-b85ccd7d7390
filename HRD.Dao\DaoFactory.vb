﻿Imports HRD.Dao

Public Class DaoFactory
    Implements IDaoFactory

    Public Function GetMenuDao() As IMenuDao Implements IDaoFactory.GetMenuDao
        Return New MenuDao
    End Function

    Public Function GetUserDao() As IUserDao Implements IDaoFactory.GetUserDao
        Return New UserDao
    End Function

    Public Function GetRoleDao() As IRoleDao Implements IDaoFactory.GetRoleDao
        Return New RoleDao
    End Function

    Public Function GetAreaDao() As IAreaDao Implements IDaoFactory.GetAreaDao
        Return New AreaDao
    End Function

    Public Function GetCabangDao() As ICabangDao Implements IDaoFactory.GetCabangDao
        Return New Cabangdao
    End Function

    Public Function GetMenuRoleDao() As IMenuRoleDao Implements IDaoFactory.GetMenuRoleDao
        Return New MenuRoleDao
    End Function

    Public Function GetUserRoleDao() As IUserRoleDao Implements IDaoFactory.GetUserRoleDao
        Return New UserRoleDao
    End Function

    Public Function GetPendidikanDao() As IPendidikanDao Implements IDaoFactory.GetPendidikanDao
        Return New PendidikanDao
    End Function

    Public Function GetJurusanDao() As IJurusanDao Implements IDaoFactory.GetJurusanDao
        Return New JurusanDao
    End Function

    Public Function GetJenisKelaminDao() As IJenisKelaminDao Implements IDaoFactory.GetJenisKelaminDao
        Return New JenisKelaminDao
    End Function

    Public Function GetAgamaDao() As IAgamaDao Implements IDaoFactory.GetAgamaDao
        Return New AgamaDao
    End Function

    Public Function GetStatusPTKPDao() As IStatusPTKPDao Implements IDaoFactory.GetStatusPTKPDao
        Return New StatusPTKPDao
    End Function

    Public Function GetNamaBankDao() As INamaBankDao Implements IDaoFactory.GetNamaBankDao
        Return New NamaBankDao
    End Function

    Public Function GetKaryawanDao() As IKaryawanDao Implements IDaoFactory.GetKaryawanDao
        Return New KaryawanDao
    End Function

    Public Function GetDepartmentDao() As IDepartmentDao Implements IDaoFactory.GetDepartmentDao
        Return New DepartmentDao
    End Function

    Public Function GetJabatanDao() As IJabatanDao Implements IDaoFactory.GetJabatanDao
        Return New JabatanDao
    End Function

    Public Function GetSettingAbsenDao() As ISettingAbsenDao Implements IDaoFactory.GetSettingAbsenDao
        Return New SettingAbsenDao
    End Function

    Public Function GetIjinMasterDao() As IIjinMasterDao Implements IDaoFactory.GetIjinMasterDao
        Return New IjinMasterDao
    End Function

    Public Function GetStatusPerkawinanDao() As IStatusPerkawinanDao Implements IDaoFactory.GetStatusPerkawinanDao
        Return New StatusPerkawinanDao
    End Function

    Public Function GetDataDinasDao() As IDataDinasDao Implements IDaoFactory.GetDataDinasDao
        Return New DataDinasDao
    End Function

    Public Function GetRegisterPinjamanDao() As IRegisterPinjamanDao Implements IDaoFactory.GetRegisterPinjamanDao
        Return New RegisterPinjamanDao
    End Function

    Public Function GetAbsensiDao() As IAbsensiDao Implements IDaoFactory.GetAbsensiDao
        Return New AbsensiDao
    End Function

    Public Function GetLiburNasionalDao() As ILiburNasionalDao Implements IDaoFactory.GetLiburNasionalDao
        Return New LiburNasionalDao
    End Function

    Public Function GetSPKLemburDao() As ISPKLemburDao Implements IDaoFactory.GetSPKLemburDao
        Return New SPKLemburDao
    End Function

    Public Function GetSPKLemburLineDao() As ISPKLemburLineDao Implements IDaoFactory.GetSPKLemburLineDao
        Return New SPKLemburLineDao
    End Function

    Public Function GetShiftAbsensiDao() As IShiftAbsensiDao Implements IDaoFactory.GetShiftAbsensiDao
        Return New ShiftAbsensiDao
    End Function

    Public Function GetOffKaryawanLineDao() As IOffKaryawanLineDao Implements IDaoFactory.GetOffKaryawanLineDao
        Return New OffKaryawanLineDao
    End Function

    Public Function GetTerPajakDao() As ITerPajakDao Implements IDaoFactory.GetTerPajakDao
        Return New TerPajakDao
    End Function

    Public Function GetGajiLineDao() As IGajiLineDao Implements IDaoFactory.GetGajiLineDao
        Return New GajiLineDao
    End Function

    Public Function GetUserAreaDao() As IUserAreaDao Implements IDaoFactory.GetUserAreaDao
        Return New UserAreaDao
    End Function
End Class
