﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web
Public Class ucMasterBonus_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IBonusTHREditController

    Private Const PageIdSessionKey As String = "_PageID"
    Private Const ActionSessionKeySuffix As String = "_Action"

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)

        Visible = Controller.SelectedItem IsNot Nothing

        If Not Visible Then Return

        oSelectItem = Controller.SelectedItem
        oAction = Controller.Action

        ASPxFormLayout1.DataSource = Controller.SelectedItem
        ASPxFormLayout1.DataBind()

        ConfigureFormBasedOnAction()



    End Sub

    Private Sub ConfigureFormBasedOnAction()
        If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then
            ' Configuration for AddNew or Edit
        Else
            MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
        End If

        ConfigureButtonsBasedOnAction()
    End Sub
    Private Sub ConfigureButtonsBasedOnAction()
        If Controller.Action = Action.Posting Then
            btn_save.Text = "Release"
        Else
            btn_save.Visible = Controller.Action <> Action.View
            SetClientSideEvents("save", "back")
        End If
    End Sub
    Private Sub SetClientSideEvents(saveCallback As String, backCallback As String)
        btn_save.ClientSideEvents.Click = $"function(s, e) {{if (ASPxClientEdit.ValidateGroup(null) == true) {{cp_bonusthr.PerformCallback('{saveCallback}');s.SetEnabled(false);}}}}"
        btn_back.ClientSideEvents.Click = $"function(s, e) {{cp_bonusthr.PerformCallback('{backCallback}');}}"
    End Sub
    Private Function GetSessionKey(baseKey As String) As String
        Dim value = ViewState(baseKey)
        If value IsNot Nothing Then
            Return value.ToString() & baseKey
        Else
            ' Handle the null case or initialize with a default value
            ' For example, return a default string or initialize ViewState(baseKey)
            Return "DefaultValue" & baseKey
        End If
    End Function

    Sub Saving()
        Controller.Action = oAction


        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tm_bonus_thr
        Get
            Return CType(Session(GetSessionKey(PageIdSessionKey)), tm_bonus_thr)
        End Get
        Set(value As tm_bonus_thr)
            Session(GetSessionKey(PageIdSessionKey)) = value
        End Set
    End Property

    Private Property oAction As Action
        Get
            Return Session(GetSessionKey(PageIdSessionKey) & ActionSessionKeySuffix)
        End Get
        Set(value As Action)
            Session(GetSessionKey(PageIdSessionKey) & ActionSessionKeySuffix) = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Not IsPostBack Then
            ViewState(PageIdSessionKey) = (New Random()).Next().ToString()
        End If
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub
End Class