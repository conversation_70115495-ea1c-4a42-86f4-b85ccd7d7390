Imports HRD.Application
Imports HRD.Domain
Imports HRD.Helpers
Public Class MyFunctionInfra
    Implements IMyFunction

    Public Function UpdateAbsensiDataLembur(absensi As tr_absensi, oKar As tm_karyawan) As Boolean Implements IMyFunction.UpdateAbsensiDataLembur
        Return myFunction.UpdateAbsensiDataLembur(absensi, oKar)
    End Function

    Public Function GetPPH21(totalPendapatan As Decimal, id_kar As String, zakat As Decimal) As Decimal Implements IMyFunction.GetPPH21
        Return myFunction.GetPPH21(totalPendapatan, id_kar, zakat)
    End Function

    Public Function tm_area(id As String) As Domain.tm_area Implements IMyFunction.tm_area
        Return myFunction.tm_area(id)
    End Function

    Public Function GetKodeNIK(o As tm_karyawan) As String Implements IMyFunction.GetKodeNIK
        Return myFunction.GetKodeNIK(o)
    End Function

    Public Function GetPotonganPinjaman(id_kar As String, tgl As Date) As Decimal Implements IMyFunction.GetPotonganPinjaman
        Return myFunction.GetPotonganPinjaman(id_kar, tgl)
    End Function

    Public Function CalculateWeekdays(cabang_id As String, startDate As Date, endDate As Date, ByRef allDay As Integer) As Integer Implements IMyFunction.CalculateWeekdays
        Return myFunction.CalculateWeekdays(cabang_id, startDate, endDate, allDay)
    End Function

    Public Function GetAbsensis(karyawan_id As String, startDate As Date, endDate As Date) As IQueryable(Of tr_absensi) Implements IMyFunction.GetAbsensis
        Return myFunction.GetAbsensis(karyawan_id, startDate, endDate)
    End Function

    Public Function CekHariLibur(area_id As String, kar As tm_karyawan, tgl As Date, ByRef ket As String, Optional ByRef cutiBersama As Boolean = False) As Boolean Implements IMyFunction.CekHariLibur
        Return myFunction.CekHariLibur(area_id, kar, tgl, ket, cutiBersama)
    End Function
    Public Function CekSPKLembur(tgl As Date, kar_id As String, ByRef ket As String, ByRef jamMasuk As Date, ByRef jamKeluar As DateTime) As tr_spk_lembur_line Implements IMyFunction.CekSPKLembur
        Dim o = myFunction.CekSPKLembur(tgl, kar_id, ket, jamMasuk, jamKeluar)
        Return o
    End Function

    Public Function tm_absensi_setting(cabang_id As String) As tm_absensi_setting Implements IMyFunction.tm_absensi_setting
        Return myFunction.tm_absensi_setting(cabang_id)
    End Function

    Public Function tm_absensi_setting(cabang_id As String, shift As String) As tm_absensi_setting Implements IMyFunction.tm_absensi_setting
        Return myFunction.tm_absensi_setting(cabang_id, shift)
    End Function

    Public Function tm_absensi_setting_by_date(cabang_id As String, tanggal As Date, Optional shift As Integer = 0) As tm_absensi_setting Implements IMyFunction.tm_absensi_setting_by_date
        Return myFunction.tm_absensi_setting_by_date(cabang_id, tanggal, shift)
    End Function

    Public Function GetSettingAbsensi_OFF(kar_id As String, tgl As Date) As (bOff As Boolean, dMSmasuk As DateTime, dMSkeluar As DateTime, sKeterangan As String) Implements IMyFunction.GetSettingAbsensi_OFF
        Return myFunction.GetSettingAbsensi_OFF(kar_id, tgl)
    End Function

    Public Function CekHariLiburOFF(kar_id As String, tgl As Date, ByRef ket As String) As Boolean Implements IMyFunction.CekHariLiburOFF
        Return myFunction.CekHariLiburOFF(kar_id, tgl, ket)
    End Function

    Public Function tm_cabang(id As String) As tm_cabang Implements IMyFunction.tm_cabang
        Return myFunction.tm_cabang(id)
    End Function

    Public Function PPh21CalculatorBulanan(totalPendapatanBrutoBulanan As Decimal, kode_statusPTKP As String) As (ter_pajak_id As String, tarifPercent As Decimal, nilaiPajak As Decimal) Implements IMyFunction.PPh21CalculatorBulanan
        Return myFunction.PPh21CalculatorBulanan(totalPendapatanBrutoBulanan, kode_statusPTKP)
    End Function

    Public Function PPh21CalculatorTahunan(totalPendapatanBrutoTahunan As Decimal, zakat As Decimal, kode_statusPTKP As String) As Decimal Implements IMyFunction.PPh21CalculatorTahunan
        Return myFunction.PPh21CalculatorTahunan(totalPendapatanBrutoTahunan, zakat, kode_statusPTKP)
    End Function

    Public Function CalculateAssuransi(gajiPokok As Decimal, id_kar As String, cabang_id As String) As Object Implements IMyFunction.CalculateAssuransi
        Return myFunction.CalculateAssuransi(gajiPokok, id_kar, cabang_id)
    End Function

    Public Function GetJumlahHariLiburNasionals(cabang_id As String, startDate As Date, endDate As Date) As Integer Implements IMyFunction.GetJumlahHariLiburNasionals
        Return myFunction.GetJumlahHariLiburNasionals(cabang_id, startDate, endDate)
    End Function

    Public Function CekHariJumat(tgl As Date) As Boolean Implements IMyFunction.CekHariJumat
        Return myFunction.CekHariJumat(tgl)
    End Function

    Public Function tm_libur_nasional(tgl As Date) As tm_libur_nasional Implements IMyFunction.tm_libur_nasional
        Return myFunction.tm_libur_nasional(tgl)
    End Function

    Public Function GetTotalPendapatanTahunan(kar_id As String, tahun As Integer) As Decimal Implements IMyFunction.GetTotalPendapatanTahunan
        Return myFunction.GetTotalPendapatanTahunan(kar_id, tahun)
    End Function

    Public Function GetTotalPph21Tahunan(kar_id As String, tahun As Integer) As Decimal Implements IMyFunction.GetTotalPph21Tahunan
        Return myFunction.GetTotalPph21Tahunan(kar_id, tahun)
    End Function

    Public Function CekHariSabtu(tgl As Date) As Boolean Implements IMyFunction.CekHariSabtu
        Return myFunction.CekHariSabtu(tgl)
    End Function

    Public Function CekHariMinggu(tgl As Date) As Boolean Implements IMyFunction.CekHariMinggu
        Return myFunction.CekHariMinggu(tgl)
    End Function

    Public Function tm_karyawan(id As Integer) As tm_karyawan Implements IMyFunction.tm_karyawan
        Return myFunction.tm_karyawan(id)
    End Function
End Class
