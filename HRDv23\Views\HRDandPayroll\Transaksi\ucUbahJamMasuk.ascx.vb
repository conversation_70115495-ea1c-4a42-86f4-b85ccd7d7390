﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Public Class ucUbahJamMasuk
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IAbsensiListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()
        ASPxGridView1.Selection.UnselectAll()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = AuthHelper.GetLoggedInUserInfo.Cabang_id

            txt_endDate.Value = CDate(Format(Now, "yyyy-MM-20"))
            txt_startDate.Value = txt_endDate.Date.AddMonths(-1).AddDays(1)
            txt_jammasuk.Value = CDate(Format(Now, "yyyy-MM-dd 08:00:00"))
        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.QueryableSource = Controller.DataList(txt_startDate.Value, txt_endDate.Value, cb_area.Value, cb_cabang.Value).Where(Function(f) f.Masuk_b = True).AsQueryable
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Friend Function GetJamMasuk() As DateTime
        Return txt_jammasuk.Value
    End Function

    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim bCuti As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Cuti_b")
        Dim bIjin As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Ijin_b")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(bCuti = False And bIjin = False, DevExpress.Utils.DefaultBoolean.True, DevExpress.Utils.DefaultBoolean.False)
            Case "btn_view"
                e.Visible = If(bCuti Or bIjin, DevExpress.Utils.DefaultBoolean.True, DevExpress.Utils.DefaultBoolean.False)


        End Select
    End Sub
    Public ReadOnly Property GetSelectedItem() As List(Of Object)
        Get
            Return Me.ASPxGridView1.GetSelectedFieldValues("Id")
        End Get
    End Property

End Class