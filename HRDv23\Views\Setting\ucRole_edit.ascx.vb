﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.ASPxTreeList

Public Class ucRole_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IRoleEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            Session(ViewState("_PageID").ToString()) = Controller.SelectedItem
            Session(ViewState("_PageID").ToString() & "_Action") = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If

            ASPxTreeList1.UnselectAll()

            For Each x In Controller.SelectedItem.tm_menu_role

                ASPxTreeList1.FindNodeByFieldValue("Id", x.SiteMenu_id).Selected = True

            Next


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = Session(ViewState("_PageID").ToString() & "_Action")

        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_role)

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelect)

        ' Create a dictionary for faster lookups
        Dim menuRoleDict = oSelect.tm_menu_role.ToDictionary(Function(f) f.SiteMenu_id)

        For Each x In ASPxTreeList1.GetAllNodes
            Dim id = x.GetValue("Id")
            If x.Selected Then
                If Not menuRoleDict.ContainsKey(id) Then
                    Dim o = New tm_menu_role With {.SiteMenu_id = id}
                    oSelect.tm_menu_role.Add(o)
                    menuRoleDict.Add(id, o)
                End If
            Else
                If menuRoleDict.ContainsKey(id) Then
                    menuRoleDict(id).Deleting = True
                End If
            End If
        Next

        Controller.SelectedItem = oSelect
    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()
        Else
            Dim os = Controller.MenuList
            ASPxTreeList1.DataSource = os.ToList
            ASPxTreeList1.DataBind()
        End If
    End Sub


End Class