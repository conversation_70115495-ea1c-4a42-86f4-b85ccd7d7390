﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucAbsensi_list.ascx.vb" Inherits="HRDv23.ucAbsensi_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="Filter" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem Caption="Perusahaan" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" NullText="ALL">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_absensi.UnselectRows();
    cb_cabang_list.PerformCallback();
	grd_absensi.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ClearButton DisplayMode="Always">
                                </ClearButton>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Cabang" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang_list" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" NullText="ALL">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_absensi.UnselectRows();
    grd_absensi.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ClearButton DisplayMode="Always">
                                </ClearButton>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Start Date" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit ID="txt_startDate" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                <ClientSideEvents ValueChanged="function(s, e) {
    grd_absensi.UnselectRows();
    grd_absensi.Refresh();
}" />
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="End Date" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxDateEdit ID="txt_endDate" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                <ClientSideEvents ValueChanged="function(s, e) {
	grd_absensi.UnselectRows();
    grd_absensi.Refresh();
}" />
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Daftar Absensi" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_absensi">
                                <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_absensi.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_absensi.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_absensi.PerformCallback('new'); 
break;
case 'btn_delete_selected':
cp_absensi.PerformCallback(e.item.name); 

}

}" />
                                <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
                                </SettingsAdaptivity>
                                <SettingsPager AlwaysShowPager="True">
                                    <AllButton Visible="True">
                                    </AllButton>
                                    <PageSizeItemSettings Visible="True">
                                    </PageSizeItemSettings>
                                </SettingsPager>
                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" HorizontalScrollBarMode="Auto" />
                                <SettingsBehavior AllowFocusedRow="True" ProcessSelectionChangedOnServer="True" />
                                <SettingsPopup>
                                    <FilterControl AutoUpdatePosition="False">
                                    </FilterControl>
                                </SettingsPopup>
                                <SettingsSearchPanel Visible="True" />
                                <SettingsExport EnableClientSideExportAPI="True">
                                </SettingsExport>
                                <Columns>
                                    <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0" SelectAllCheckboxMode="AllPages" SelectCheckBoxPosition="Left" ShowSelectCheckbox="True">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                    <Image IconID="iconbuilder_actions_edit_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                    <Image IconID="scheduling_delete_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
                <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                    <Image IconID="iconbuilder_security_visibility_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
                                    <dx:GridViewDataTextColumn Caption="Perusahaan" FieldName="tm_area.KodeArea" ShowInCustomizationForm="True" VisibleIndex="1">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Cabang" FieldName="tm_cabang.NamaCabang" ShowInCustomizationForm="True" VisibleIndex="2">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="NIK" FieldName="tm_karyawan.NIK" ShowInCustomizationForm="True" VisibleIndex="3">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataDateColumn FieldName="Tanggal" ShowInCustomizationForm="True" VisibleIndex="5">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn FieldName="Terlambat" ShowInCustomizationForm="True" VisibleIndex="10">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Overtime" ShowInCustomizationForm="True" VisibleIndex="12">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Ovt. H. Libur" FieldName="OverTime_HLibur" ShowInCustomizationForm="True" VisibleIndex="13">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataCheckColumn Caption="Alpha" FieldName="Alpha_b" ShowInCustomizationForm="True" VisibleIndex="16">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataCheckColumn Caption="Cuti" FieldName="Cuti_b" ShowInCustomizationForm="True" VisibleIndex="18">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataCheckColumn Caption="Ijin" FieldName="Ijin_b" ShowInCustomizationForm="True" VisibleIndex="19">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataTextColumn Caption="P. Cepat" FieldName="PulangCepat" ShowInCustomizationForm="True" VisibleIndex="11">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTimeEditColumn Caption="J. Masuk" FieldName="MsJamMasuk" ShowInCustomizationForm="True" VisibleIndex="6">
                                        <PropertiesTimeEdit DisplayFormatInEditMode="True" DisplayFormatString="HH:mm">
                                        </PropertiesTimeEdit>
                                    </dx:GridViewDataTimeEditColumn>
                                    <dx:GridViewDataTimeEditColumn Caption="Jam Masuk" FieldName="JamMasuk" ShowInCustomizationForm="True" VisibleIndex="7">
                                        <PropertiesTimeEdit DisplayFormatInEditMode="True" DisplayFormatString="HH:mm">
                                        </PropertiesTimeEdit>
                                    </dx:GridViewDataTimeEditColumn>
                                    <dx:GridViewDataTimeEditColumn Caption="J. Keluar" FieldName="MsJamKeluar" ShowInCustomizationForm="True" VisibleIndex="8">
                                        <PropertiesTimeEdit DisplayFormatInEditMode="True" DisplayFormatString="HH:mm">
                                        </PropertiesTimeEdit>
                                    </dx:GridViewDataTimeEditColumn>
                                    <dx:GridViewDataTimeEditColumn Caption="Jam Keluar" FieldName="JamKeluar" ShowInCustomizationForm="True" VisibleIndex="9">
                                        <PropertiesTimeEdit DisplayFormatInEditMode="True" DisplayFormatString="HH:mm">
                                        </PropertiesTimeEdit>
                                    </dx:GridViewDataTimeEditColumn>
                                    <dx:GridViewDataMemoColumn FieldName="Keterangan" ShowInCustomizationForm="True" VisibleIndex="14">
                                    </dx:GridViewDataMemoColumn>
                                    <dx:GridViewDataTextColumn Caption="Nama" FieldName="tm_karyawan.Nama" ShowInCustomizationForm="True" VisibleIndex="4">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataCheckColumn Caption="Masuk" FieldName="Masuk_b" ShowInCustomizationForm="True" VisibleIndex="15">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataCheckColumn Caption="Libur/OFF" FieldName="Off_b" ShowInCustomizationForm="True" VisibleIndex="17">
                                    </dx:GridViewDataCheckColumn>
                                </Columns>
                                <Toolbars>
                                    <dx:GridViewToolbar>
                                        <Items>
                                            <dx:GridViewToolbarItem Name="btn_new" Text="New">
                                                <Image IconID="actions_new_svg_16x16">
                                                </Image>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Name="btn_delete_selected" Text="Delete Selected">
                                                <Image IconID="iconbuilder_actions_deletecircled_svg_16x16">
                                                </Image>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem Command="ShowFilterRow">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                            </dx:ASPxGridView>
                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_absensi" />
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>
