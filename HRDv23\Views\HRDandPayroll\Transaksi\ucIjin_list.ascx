﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucIjin_list.ascx.vb" Inherits="HRDv23.ucIjin_list" %>
    <%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

    <table style="width: 100%;">
        <tr>
            <td style="width: 150px; font-weight: bold">Released</td>
            <td style="width: 170px">
                <dx:ASPxCheckBox ID="chk_released" runat="server" CheckState="Unchecked">
                    <ClientSideEvents ValueChanged="function(s, e) {
	    grd_ijin.Refresh();
    }" />
                </dx:ASPxCheckBox>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td style="width: 150px; font-weight: bold">&nbsp;</td>
            <td style="width: 170px">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        </table>
    <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_ijin">
        <ClientSideEvents CustomButtonClick="function(s, e) {
	    var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	    if(e.buttonID!='btn_print'){
		    if(e.buttonID=='btn_delete'){
			    var b=confirm('Are you sure to delete this item?');
			    if(b){
				    cp_ijin.PerformCallback(e.buttonID+';'+rowKey);
			    }
		    }else{cp_ijin.PerformCallback(e.buttonID+';'+rowKey);}
	    }else{wd1.Show();}

    }" ToolbarItemClick="function(s, e) {
	    switch (e.item.name) { 
    case 'btn_new':
    cp_ijin.PerformCallback('new'); 
    break;
    }

    }" />
        <SettingsAdaptivity AdaptivityMode="HideDataCells">
        </SettingsAdaptivity>
        <SettingsPager AlwaysShowPager="True">
            <AllButton Visible="True">
            </AllButton>
            <PageSizeItemSettings Visible="True">
            </PageSizeItemSettings>
        </SettingsPager>
        <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
        <SettingsBehavior AllowFocusedRow="True" />
    <SettingsPopup>
    <FilterControl AutoUpdatePosition="False"></FilterControl>
    </SettingsPopup>
        <SettingsSearchPanel Visible="True" />
        <SettingsExport EnableClientSideExportAPI="True">
        </SettingsExport>
        <Columns>
            <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
                <CustomButtons>
                    <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                        <Image IconID="iconbuilder_actions_edit_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                        <Image IconID="scheduling_delete_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                        <Image IconID="iconbuilder_security_visibility_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_unrelease" Text="Un-Release">
                        <Image IconID="spreadsheet_3symbolsuncircled_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                </CustomButtons>
            </dx:GridViewCommandColumn>
            <dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="1" Caption="Perusahaan">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="2" Caption="Cabang">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="tm_ijin_master.NamaIjin" VisibleIndex="5" Caption="Ijin">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="tm_karyawan.NIK" VisibleIndex="3" Caption="NIK">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataDateColumn FieldName="StartDate" VisibleIndex="6">
                <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                </PropertiesDateEdit>
            </dx:GridViewDataDateColumn>
            <dx:GridViewDataDateColumn FieldName="EndDate" VisibleIndex="7">
                <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                </PropertiesDateEdit>
            </dx:GridViewDataDateColumn>
            <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="8">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn Caption="Nama" FieldName="tm_karyawan.Nama" VisibleIndex="4">
            </dx:GridViewDataTextColumn>
        </Columns>
        <Toolbars>
            <dx:GridViewToolbar>
                <Items>
                    <dx:GridViewToolbarItem Name="btn_new" Text="New">
                        <Image IconID="actions_new_svg_16x16">
                        </Image>
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Command="ShowFilterRow">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                    </dx:GridViewToolbarItem>
                </Items>
            </dx:GridViewToolbar>
        </Toolbars>
    </dx:ASPxGridView>

    <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_ijin">
    </dx:EntityServerModeDataSource>
