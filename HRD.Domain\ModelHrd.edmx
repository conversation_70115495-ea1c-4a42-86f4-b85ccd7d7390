﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="HRDModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="tm_absensi_setting">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="JamMasuk" Type="datetime" Nullable="false" />
          <Property Name="JamKeluar" Type="datetime" Nullable="false" />
          <Property Name="ToleransiJamMasuk" Type="int" Nullable="false" />
          <Property Name="ToleransiJamKeluar" Type="int" Nullable="false" />
          <Property Name="Shift" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="JamMasukJumat" Type="datetime" />
          <Property Name="JamKeluarJumat" Type="datetime" />
          <Property Name="JamMasukSabtu" Type="datetime" />
          <Property Name="JamKeluarSabtu" Type="datetime" />
          <Property Name="JamMasukMinggu" Type="datetime" />
          <Property Name="JamKeluarMinggu" Type="datetime" />
          <Property Name="HariKhusus" Type="bit" Nullable="false" />
          <Property Name="StartDate" Type="date" />
          <Property Name="EndDate" Type="date" />
        </EntityType>
        <EntityType Name="tm_agama">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="int" Nullable="false" />
          <Property Name="agama" Type="varchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_area">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeArea" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaArea" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="KodeNIK_Tetap" Type="varchar" MaxLength="10" />
          <Property Name="KodeNIK_Kontrak" Type="varchar" MaxLength="10" />
          <Property Name="NoRekeningBank" Type="varchar" MaxLength="50" />
          <Property Name="TglCustOffCuti" Type="date" />
          <Property Name="FlatRateBPJS" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_bonus_thr">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Periode" Type="date" Nullable="false" />
          <Property Name="Tahun" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Bulan" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Bonus" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_cabang">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="KodeCabang" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaCabang" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="IsHeadOffice" Type="bit" Nullable="false" />
          <Property Name="Alamat" Type="varchar" MaxLength="250" />
          <Property Name="Telp" Type="varchar" MaxLength="50" />
          <Property Name="Fax" Type="varchar" MaxLength="50" />
          <Property Name="UpahMinimum" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="UpahMinimum_Kes" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="SabtuLibur" Type="bit" Nullable="false" />
          <Property Name="MingguLibur" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_department">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NamaDepartemen" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_event_setting">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_Id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="StartDate" Type="date" Nullable="false" />
          <Property Name="EndDate" Type="date" Nullable="false" />
          <Property Name="MsMasuk" Type="datetime" Nullable="false" />
          <Property Name="MsKeluar" Type="datetime" Nullable="false" />
          <Property Name="NamaEvent" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_event_setting_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Event_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_ijin_master">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NamaIjin" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="IsCuti" Type="bit" Nullable="false" />
          <Property Name="Max" Type="int" Nullable="false" />
          <Property Name="KodeIjin" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="PotongGaji" Type="bit" Nullable="false" />
          <Property Name="PotongCutiTahunan" Type="bit" Nullable="false" />
          <Property Name="IsMasuk" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_jabatan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Jabatan" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="JumlahCutiTahunan" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_jenis_kelamin">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="JenisKelamin" Type="varchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_jurusan">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Pendidikan_id" Type="int" Nullable="false" />
          <Property Name="KodeJurusan" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaJurusan" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Createdby" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_karyawan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="NIK" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Nama" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Tempat" Type="varchar" MaxLength="50" />
          <Property Name="TglLahir" Type="datetime" />
          <Property Name="Umur" Type="int" />
          <Property Name="JenisKelamin_id" Type="int" />
          <Property Name="Agama_id" Type="int" />
          <Property Name="Alamat" Type="varchar" MaxLength="255" />
          <Property Name="Kota" Type="varchar" MaxLength="50" />
          <Property Name="Telp" Type="varchar" MaxLength="50" />
          <Property Name="StatusPerkawinan_id" Type="int" />
          <Property Name="Pendidikan_id" Type="int" />
          <Property Name="Jurusan_id" Type="int" />
          <Property Name="Alumni" Type="varchar" MaxLength="255" />
          <Property Name="TahunLulus" Type="varchar" MaxLength="4" />
          <Property Name="TglMasuk" Type="datetime" Nullable="false" />
          <Property Name="Berhenti_b" Type="bit" Nullable="false" />
          <Property Name="Tgl_Berhenti" Type="datetime" />
          <Property Name="AlasanBerhenti" Type="varchar" MaxLength="255" />
          <Property Name="NamaBank_id" Type="int" />
          <Property Name="NoRekening" Type="varchar" MaxLength="50" />
          <Property Name="NamaAccount" Type="varchar" MaxLength="50" />
          <Property Name="UserName" Type="varchar" MaxLength="50" />
          <Property Name="Password" Type="varchar" MaxLength="50" />
          <Property Name="Photo" Type="image" />
          <Property Name="Token" Type="uniqueidentifier" />
          <Property Name="TokenExpired" Type="datetime" />
          <Property Name="NoKTP" Type="varchar" MaxLength="50" />
          <Property Name="Ship" Type="int" />
          <Property Name="Createdby" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Version" Type="timestamp" StoreGeneratedPattern="Computed" Nullable="false" />
          <Property Name="NPWP" Type="varchar" MaxLength="50" />
          <Property Name="IsBPJS_Kes" Type="bit" Nullable="false" />
          <Property Name="IsBPJS_TK" Type="bit" Nullable="false" />
          <Property Name="StatusPerkawinan_NonPTKP_id" Type="int" />
          <Property Name="KaryawanTetap" Type="bit" Nullable="false" />
          <Property Name="TipeAbsensiOFF" Type="smallint" Nullable="false" />
          <Property Name="Email" Type="varchar" MaxLength="150" />
          <Property Name="TglReloadCuti" Type="date" />
        </EntityType>
        <EntityType Name="tm_karyawan_datadinas">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Tanggal" Type="datetime" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Department_id" Type="int" />
          <Property Name="Jabatan_id" Type="int" />
          <Property Name="Tetap" Type="bit" Nullable="false" />
          <Property Name="TglTetap" Type="datetime" />
          <Property Name="TglAwalKontrak" Type="datetime" />
          <Property Name="TglAkhirKontrak" Type="datetime" />
          <Property Name="Pd_GajiPokok" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Jabatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Makan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_PremiHadir" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Susu" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JKK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JKM" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalPendapatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JKK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JKM" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JPK_Mandiri" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_PPH21" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_SP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalPotongan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="THP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="NoKontrakKerja" Type="varchar" MaxLength="50" />
          <Property Name="Pd_T_Transport" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Kontrak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Probation" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_PremiAss" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Pajak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_SerPekerja" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="NIK_Lama" Type="varchar" MaxLength="50" />
          <Property Name="SaldoAwalCuti" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_libur_nasional">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NamaHariLibur" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="StartDate" Type="date" Nullable="false" />
          <Property Name="EndDate" Type="date" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Version" Type="timestamp" StoreGeneratedPattern="Computed" Nullable="false" />
          <Property Name="CutiBersama" Type="bit" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_menu">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="Title" Type="varchar" MaxLength="150" />
          <Property Name="Description" Type="varchar" MaxLength="250" />
          <Property Name="Url" Type="varchar" MaxLength="100" />
          <Property Name="Parent" Type="int" />
          <Property Name="BeginGroup" Type="bit" />
        </EntityType>
        <EntityType Name="tm_menu_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SiteMenu_id" Type="int" Nullable="false" />
          <Property Name="Role_id" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_namabank">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeBank" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaBank" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="KodeKliring" Type="varchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="tm_off_karyawan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="TglBerlaku" Type="date" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="Createdby" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="Version" Type="timestamp" StoreGeneratedPattern="Computed" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_off_karyawan_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Off_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Minggu" Type="int" />
          <Property Name="Senin" Type="int" />
          <Property Name="Selasa" Type="int" />
          <Property Name="Rabu" Type="int" />
          <Property Name="Kamis" Type="int" />
          <Property Name="Jumat" Type="int" />
          <Property Name="Sabtu" Type="int" />
        </EntityType>
        <EntityType Name="tm_pendidikan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodePendidikan" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="NamaPendidikan" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Createdby" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RoleName" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_shift_absensi">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="IsOff" Type="bit" Nullable="false" />
          <Property Name="StartTime" Type="time" Precision="0" />
          <Property Name="EndTime" Type="time" Precision="7" />
          <Property Name="Createdby" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Deskripsi" Type="varchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="tm_status_perkawinan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeStatus" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="StatusPernikahan" Type="varchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_status_PTKP">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeStatus" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="StatusPernikahan" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="PTKP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_ter_pajak">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Kode" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="Mulai" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Sampai" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TarifPercent" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_user">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Username" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Password" Type="varchar" MaxLength="200" />
          <Property Name="Email" Type="varchar" MaxLength="50" />
          <Property Name="isApproved" Type="bit" Nullable="false" />
          <Property Name="isLockedOut" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Area_id" Type="int" />
          <Property Name="Cabang_id" Type="int" />
          <Property Name="PermisionArea" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_user_area">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="User_id" Type="int" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tm_user_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="User_id" Type="int" Nullable="false" />
          <Property Name="Role_id" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_absensi">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="karyawan_id" Type="int" Nullable="false" />
          <Property Name="Tanggal" Type="datetime" Nullable="false" />
          <Property Name="MsJamMasuk" Type="datetime" />
          <Property Name="JamMasuk" Type="datetime" />
          <Property Name="MsJamKeluar" Type="datetime" />
          <Property Name="JamKeluar" Type="datetime" />
          <Property Name="Terlambat" Type="int" Nullable="false" />
          <Property Name="Overtime" Type="int" Nullable="false" />
          <Property Name="OverTime_HLibur" Type="int" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="Alpha_b" Type="bit" Nullable="false" />
          <Property Name="Cuti_b" Type="bit" Nullable="false" />
          <Property Name="Ijin_b" Type="bit" Nullable="false" />
          <Property Name="PulangCepat" Type="int" Nullable="false" />
          <Property Name="Lembur_HKerja_1" Type="int" Nullable="false" />
          <Property Name="Lembur_HKerja_2" Type="int" Nullable="false" />
          <Property Name="Lembur_HKerja_3" Type="int" Nullable="false" />
          <Property Name="Lembur_HLibur_1_7" Type="int" Nullable="false" />
          <Property Name="Lembur_HLibur_8" Type="int" Nullable="false" />
          <Property Name="Lembur_HLibur_9_10" Type="int" Nullable="false" />
          <Property Name="Masuk_b" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Ijin_id" Type="int" />
          <Property Name="Off_b" Type="bit" Nullable="false" />
          <Property Name="PotongGaji" Type="bit" Nullable="false" />
          <Property Name="LemburLine_id" Type="int" />
          <Property Name="FromAbsensi" Type="bit" Nullable="false" />
          <Property Name="LiburNasional" Type="bit" Nullable="false" />
          <Property Name="CutiBersama" Type="bit" Nullable="false" />
          <Property Name="LiburNasional_id" Type="int" />
          <Property Name="GantiHari" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_cuti_besar">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="NoCutiBesar" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Tanggal" Type="date" Nullable="false" />
          <Property Name="Tahun" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Status" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="Total" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_cuti_besar_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CutiBesar_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="TanggalTetap" Type="date" Nullable="false" />
          <Property Name="LamaKerja" Type="int" Nullable="false" />
          <Property Name="Pd_GajiPokok" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Jabatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Susu" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Probation" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Kehadiran" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Makan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Total" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_gaji">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="Tahun" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Bulan" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Tanggal" Type="date" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="TotalPendapatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalPotongan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalNet" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Bayar" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="KurangBayar" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="NoVoucher" Type="varchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="tr_gaji_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Gaji_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Pd_GajiPokok" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Jabatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Transport" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Makan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_PremiHadir" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Susu" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Kontrak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Probation" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_PremiAss" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Pajak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JKK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JKM" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalPendapatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JKK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JKM" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_P_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JHT" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JPK" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_K_JPK_Mandiri" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_PPH21" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_SP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_SerPekerja" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_Kasbon" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TotalPotongan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="THP" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pot_Alpha" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="HariKerja" Type="int" Nullable="false" />
          <Property Name="JumlahAlpha" Type="int" Nullable="false" />
          <Property Name="JumlahIjin" Type="int" Nullable="false" />
          <Property Name="JumlahIjinDispensasi" Type="int" Nullable="false" />
          <Property Name="JumlahCuti" Type="int" Nullable="false" />
          <Property Name="Pd_Lembur" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="JumlahSakit" Type="int" Nullable="false" />
          <Property Name="JumlahLembur" Type="int" Nullable="false" />
          <Property Name="JumlahPotongGaji" Type="int" Nullable="false" />
          <Property Name="Pd_Insentif" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Lain" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="DataDinas_id" Type="int" />
          <Property Name="StatusPTKP_id" Type="int" />
          <Property Name="Ter_pajak_id" Type="int" />
          <Property Name="Ter_pajak_percent" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_ijin">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="Ijin_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="StartDate" Type="date" Nullable="false" />
          <Property Name="EndDate" Type="date" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="Photo" Type="image" />
        </EntityType>
        <EntityType Name="tr_kompensasi_kontrak">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="NoKompensasi" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Tanggal" Type="date" Nullable="false" />
          <Property Name="Periode" Type="varchar" MaxLength="7" Nullable="false" />
          <Property Name="PeriodeDate" Type="date" Nullable="false" />
          <Property Name="Status" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="Total" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_kompensasi_kontrak_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Kompensasi_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="TanggalAwalKontrak" Type="date" Nullable="false" />
          <Property Name="TanggalAkhirKontrak" Type="date" Nullable="false" />
          <Property Name="LamaKerja" Type="int" Nullable="false" />
          <Property Name="NilaiKompensasi" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_pinjaman_bayar">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="KodeBayar" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Tanggal" Type="datetime" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Amount" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Memo" Type="text" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="tr_pinjaman_bayar_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Bayar_id" Type="bigint" Nullable="false" />
          <Property Name="Pinjaman_id" Type="bigint" Nullable="false" />
          <Property Name="Amount" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_pinjaman_move">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Pinjaman_id" Type="bigint" Nullable="false" />
          <Property Name="TipeTrans" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="TransDate" Type="datetime" Nullable="false" />
          <Property Name="Amount" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Memo" Type="text" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="PinjBayarLine_id" Type="bigint" />
          <Property Name="GajiLine_id" Type="bigint" />
        </EntityType>
        <EntityType Name="tr_register_pinjaman">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="KodePinjaman" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Tanggal" Type="datetime" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="Amount" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Memo" Type="text" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="TotalBayar" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Saldo" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CicilanPerBulam" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="NoVoucher" Type="varchar" MaxLength="50" />
          <Property Name="Status" Type="varchar" MaxLength="10" />
          <Property Name="Version" Type="timestamp" StoreGeneratedPattern="Computed" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_spk_lembur">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="NoSpk" Type="varchar" MaxLength="30" Nullable="false" />
          <Property Name="Tanggal" Type="date" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Version" Type="timestamp" StoreGeneratedPattern="Computed" Nullable="false" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="TotalJam" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_spk_lembur_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SPKLembur_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_thr">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="KodeTHR" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Periode" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="PeriodeDate" Type="datetime" Nullable="false" />
          <Property Name="Keterangan" Type="text" />
          <Property Name="Total" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="isCompleted" Type="bit" Nullable="false" />
          <Property Name="TipeTHR" Type="int" Nullable="false" />
          <Property Name="GajiPokok_b" Type="bit" Nullable="false" />
          <Property Name="T_Jabatan_b" Type="bit" Nullable="false" />
          <Property Name="T_Transport_b" Type="bit" Nullable="false" />
          <Property Name="T_Susu_b" Type="bit" Nullable="false" />
          <Property Name="T_Makan_b" Type="bit" Nullable="false" />
          <Property Name="T_Kontrak_b" Type="bit" Nullable="false" />
          <Property Name="T_Probation_b" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_thr_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Thr_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="GajiPokok" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="T_Jabatan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Bonus" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Bonus_id" Type="int" />
          <Property Name="DataDinas_id" Type="int" />
          <Property Name="T_Transport" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="T_Susu" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="T_Makan" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="T_Kontrak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="T_Probation" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pd_T_Pajak" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Total" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Pt_PPH21" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TahunKerja" Type="int" Nullable="false" />
          <Property Name="PotMasaKerja" Type="int" Nullable="false" />
          <Property Name="StatusPTKP_id" Type="int" />
          <Property Name="Ter_pajak_id" Type="int" />
          <Property Name="Ter_pajak_percent" Type="decimal" Precision="18" Scale="2" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_tunjangan_onetime">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="Karyawan_id" Type="int" Nullable="false" />
          <Property Name="PeriodeGaji" Type="date" Nullable="false" />
          <Property Name="Tahun" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Bulan" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="TunjanganInsentif" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="TunjanganLain" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="Keterangan" Type="varchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="varchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="varchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="Posted" Type="bit" Nullable="false" />
          <Property Name="PostedBy" Type="varchar" MaxLength="50" />
          <Property Name="PostedDate" Type="datetime" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'HRD_MNF.dbo.vw_KaryawanAkanHabisKontrak' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="vw_KaryawanAkanHabisKontrak">
          <Key>
            <PropertyRef Name="NIK" />
            <PropertyRef Name="Nama" />
            <PropertyRef Name="Area_id" />
            <PropertyRef Name="Cabang_id" />
            <PropertyRef Name="KodeArea" />
            <PropertyRef Name="NamaArea" />
            <PropertyRef Name="KodeCabang" />
            <PropertyRef Name="NamaCabang" />
          </Key>
          <Property Name="NIK" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Nama" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="TglAkhirKontrak" Type="datetime" />
          <Property Name="Id" Type="int" />
          <Property Name="Area_id" Type="int" Nullable="false" />
          <Property Name="Cabang_id" Type="int" Nullable="false" />
          <Property Name="KodeArea" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaArea" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="KodeCabang" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaCabang" Type="varchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <Association Name="FK_Cabang_Area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_cabang">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteMenuRole_SiteMenu">
          <End Role="tm_menu" Type="Self.tm_menu" Multiplicity="1" />
          <End Role="tm_menu_role" Type="Self.tm_menu_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_menu">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_menu_role">
              <PropertyRef Name="SiteMenu_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteMenuRole_SiteRole">
          <End Role="tm_role" Type="Self.tm_role" Multiplicity="1" />
          <End Role="tm_menu_role" Type="Self.tm_menu_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_menu_role">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUser_Area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="0..1" />
          <End Role="tm_user" Type="Self.tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUser_Cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="0..1" />
          <End Role="tm_user" Type="Self.tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUserRole_SiteRole">
          <End Role="tm_role" Type="Self.tm_role" Multiplicity="1" />
          <End Role="tm_user_role" Type="Self.tm_user_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_role">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUserRole_SiteUser">
          <End Role="tm_user" Type="Self.tm_user" Multiplicity="1" />
          <End Role="tm_user_role" Type="Self.tm_user_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_user">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_role">
              <PropertyRef Name="User_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_absensi_setting_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_absensi_setting" Type="Self.tm_absensi_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_absensi_setting">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_absensi_setting_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_absensi_setting" Type="Self.tm_absensi_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_absensi_setting">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_bonus_thr_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_bonus_thr" Type="Self.tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_bonus_thr_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_bonus_thr" Type="Self.tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_bonus_thr_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tm_bonus_thr" Type="Self.tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_line_tm_event_setting">
          <End Role="tm_event_setting" Type="Self.tm_event_setting" Multiplicity="1" />
          <End Role="tm_event_setting_line" Type="Self.tm_event_setting_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_event_setting">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting_line">
              <PropertyRef Name="Event_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tm_event_setting_line" Type="Self.tm_event_setting_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_event_setting" Type="Self.tm_event_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting">
              <PropertyRef Name="Area_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_event_setting" Type="Self.tm_event_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_jurusan_tm_pendidikan">
          <End Role="tm_pendidikan" Type="Self.tm_pendidikan" Multiplicity="1" />
          <End Role="tm_jurusan" Type="Self.tm_jurusan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_pendidikan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_jurusan">
              <PropertyRef Name="Pendidikan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_department">
          <End Role="tm_department" Type="Self.tm_department" Multiplicity="0..1" />
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_department">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Department_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_jabatan">
          <End Role="tm_jabatan" Type="Self.tm_jabatan" Multiplicity="0..1" />
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jabatan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Jabatan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_agama">
          <End Role="tm_agama" Type="Self.tm_agama" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_agama">
              <PropertyRef Name="id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Agama_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_jenis_kelamin">
          <End Role="tm_jenis_kelamin" Type="Self.tm_jenis_kelamin" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jenis_kelamin">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="JenisKelamin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_jurusan">
          <End Role="tm_jurusan" Type="Self.tm_jurusan" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jurusan">
              <PropertyRef Name="id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Jurusan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_namabank">
          <End Role="tm_namabank" Type="Self.tm_namabank" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_namabank">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="NamaBank_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_pendidikan">
          <End Role="tm_pendidikan" Type="Self.tm_pendidikan" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_pendidikan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Pendidikan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_status_perkawinan">
          <End Role="tm_status_perkawinan" Type="Self.tm_status_perkawinan" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_perkawinan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="StatusPerkawinan_NonPTKP_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_status_PTKP">
          <End Role="tm_status_PTKP" Type="Self.tm_status_PTKP" Multiplicity="0..1" />
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_PTKP">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="StatusPerkawinan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_libur_nasional_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_libur_nasional" Type="Self.tm_libur_nasional" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_libur_nasional">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_off_karyawan">
          <End Role="tm_off_karyawan" Type="Self.tm_off_karyawan" Multiplicity="1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_off_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Off_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_jumat">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Jumat" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_kamis">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Kamis" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_minggu">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Minggu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_rabu">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Rabu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_sabtu">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Sabtu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_selasa">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Selasa" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_senin">
          <End Role="tm_shift_absensi" Type="Self.tm_shift_absensi" Multiplicity="0..1" />
          <End Role="tm_off_karyawan_line" Type="Self.tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Senin" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_off_karyawan" Type="Self.tm_off_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tm_off_karyawan" Type="Self.tm_off_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_user_area_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tm_user_area" Type="Self.tm_user_area" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_area">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_user_area_tm_user">
          <End Role="tm_user" Type="Self.tm_user" Multiplicity="1" />
          <End Role="tm_user_area" Type="Self.tm_user_area" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_user">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_area">
              <PropertyRef Name="User_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_libur_nasional">
          <End Role="tm_libur_nasional" Type="Self.tm_libur_nasional" Multiplicity="0..1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_libur_nasional">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="LiburNasional_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tr_ijin">
          <End Role="tr_ijin" Type="Self.tr_ijin" Multiplicity="0..1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_ijin">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Ijin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tr_spk_lembur_line">
          <End Role="tr_spk_lembur_line" Type="Self.tr_spk_lembur_line" Multiplicity="0..1" />
          <End Role="tr_absensi" Type="Self.tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_spk_lembur_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="LemburLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_cuti_besar_line" Type="Self.tr_cuti_besar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_line_tr_cuti_besar">
          <End Role="tr_cuti_besar" Type="Self.tr_cuti_besar" Multiplicity="1" />
          <End Role="tr_cuti_besar_line" Type="Self.tr_cuti_besar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_cuti_besar">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar_line">
              <PropertyRef Name="CutiBesar_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_cuti_besar" Type="Self.tr_cuti_besar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_cuti_besar" Type="Self.tr_cuti_besar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_karyawan_datadinas">
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="0..1" />
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan_datadinas">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="DataDinas_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_status_PTKP">
          <End Role="tm_status_PTKP" Type="Self.tm_status_PTKP" Multiplicity="0..1" />
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_PTKP">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="StatusPTKP_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_ter_pajak">
          <End Role="tm_ter_pajak" Type="Self.tm_ter_pajak" Multiplicity="0..1" />
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_ter_pajak">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Ter_pajak_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tr_gaji">
          <End Role="tr_gaji" Type="Self.tr_gaji" Multiplicity="1" />
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_gaji">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Gaji_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_gaji" Type="Self.tr_gaji" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_gaji" Type="Self.tr_gaji" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_ijin" Type="Self.tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_ijin" Type="Self.tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_ijin_master">
          <End Role="tm_ijin_master" Type="Self.tm_ijin_master" Multiplicity="1" />
          <End Role="tr_ijin" Type="Self.tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_ijin_master">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Ijin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_ijin" Type="Self.tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_kompensasi_kontrak_line" Type="Self.tr_kompensasi_kontrak_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak">
          <End Role="tr_kompensasi_kontrak" Type="Self.tr_kompensasi_kontrak" Multiplicity="1" />
          <End Role="tr_kompensasi_kontrak_line" Type="Self.tr_kompensasi_kontrak_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak_line">
              <PropertyRef Name="Kompensasi_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_kompensasi_kontrak" Type="Self.tr_kompensasi_kontrak" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_kompensasi_kontrak" Type="Self.tr_kompensasi_kontrak" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar">
          <End Role="tr_pinjaman_bayar" Type="Self.tr_pinjaman_bayar" Multiplicity="1" />
          <End Role="tr_pinjaman_bayar_line" Type="Self.tr_pinjaman_bayar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_pinjaman_bayar">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Bayar_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_line_tr_register_pinjaman">
          <End Role="tr_register_pinjaman" Type="Self.tr_register_pinjaman" Multiplicity="1" />
          <End Role="tr_pinjaman_bayar_line" Type="Self.tr_pinjaman_bayar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_register_pinjaman">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Pinjaman_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_pinjaman_bayar" Type="Self.tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_pinjaman_bayar" Type="Self.tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_pinjaman_bayar" Type="Self.tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_move_tr_gaji_line">
          <End Role="tr_gaji_line" Type="Self.tr_gaji_line" Multiplicity="0..1" />
          <End Role="tr_pinjaman_move" Type="Self.tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_gaji_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="GajiLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_move_tr_pinjaman_bayar_line">
          <End Role="tr_pinjaman_bayar_line" Type="Self.tr_pinjaman_bayar_line" Multiplicity="0..1" />
          <End Role="tr_pinjaman_move" Type="Self.tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="PinjBayarLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_move_tr_register_pinjaman">
          <End Role="tr_register_pinjaman" Type="Self.tr_register_pinjaman" Multiplicity="1" />
          <End Role="tr_pinjaman_move" Type="Self.tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_register_pinjaman">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="Pinjaman_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_register_pinjaman" Type="Self.tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_register_pinjaman" Type="Self.tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_register_pinjaman" Type="Self.tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_spk_lembur_line" Type="Self.tr_spk_lembur_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_line_tr_spk_lembur">
          <End Role="tr_spk_lembur" Type="Self.tr_spk_lembur" Multiplicity="1" />
          <End Role="tr_spk_lembur_line" Type="Self.tr_spk_lembur_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_spk_lembur">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur_line">
              <PropertyRef Name="SPKLembur_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_spk_lembur" Type="Self.tr_spk_lembur" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_spk_lembur" Type="Self.tr_spk_lembur" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tm_bonus_thr">
          <End Role="tm_bonus_thr" Type="Self.tm_bonus_thr" Multiplicity="0..1" />
          <End Role="tr_thr_line" Type="Self.tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_bonus_thr">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Bonus_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_thr_line" Type="Self.tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tm_karyawan_datadinas">
          <End Role="tm_karyawan_datadinas" Type="Self.tm_karyawan_datadinas" Multiplicity="0..1" />
          <End Role="tr_thr_line" Type="Self.tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan_datadinas">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="DataDinas_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tr_thr">
          <End Role="tr_thr" Type="Self.tr_thr" Multiplicity="1" />
          <End Role="tr_thr_line" Type="Self.tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_thr">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Thr_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_thr" Type="Self.tr_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_thr" Type="Self.tr_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_area">
          <End Role="tm_area" Type="Self.tm_area" Multiplicity="1" />
          <End Role="tr_tunjangan_onetime" Type="Self.tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_cabang">
          <End Role="tm_cabang" Type="Self.tm_cabang" Multiplicity="1" />
          <End Role="tr_tunjangan_onetime" Type="Self.tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_karyawan">
          <End Role="tm_karyawan" Type="Self.tm_karyawan" Multiplicity="1" />
          <End Role="tr_tunjangan_onetime" Type="Self.tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="HRDModelStoreContainer">
          <EntitySet Name="tm_absensi_setting" EntityType="Self.tm_absensi_setting" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_agama" EntityType="Self.tm_agama" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_area" EntityType="Self.tm_area" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_bonus_thr" EntityType="Self.tm_bonus_thr" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_cabang" EntityType="Self.tm_cabang" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_department" EntityType="Self.tm_department" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_event_setting" EntityType="Self.tm_event_setting" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_event_setting_line" EntityType="Self.tm_event_setting_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_ijin_master" EntityType="Self.tm_ijin_master" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_jabatan" EntityType="Self.tm_jabatan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_jenis_kelamin" EntityType="Self.tm_jenis_kelamin" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_jurusan" EntityType="Self.tm_jurusan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_karyawan" EntityType="Self.tm_karyawan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_karyawan_datadinas" EntityType="Self.tm_karyawan_datadinas" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_libur_nasional" EntityType="Self.tm_libur_nasional" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_menu" EntityType="Self.tm_menu" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_menu_role" EntityType="Self.tm_menu_role" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_namabank" EntityType="Self.tm_namabank" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_off_karyawan" EntityType="Self.tm_off_karyawan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_off_karyawan_line" EntityType="Self.tm_off_karyawan_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_pendidikan" EntityType="Self.tm_pendidikan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_role" EntityType="Self.tm_role" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_shift_absensi" EntityType="Self.tm_shift_absensi" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_status_perkawinan" EntityType="Self.tm_status_perkawinan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_status_PTKP" EntityType="Self.tm_status_PTKP" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_ter_pajak" EntityType="Self.tm_ter_pajak" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_user" EntityType="Self.tm_user" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_user_area" EntityType="Self.tm_user_area" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_user_role" EntityType="Self.tm_user_role" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_absensi" EntityType="Self.tr_absensi" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_cuti_besar" EntityType="Self.tr_cuti_besar" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_cuti_besar_line" EntityType="Self.tr_cuti_besar_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_gaji" EntityType="Self.tr_gaji" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_gaji_line" EntityType="Self.tr_gaji_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_ijin" EntityType="Self.tr_ijin" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_kompensasi_kontrak" EntityType="Self.tr_kompensasi_kontrak" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_kompensasi_kontrak_line" EntityType="Self.tr_kompensasi_kontrak_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_pinjaman_bayar" EntityType="Self.tr_pinjaman_bayar" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_pinjaman_bayar_line" EntityType="Self.tr_pinjaman_bayar_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_pinjaman_move" EntityType="Self.tr_pinjaman_move" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_register_pinjaman" EntityType="Self.tr_register_pinjaman" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_spk_lembur" EntityType="Self.tr_spk_lembur" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_spk_lembur_line" EntityType="Self.tr_spk_lembur_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_thr" EntityType="Self.tr_thr" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_thr_line" EntityType="Self.tr_thr_line" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_tunjangan_onetime" EntityType="Self.tr_tunjangan_onetime" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="vw_KaryawanAkanHabisKontrak" EntityType="Self.vw_KaryawanAkanHabisKontrak" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [vw_KaryawanAkanHabisKontrak].[NIK] AS [NIK], 
    [vw_KaryawanAkanHabisKontrak].[Nama] AS [Nama], 
    [vw_KaryawanAkanHabisKontrak].[TglAkhirKontrak] AS [TglAkhirKontrak], 
    [vw_KaryawanAkanHabisKontrak].[Id] AS [Id], 
    [vw_KaryawanAkanHabisKontrak].[Area_id] AS [Area_id], 
    [vw_KaryawanAkanHabisKontrak].[Cabang_id] AS [Cabang_id], 
    [vw_KaryawanAkanHabisKontrak].[KodeArea] AS [KodeArea], 
    [vw_KaryawanAkanHabisKontrak].[NamaArea] AS [NamaArea], 
    [vw_KaryawanAkanHabisKontrak].[KodeCabang] AS [KodeCabang], 
    [vw_KaryawanAkanHabisKontrak].[NamaCabang] AS [NamaCabang]
    FROM [dbo].[vw_KaryawanAkanHabisKontrak] AS [vw_KaryawanAkanHabisKontrak]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="FK_Cabang_Area" Association="Self.FK_Cabang_Area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_cabang" EntitySet="tm_cabang" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteMenuRole_SiteMenu" Association="Self.FK_SiteMenuRole_SiteMenu">
            <End Role="tm_menu" EntitySet="tm_menu" />
            <End Role="tm_menu_role" EntitySet="tm_menu_role" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteMenuRole_SiteRole" Association="Self.FK_SiteMenuRole_SiteRole">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_menu_role" EntitySet="tm_menu_role" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUser_Area" Association="Self.FK_SiteUser_Area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUser_Cabang" Association="Self.FK_SiteUser_Cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUserRole_SiteRole" Association="Self.FK_SiteUserRole_SiteRole">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_user_role" EntitySet="tm_user_role" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUserRole_SiteUser" Association="Self.FK_SiteUserRole_SiteUser">
            <End Role="tm_user" EntitySet="tm_user" />
            <End Role="tm_user_role" EntitySet="tm_user_role" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_absensi_setting_tm_area" Association="Self.FK_tm_absensi_setting_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_absensi_setting" EntitySet="tm_absensi_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_absensi_setting_tm_cabang" Association="Self.FK_tm_absensi_setting_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_absensi_setting" EntitySet="tm_absensi_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_bonus_thr_tm_area" Association="Self.FK_tm_bonus_thr_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_bonus_thr_tm_cabang" Association="Self.FK_tm_bonus_thr_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_bonus_thr_tm_karyawan" Association="Self.FK_tm_bonus_thr_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_line_tm_event_setting" Association="Self.FK_tm_event_setting_line_tm_event_setting">
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
            <End Role="tm_event_setting_line" EntitySet="tm_event_setting_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_line_tm_karyawan" Association="Self.FK_tm_event_setting_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_event_setting_line" EntitySet="tm_event_setting_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_tm_area" Association="Self.FK_tm_event_setting_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_tm_cabang" Association="Self.FK_tm_event_setting_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_jurusan_tm_pendidikan" Association="Self.FK_tm_jurusan_tm_pendidikan">
            <End Role="tm_pendidikan" EntitySet="tm_pendidikan" />
            <End Role="tm_jurusan" EntitySet="tm_jurusan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_area" Association="Self.FK_tm_karyawan_datadinas_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_cabang" Association="Self.FK_tm_karyawan_datadinas_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_department" Association="Self.FK_tm_karyawan_datadinas_tm_department">
            <End Role="tm_department" EntitySet="tm_department" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_jabatan" Association="Self.FK_tm_karyawan_datadinas_tm_jabatan">
            <End Role="tm_jabatan" EntitySet="tm_jabatan" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_karyawan" Association="Self.FK_tm_karyawan_datadinas_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_agama" Association="Self.FK_tm_karyawan_tm_agama">
            <End Role="tm_agama" EntitySet="tm_agama" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_area" Association="Self.FK_tm_karyawan_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_cabang" Association="Self.FK_tm_karyawan_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_jenis_kelamin" Association="Self.FK_tm_karyawan_tm_jenis_kelamin">
            <End Role="tm_jenis_kelamin" EntitySet="tm_jenis_kelamin" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_jurusan" Association="Self.FK_tm_karyawan_tm_jurusan">
            <End Role="tm_jurusan" EntitySet="tm_jurusan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_namabank" Association="Self.FK_tm_karyawan_tm_namabank">
            <End Role="tm_namabank" EntitySet="tm_namabank" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_pendidikan" Association="Self.FK_tm_karyawan_tm_pendidikan">
            <End Role="tm_pendidikan" EntitySet="tm_pendidikan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_status_perkawinan" Association="Self.FK_tm_karyawan_tm_status_perkawinan">
            <End Role="tm_status_perkawinan" EntitySet="tm_status_perkawinan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_status_PTKP" Association="Self.FK_tm_karyawan_tm_status_PTKP">
            <End Role="tm_status_PTKP" EntitySet="tm_status_PTKP" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_libur_nasional_tm_area" Association="Self.FK_tm_libur_nasional_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_libur_nasional" EntitySet="tm_libur_nasional" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_karyawan" Association="Self.FK_tm_off_karyawan_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_off_karyawan" Association="Self.FK_tm_off_karyawan_line_tm_off_karyawan">
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_jumat" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_jumat">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_kamis" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_kamis">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_minggu" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_minggu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_rabu" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_rabu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_sabtu" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_sabtu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_selasa" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_selasa">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_senin" Association="Self.FK_tm_off_karyawan_line_tm_shift_absensi_senin">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_tm_area" Association="Self.FK_tm_off_karyawan_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_tm_cabang" Association="Self.FK_tm_off_karyawan_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_user_area_tm_area" Association="Self.FK_tm_user_area_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_user_area" EntitySet="tm_user_area" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_user_area_tm_user" Association="Self.FK_tm_user_area_tm_user">
            <End Role="tm_user" EntitySet="tm_user" />
            <End Role="tm_user_area" EntitySet="tm_user_area" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_area" Association="Self.FK_tr_absensi_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_cabang" Association="Self.FK_tr_absensi_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_karyawan" Association="Self.FK_tr_absensi_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_libur_nasional" Association="Self.FK_tr_absensi_tm_libur_nasional">
            <End Role="tm_libur_nasional" EntitySet="tm_libur_nasional" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tr_ijin" Association="Self.FK_tr_absensi_tr_ijin">
            <End Role="tr_ijin" EntitySet="tr_ijin" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tr_spk_lembur_line" Association="Self.FK_tr_absensi_tr_spk_lembur_line">
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_line_tm_karyawan" Association="Self.FK_tr_cuti_besar_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_cuti_besar_line" EntitySet="tr_cuti_besar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_line_tr_cuti_besar" Association="Self.FK_tr_cuti_besar_line_tr_cuti_besar">
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
            <End Role="tr_cuti_besar_line" EntitySet="tr_cuti_besar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_tm_area" Association="Self.FK_tr_cuti_besar_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_tm_cabang" Association="Self.FK_tr_cuti_besar_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_karyawan" Association="Self.FK_tr_gaji_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_karyawan_datadinas" Association="Self.FK_tr_gaji_line_tm_karyawan_datadinas">
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_status_PTKP" Association="Self.FK_tr_gaji_line_tm_status_PTKP">
            <End Role="tm_status_PTKP" EntitySet="tm_status_PTKP" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_ter_pajak" Association="Self.FK_tr_gaji_line_tm_ter_pajak">
            <End Role="tm_ter_pajak" EntitySet="tm_ter_pajak" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tr_gaji" Association="Self.FK_tr_gaji_line_tr_gaji">
            <End Role="tr_gaji" EntitySet="tr_gaji" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_tm_area" Association="Self.FK_tr_gaji_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_gaji" EntitySet="tr_gaji" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_tm_cabang" Association="Self.FK_tr_gaji_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_gaji" EntitySet="tr_gaji" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_area" Association="Self.FK_tr_ijin_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_cabang" Association="Self.FK_tr_ijin_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_ijin_master" Association="Self.FK_tr_ijin_tm_ijin_master">
            <End Role="tm_ijin_master" EntitySet="tm_ijin_master" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_karyawan" Association="Self.FK_tr_ijin_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_line_tm_karyawan" Association="Self.FK_tr_kompensasi_kontrak_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_kompensasi_kontrak_line" EntitySet="tr_kompensasi_kontrak_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak" Association="Self.FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak">
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
            <End Role="tr_kompensasi_kontrak_line" EntitySet="tr_kompensasi_kontrak_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_tm_area" Association="Self.FK_tr_kompensasi_kontrak_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_tm_cabang" Association="Self.FK_tr_kompensasi_kontrak_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar" Association="Self.FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar">
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_line_tr_register_pinjaman" Association="Self.FK_tr_pinjaman_bayar_line_tr_register_pinjaman">
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_area" Association="Self.FK_tr_pinjaman_bayar_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_cabang" Association="Self.FK_tr_pinjaman_bayar_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_karyawan" Association="Self.FK_tr_pinjaman_bayar_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_move_tr_gaji_line" Association="Self.FK_tr_pinjaman_move_tr_gaji_line">
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_move_tr_pinjaman_bayar_line" Association="Self.FK_tr_pinjaman_move_tr_pinjaman_bayar_line">
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_move_tr_register_pinjaman" Association="Self.FK_tr_pinjaman_move_tr_register_pinjaman">
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_area" Association="Self.FK_tr_register_pinjaman_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_cabang" Association="Self.FK_tr_register_pinjaman_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_karyawan" Association="Self.FK_tr_register_pinjaman_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_line_tm_karyawan" Association="Self.FK_tr_spk_lembur_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_line_tr_spk_lembur" Association="Self.FK_tr_spk_lembur_line_tr_spk_lembur">
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_tm_area" Association="Self.FK_tr_spk_lembur_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_tm_cabang" Association="Self.FK_tr_spk_lembur_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tm_bonus_thr" Association="Self.FK_tr_thr_line_tm_bonus_thr">
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tm_karyawan" Association="Self.FK_tr_thr_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tm_karyawan_datadinas" Association="Self.FK_tr_thr_line_tm_karyawan_datadinas">
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tr_thr" Association="Self.FK_tr_thr_line_tr_thr">
            <End Role="tr_thr" EntitySet="tr_thr" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_tm_area" Association="Self.FK_tr_thr_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_thr" EntitySet="tr_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_tm_cabang" Association="Self.FK_tr_thr_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_thr" EntitySet="tr_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_area" Association="Self.FK_tr_tunjangan_onetime_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_cabang" Association="Self.FK_tr_tunjangan_onetime_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_karyawan" Association="Self.FK_tr_tunjangan_onetime_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="HRDModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="HRDEntities" annotation:LazyLoadingEnabled="true" >
          <EntitySet Name="tm_menu_role" EntityType="HRDModel.tm_menu_role" />
          <EntitySet Name="tm_role" EntityType="HRDModel.tm_role" />
          <EntitySet Name="tm_user" EntityType="HRDModel.tm_user" />
          <EntitySet Name="tm_user_role" EntityType="HRDModel.tm_user_role" />
          <AssociationSet Name="FK_SiteMenuRole_SiteRole" Association="HRDModel.FK_SiteMenuRole_SiteRole">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_menu_role" EntitySet="tm_menu_role" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUserRole_SiteRole" Association="HRDModel.FK_SiteUserRole_SiteRole">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_user_role" EntitySet="tm_user_role" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUserRole_SiteUser" Association="HRDModel.FK_SiteUserRole_SiteUser">
            <End Role="tm_user" EntitySet="tm_user" />
            <End Role="tm_user_role" EntitySet="tm_user_role" />
          </AssociationSet>
          <EntitySet Name="tm_menu" EntityType="HRDModel.tm_menu" />
          <AssociationSet Name="FK_SiteMenuRole_SiteMenu" Association="HRDModel.FK_SiteMenuRole_SiteMenu">
            <End Role="tm_menu" EntitySet="tm_menu" />
            <End Role="tm_menu_role" EntitySet="tm_menu_role" />
          </AssociationSet>
          <EntitySet Name="tm_jenis_kelamin" EntityType="HRDModel.tm_jenis_kelamin" />
          <EntitySet Name="tm_jurusan" EntityType="HRDModel.tm_jurusan" />
          <EntitySet Name="tm_pendidikan" EntityType="HRDModel.tm_pendidikan" />
          <EntitySet Name="tm_status_PTKP" EntityType="HRDModel.tm_status_PTKP" />
          <AssociationSet Name="FK_tm_jurusan_tm_pendidikan" Association="HRDModel.FK_tm_jurusan_tm_pendidikan">
            <End Role="tm_pendidikan" EntitySet="tm_pendidikan" />
            <End Role="tm_jurusan" EntitySet="tm_jurusan" />
          </AssociationSet>
          <EntitySet Name="tm_jabatan" EntityType="HRDModel.tm_jabatan" />
          <EntitySet Name="tm_namabank" EntityType="HRDModel.tm_namabank" />
          <EntitySet Name="tr_ijin" EntityType="HRDModel.tr_ijin" />
          <EntitySet Name="tr_gaji" EntityType="HRDModel.tr_gaji" />
          <EntitySet Name="tm_status_perkawinan" EntityType="HRDModel.tm_status_perkawinan" />
          <EntitySet Name="tm_ijin_master" EntityType="HRDModel.tm_ijin_master" />
          <AssociationSet Name="FK_tr_ijin_tm_ijin_master" Association="HRDModel.FK_tr_ijin_tm_ijin_master">
            <End Role="tm_ijin_master" EntitySet="tm_ijin_master" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <EntitySet Name="tr_register_pinjaman" EntityType="HRDModel.tr_register_pinjaman" />
          <EntitySet Name="tm_shift_absensi" EntityType="HRDModel.tm_shift_absensi" />
          <EntitySet Name="tm_off_karyawan" EntityType="HRDModel.tm_off_karyawan" />
          <EntitySet Name="tm_off_karyawan_line" EntityType="HRDModel.tm_off_karyawan_line" />
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_off_karyawan" Association="HRDModel.FK_tm_off_karyawan_line_tm_off_karyawan">
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_jumat" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_jumat">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_kamis" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_kamis">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_minggu" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_minggu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_rabu" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_rabu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_sabtu" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_sabtu">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_selasa" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_selasa">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_shift_absensi_senin" Association="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_senin">
            <End Role="tm_shift_absensi" EntitySet="tm_shift_absensi" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <EntitySet Name="tm_ter_pajak" EntityType="HRDModel.tm_ter_pajak" />
          <EntitySet Name="tm_event_setting_line" EntityType="HRDModel.tm_event_setting_line" />
          <EntitySet Name="tm_event_setting" EntityType="HRDModel.tm_event_setting" />
          <AssociationSet Name="FK_tm_event_setting_line_tm_event_setting" Association="HRDModel.FK_tm_event_setting_line_tm_event_setting">
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
            <End Role="tm_event_setting_line" EntitySet="tm_event_setting_line" />
          </AssociationSet>
          <EntitySet Name="tm_user_area" EntityType="HRDModel.tm_user_area" />
          <AssociationSet Name="FK_tm_user_area_tm_user" Association="HRDModel.FK_tm_user_area_tm_user">
            <End Role="tm_user" EntitySet="tm_user" />
            <End Role="tm_user_area" EntitySet="tm_user_area" />
          </AssociationSet>
          <EntitySet Name="tr_pinjaman_bayar" EntityType="HRDModel.tr_pinjaman_bayar" />
          <EntitySet Name="tr_pinjaman_bayar_line" EntityType="HRDModel.tr_pinjaman_bayar_line" />
          <AssociationSet Name="FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar" Association="HRDModel.FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar">
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_line_tr_register_pinjaman" Association="HRDModel.FK_tr_pinjaman_bayar_line_tr_register_pinjaman">
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
          </AssociationSet>
          <EntitySet Name="tr_pinjaman_move" EntityType="HRDModel.tr_pinjaman_move" />
          <AssociationSet Name="FK_tr_pinjaman_move_tr_pinjaman_bayar_line" Association="HRDModel.FK_tr_pinjaman_move_tr_pinjaman_bayar_line">
            <End Role="tr_pinjaman_bayar_line" EntitySet="tr_pinjaman_bayar_line" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_move_tr_register_pinjaman" Association="HRDModel.FK_tr_pinjaman_move_tr_register_pinjaman">
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <EntitySet Name="tm_libur_nasional" EntityType="HRDModel.tm_libur_nasional" />
          <EntitySet Name="tr_absensi" EntityType="HRDModel.tr_absensi" />
          <AssociationSet Name="FK_tr_absensi_tm_libur_nasional" Association="HRDModel.FK_tr_absensi_tm_libur_nasional">
            <End Role="tm_libur_nasional" EntitySet="tm_libur_nasional" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tr_ijin" Association="HRDModel.FK_tr_absensi_tr_ijin">
            <End Role="tr_ijin" EntitySet="tr_ijin" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <EntitySet Name="tr_gaji_line" EntityType="HRDModel.tr_gaji_line" />
          <AssociationSet Name="FK_tr_gaji_line_tm_status_PTKP" Association="HRDModel.FK_tr_gaji_line_tm_status_PTKP">
            <End Role="tm_status_PTKP" EntitySet="tm_status_PTKP" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_ter_pajak" Association="HRDModel.FK_tr_gaji_line_tm_ter_pajak">
            <End Role="tm_ter_pajak" EntitySet="tm_ter_pajak" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tr_gaji" Association="HRDModel.FK_tr_gaji_line_tr_gaji">
            <End Role="tr_gaji" EntitySet="tr_gaji" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_move_tr_gaji_line" Association="HRDModel.FK_tr_pinjaman_move_tr_gaji_line">
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
            <End Role="tr_pinjaman_move" EntitySet="tr_pinjaman_move" />
          </AssociationSet>
          <EntitySet Name="tm_absensi_setting" EntityType="HRDModel.tm_absensi_setting" />
          <EntitySet Name="tm_agama" EntityType="HRDModel.tm_agama" />
          <EntitySet Name="tm_cabang" EntityType="HRDModel.tm_cabang" />
          <EntitySet Name="tm_department" EntityType="HRDModel.tm_department" />
          <EntitySet Name="tr_spk_lembur_line" EntityType="HRDModel.tr_spk_lembur_line" />
          <EntitySet Name="tr_thr" EntityType="HRDModel.tr_thr" />
          <EntitySet Name="tr_tunjangan_onetime" EntityType="HRDModel.tr_tunjangan_onetime" />
          <EntitySet Name="vw_KaryawanAkanHabisKontrak" EntityType="HRDModel.vw_KaryawanAkanHabisKontrak" />
          <AssociationSet Name="FK_tm_absensi_setting_tm_cabang" Association="HRDModel.FK_tm_absensi_setting_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_absensi_setting" EntitySet="tm_absensi_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUser_Cabang" Association="HRDModel.FK_SiteUser_Cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_tm_cabang" Association="HRDModel.FK_tm_event_setting_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_tm_cabang" Association="HRDModel.FK_tm_off_karyawan_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_cabang" Association="HRDModel.FK_tr_absensi_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_tm_cabang" Association="HRDModel.FK_tr_gaji_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_gaji" EntitySet="tr_gaji" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_cabang" Association="HRDModel.FK_tr_ijin_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_cabang" Association="HRDModel.FK_tr_pinjaman_bayar_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_cabang" Association="HRDModel.FK_tr_register_pinjaman_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_tm_cabang" Association="HRDModel.FK_tr_thr_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_thr" EntitySet="tr_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_cabang" Association="HRDModel.FK_tr_tunjangan_onetime_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tr_spk_lembur_line" Association="HRDModel.FK_tr_absensi_tr_spk_lembur_line">
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <EntitySet Name="tr_kompensasi_kontrak" EntityType="HRDModel.tr_kompensasi_kontrak" />
          <EntitySet Name="tr_kompensasi_kontrak_line" EntityType="HRDModel.tr_kompensasi_kontrak_line" />
          <AssociationSet Name="FK_tr_kompensasi_kontrak_tm_cabang" Association="HRDModel.FK_tr_kompensasi_kontrak_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak" Association="HRDModel.FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak">
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
            <End Role="tr_kompensasi_kontrak_line" EntitySet="tr_kompensasi_kontrak_line" />
          </AssociationSet>
          <EntitySet Name="tm_karyawan" EntityType="HRDModel.tm_karyawan" />
          <EntitySet Name="tm_karyawan_datadinas" EntityType="HRDModel.tm_karyawan_datadinas" />
          <AssociationSet Name="FK_tm_karyawan_tm_agama" Association="HRDModel.FK_tm_karyawan_tm_agama">
            <End Role="tm_agama" EntitySet="tm_agama" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_cabang" Association="HRDModel.FK_tm_karyawan_datadinas_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_cabang" Association="HRDModel.FK_tm_karyawan_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_department" Association="HRDModel.FK_tm_karyawan_datadinas_tm_department">
            <End Role="tm_department" EntitySet="tm_department" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_line_tm_karyawan" Association="HRDModel.FK_tm_event_setting_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_event_setting_line" EntitySet="tm_event_setting_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_jabatan" Association="HRDModel.FK_tm_karyawan_datadinas_tm_jabatan">
            <End Role="tm_jabatan" EntitySet="tm_jabatan" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_jenis_kelamin" Association="HRDModel.FK_tm_karyawan_tm_jenis_kelamin">
            <End Role="tm_jenis_kelamin" EntitySet="tm_jenis_kelamin" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_jurusan" Association="HRDModel.FK_tm_karyawan_tm_jurusan">
            <End Role="tm_jurusan" EntitySet="tm_jurusan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_karyawan" Association="HRDModel.FK_tm_karyawan_datadinas_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_namabank" Association="HRDModel.FK_tm_karyawan_tm_namabank">
            <End Role="tm_namabank" EntitySet="tm_namabank" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_pendidikan" Association="HRDModel.FK_tm_karyawan_tm_pendidikan">
            <End Role="tm_pendidikan" EntitySet="tm_pendidikan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_status_perkawinan" Association="HRDModel.FK_tm_karyawan_tm_status_perkawinan">
            <End Role="tm_status_perkawinan" EntitySet="tm_status_perkawinan" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_status_PTKP" Association="HRDModel.FK_tm_karyawan_tm_status_PTKP">
            <End Role="tm_status_PTKP" EntitySet="tm_status_PTKP" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_line_tm_karyawan" Association="HRDModel.FK_tm_off_karyawan_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_off_karyawan_line" EntitySet="tm_off_karyawan_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_karyawan" Association="HRDModel.FK_tr_absensi_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_karyawan" Association="HRDModel.FK_tr_gaji_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_karyawan" Association="HRDModel.FK_tr_ijin_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_line_tm_karyawan" Association="HRDModel.FK_tr_kompensasi_kontrak_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_kompensasi_kontrak_line" EntitySet="tr_kompensasi_kontrak_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_karyawan" Association="HRDModel.FK_tr_pinjaman_bayar_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_karyawan" Association="HRDModel.FK_tr_register_pinjaman_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_line_tm_karyawan" Association="HRDModel.FK_tr_spk_lembur_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_karyawan" Association="HRDModel.FK_tr_tunjangan_onetime_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_line_tm_karyawan_datadinas" Association="HRDModel.FK_tr_gaji_line_tm_karyawan_datadinas">
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
            <End Role="tr_gaji_line" EntitySet="tr_gaji_line" />
          </AssociationSet>
          <EntitySet Name="tm_area" EntityType="HRDModel.tm_area" />
          <AssociationSet Name="FK_tm_absensi_setting_tm_area" Association="HRDModel.FK_tm_absensi_setting_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_absensi_setting" EntitySet="tm_absensi_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_Cabang_Area" Association="HRDModel.FK_Cabang_Area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_cabang" EntitySet="tm_cabang" />
          </AssociationSet>
          <AssociationSet Name="FK_SiteUser_Area" Association="HRDModel.FK_SiteUser_Area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_event_setting_tm_area" Association="HRDModel.FK_tm_event_setting_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_event_setting" EntitySet="tm_event_setting" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_datadinas_tm_area" Association="HRDModel.FK_tm_karyawan_datadinas_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_karyawan_tm_area" Association="HRDModel.FK_tm_karyawan_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_libur_nasional_tm_area" Association="HRDModel.FK_tm_libur_nasional_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_libur_nasional" EntitySet="tm_libur_nasional" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_off_karyawan_tm_area" Association="HRDModel.FK_tm_off_karyawan_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_off_karyawan" EntitySet="tm_off_karyawan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_user_area_tm_area" Association="HRDModel.FK_tm_user_area_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_user_area" EntitySet="tm_user_area" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_absensi_tm_area" Association="HRDModel.FK_tr_absensi_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_absensi" EntitySet="tr_absensi" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_gaji_tm_area" Association="HRDModel.FK_tr_gaji_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_gaji" EntitySet="tr_gaji" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_ijin_tm_area" Association="HRDModel.FK_tr_ijin_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_ijin" EntitySet="tr_ijin" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kompensasi_kontrak_tm_area" Association="HRDModel.FK_tr_kompensasi_kontrak_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_kompensasi_kontrak" EntitySet="tr_kompensasi_kontrak" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_pinjaman_bayar_tm_area" Association="HRDModel.FK_tr_pinjaman_bayar_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_pinjaman_bayar" EntitySet="tr_pinjaman_bayar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_register_pinjaman_tm_area" Association="HRDModel.FK_tr_register_pinjaman_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_register_pinjaman" EntitySet="tr_register_pinjaman" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_tm_area" Association="HRDModel.FK_tr_thr_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_thr" EntitySet="tr_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_tunjangan_onetime_tm_area" Association="HRDModel.FK_tr_tunjangan_onetime_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_tunjangan_onetime" EntitySet="tr_tunjangan_onetime" />
          </AssociationSet>
          <EntitySet Name="tm_bonus_thr" EntityType="HRDModel.tm_bonus_thr" />
          <AssociationSet Name="FK_tm_bonus_thr_tm_area" Association="HRDModel.FK_tm_bonus_thr_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_bonus_thr_tm_cabang" Association="HRDModel.FK_tm_bonus_thr_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_bonus_thr_tm_karyawan" Association="HRDModel.FK_tm_bonus_thr_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
          </AssociationSet>
          <EntitySet Name="tr_thr_line" EntityType="HRDModel.tr_thr_line" />
          <AssociationSet Name="FK_tr_thr_line_tm_bonus_thr" Association="HRDModel.FK_tr_thr_line_tm_bonus_thr">
            <End Role="tm_bonus_thr" EntitySet="tm_bonus_thr" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tm_karyawan" Association="HRDModel.FK_tr_thr_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tm_karyawan_datadinas" Association="HRDModel.FK_tr_thr_line_tm_karyawan_datadinas">
            <End Role="tm_karyawan_datadinas" EntitySet="tm_karyawan_datadinas" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_thr_line_tr_thr" Association="HRDModel.FK_tr_thr_line_tr_thr">
            <End Role="tr_thr" EntitySet="tr_thr" />
            <End Role="tr_thr_line" EntitySet="tr_thr_line" />
          </AssociationSet>
          <EntitySet Name="tr_spk_lembur" EntityType="HRDModel.tr_spk_lembur" />
          <AssociationSet Name="FK_tr_spk_lembur_tm_area" Association="HRDModel.FK_tr_spk_lembur_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_tm_cabang" Association="HRDModel.FK_tr_spk_lembur_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_spk_lembur_line_tr_spk_lembur" Association="HRDModel.FK_tr_spk_lembur_line_tr_spk_lembur">
            <End Role="tr_spk_lembur" EntitySet="tr_spk_lembur" />
            <End Role="tr_spk_lembur_line" EntitySet="tr_spk_lembur_line" />
          </AssociationSet>
          <EntitySet Name="tr_cuti_besar" EntityType="HRDModel.tr_cuti_besar" />
          <EntitySet Name="tr_cuti_besar_line" EntityType="HRDModel.tr_cuti_besar_line" />
          <AssociationSet Name="FK_tr_cuti_besar_tm_area" Association="HRDModel.FK_tr_cuti_besar_tm_area">
            <End Role="tm_area" EntitySet="tm_area" />
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_tm_cabang" Association="HRDModel.FK_tr_cuti_besar_tm_cabang">
            <End Role="tm_cabang" EntitySet="tm_cabang" />
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_line_tm_karyawan" Association="HRDModel.FK_tr_cuti_besar_line_tm_karyawan">
            <End Role="tm_karyawan" EntitySet="tm_karyawan" />
            <End Role="tr_cuti_besar_line" EntitySet="tr_cuti_besar_line" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_cuti_besar_line_tr_cuti_besar" Association="HRDModel.FK_tr_cuti_besar_line_tr_cuti_besar">
            <End Role="tr_cuti_besar" EntitySet="tr_cuti_besar" />
            <End Role="tr_cuti_besar_line" EntitySet="tr_cuti_besar_line" />
          </AssociationSet>
          </EntityContainer>
        <EntityType Name="tm_menu_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SiteMenu_id" Type="Int32" Nullable="false" />
          <Property Name="Role_id" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_role" Relationship="HRDModel.FK_SiteMenuRole_SiteRole" FromRole="tm_menu_role" ToRole="tm_role" />
          <NavigationProperty Name="tm_menu" Relationship="HRDModel.FK_SiteMenuRole_SiteMenu" FromRole="tm_menu_role" ToRole="tm_menu" />
        </EntityType>
        <EntityType Name="tm_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RoleName" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_menu_role" Relationship="HRDModel.FK_SiteMenuRole_SiteRole" FromRole="tm_role" ToRole="tm_menu_role" />
          <NavigationProperty Name="tm_user_role" Relationship="HRDModel.FK_SiteUserRole_SiteRole" FromRole="tm_role" ToRole="tm_user_role" />
        </EntityType>
        <EntityType Name="tm_user">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Username" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Password" Type="String" MaxLength="200" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="isApproved" Type="Boolean" Nullable="false" />
          <Property Name="isLockedOut" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Area_id" Type="Int32" />
          <Property Name="Cabang_id" Type="Int32" />
          <NavigationProperty Name="tm_user_role" Relationship="HRDModel.FK_SiteUserRole_SiteUser" FromRole="tm_user" ToRole="tm_user_role" />
          <Property Name="PermisionArea" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_user_area" Relationship="HRDModel.FK_tm_user_area_tm_user" FromRole="tm_user" ToRole="tm_user_area" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_SiteUser_Cabang" FromRole="tm_user" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_SiteUser_Area" FromRole="tm_user" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tm_user_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="User_id" Type="Int32" Nullable="false" />
          <Property Name="Role_id" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_role" Relationship="HRDModel.FK_SiteUserRole_SiteRole" FromRole="tm_user_role" ToRole="tm_role" />
          <NavigationProperty Name="tm_user" Relationship="HRDModel.FK_SiteUserRole_SiteUser" FromRole="tm_user_role" ToRole="tm_user" />
        </EntityType>
        <Association Name="FK_SiteMenuRole_SiteRole">
          <End Type="HRDModel.tm_role" Role="tm_role" Multiplicity="1" />
          <End Type="HRDModel.tm_menu_role" Role="tm_menu_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_menu_role">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUserRole_SiteRole">
          <End Type="HRDModel.tm_role" Role="tm_role" Multiplicity="1" />
          <End Type="HRDModel.tm_user_role" Role="tm_user_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_role">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUserRole_SiteUser">
          <End Type="HRDModel.tm_user" Role="tm_user" Multiplicity="1" />
          <End Type="HRDModel.tm_user_role" Role="tm_user_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_user">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_role">
              <PropertyRef Name="User_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_menu">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="Title" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Url" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Parent" Type="Int32" />
          <Property Name="BeginGroup" Type="Boolean" />
          <NavigationProperty Name="tm_menu_role" Relationship="HRDModel.FK_SiteMenuRole_SiteMenu" FromRole="tm_menu" ToRole="tm_menu_role" />
        </EntityType>
        <Association Name="FK_SiteMenuRole_SiteMenu">
          <End Type="HRDModel.tm_menu" Role="tm_menu" Multiplicity="1" />
          <End Type="HRDModel.tm_menu_role" Role="tm_menu_role" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_menu">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_menu_role">
              <PropertyRef Name="SiteMenu_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_jenis_kelamin">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="JenisKelamin" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_jenis_kelamin" FromRole="tm_jenis_kelamin" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_jurusan">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Pendidikan_id" Type="Int32" Nullable="false" />
          <Property Name="KodeJurusan" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaJurusan" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Createdby" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_pendidikan" Relationship="HRDModel.FK_tm_jurusan_tm_pendidikan" FromRole="tm_jurusan" ToRole="tm_pendidikan" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_jurusan" FromRole="tm_jurusan" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_pendidikan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodePendidikan" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="NamaPendidikan" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Createdby" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_jurusan" Relationship="HRDModel.FK_tm_jurusan_tm_pendidikan" FromRole="tm_pendidikan" ToRole="tm_jurusan" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_pendidikan" FromRole="tm_pendidikan" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_status_PTKP">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeStatus" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="StatusPernikahan" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PTKP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_gaji_line_tm_status_PTKP" FromRole="tm_status_PTKP" ToRole="tr_gaji_line" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_status_PTKP" FromRole="tm_status_PTKP" ToRole="tm_karyawan" />
        </EntityType>
        <Association Name="FK_tm_jurusan_tm_pendidikan">
          <End Type="HRDModel.tm_pendidikan" Role="tm_pendidikan" Multiplicity="1" />
          <End Type="HRDModel.tm_jurusan" Role="tm_jurusan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_pendidikan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_jurusan">
              <PropertyRef Name="Pendidikan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_jabatan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Jabatan" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="JumlahCutiTahunan" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_jabatan" FromRole="tm_jabatan" ToRole="tm_karyawan_datadinas" />
        </EntityType>
        <EntityType Name="tm_namabank">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeBank" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaBank" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="KodeKliring" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_namabank" FromRole="tm_namabank" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tr_ijin">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="Ijin_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_ijin_master" Relationship="HRDModel.FK_tr_ijin_tm_ijin_master" FromRole="tr_ijin" ToRole="tm_ijin_master" />
          <Property Name="Photo" Type="Binary" MaxLength="Max" FixedLength="false" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tr_ijin" FromRole="tr_ijin" ToRole="tr_absensi" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_ijin_tm_cabang" FromRole="tr_ijin" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_ijin_tm_karyawan" FromRole="tr_ijin" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_ijin_tm_area" FromRole="tr_ijin" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tr_gaji">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="Tahun" Type="String" Nullable="false" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="Bulan" Type="String" Nullable="false" MaxLength="2" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="TotalPendapatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalPotongan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalNet" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Bayar" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="KurangBayar" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="NoVoucher" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_gaji_line_tr_gaji" FromRole="tr_gaji" ToRole="tr_gaji_line" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_gaji_tm_cabang" FromRole="tr_gaji" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_gaji_tm_area" FromRole="tr_gaji" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tm_status_perkawinan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeStatus" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="StatusPernikahan" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_status_perkawinan" FromRole="tm_status_perkawinan" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_ijin_master">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeIjin" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="NamaIjin" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsCuti" Type="Boolean" Nullable="false" />
          <Property Name="Max" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tr_ijin" Relationship="HRDModel.FK_tr_ijin_tm_ijin_master" FromRole="tm_ijin_master" ToRole="tr_ijin" />
          <Property Name="PotongGaji" Type="Boolean" Nullable="false" />
          <Property Name="PotongCutiTahunan" Type="Boolean" Nullable="false" />
          <Property Name="IsMasuk" Type="Boolean" Nullable="false" />
        </EntityType>
        <Association Name="FK_tr_ijin_tm_ijin_master">
          <End Type="HRDModel.tm_ijin_master" Role="tm_ijin_master" Multiplicity="1" />
          <End Type="HRDModel.tr_ijin" Role="tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_ijin_master">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Ijin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_register_pinjaman">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="KodePinjaman" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Memo" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="TotalBayar" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Saldo" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CicilanPerBulam" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="NoVoucher" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Status" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="Version" Type="Binary" Nullable="false" MaxLength="8" FixedLength="true" annotation:StoreGeneratedPattern="Computed" ConcurrencyMode="Fixed" />
          <NavigationProperty Name="tr_pinjaman_bayar_line" Relationship="HRDModel.FK_tr_pinjaman_bayar_line_tr_register_pinjaman" FromRole="tr_register_pinjaman" ToRole="tr_pinjaman_bayar_line" />
          <NavigationProperty Name="tr_pinjaman_move" Relationship="HRDModel.FK_tr_pinjaman_move_tr_register_pinjaman" FromRole="tr_register_pinjaman" ToRole="tr_pinjaman_move" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_register_pinjaman_tm_cabang" FromRole="tr_register_pinjaman" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_register_pinjaman_tm_karyawan" FromRole="tr_register_pinjaman" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_register_pinjaman_tm_area" FromRole="tr_register_pinjaman" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tm_shift_absensi">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="IsOff" Type="Boolean" Nullable="false" />
          <Property Name="StartTime" Type="Time" Precision="0" />
          <Property Name="EndTime" Type="Time" Precision="7" />
          <Property Name="Createdby" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Deskripsi" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_off_karyawan_line" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_jumat" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line1" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_kamis" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line2" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_minggu" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line3" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_rabu" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line4" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_sabtu" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line5" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_selasa" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tm_off_karyawan_line6" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_senin" FromRole="tm_shift_absensi" ToRole="tm_off_karyawan_line" />
          </EntityType>
        <EntityType Name="tm_off_karyawan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="TglBerlaku" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Createdby" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Version" Type="Binary" Nullable="false" MaxLength="8" FixedLength="true" annotation:StoreGeneratedPattern="Computed" />
          <NavigationProperty Name="tm_off_karyawan_line" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_off_karyawan" FromRole="tm_off_karyawan" ToRole="tm_off_karyawan_line" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_off_karyawan_tm_cabang" FromRole="tm_off_karyawan" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_off_karyawan_tm_area" FromRole="tm_off_karyawan" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tm_off_karyawan_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Off_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Minggu" Type="Int32" />
          <Property Name="Senin" Type="Int32" />
          <Property Name="Selasa" Type="Int32" />
          <Property Name="Rabu" Type="Int32" />
          <Property Name="Kamis" Type="Int32" />
          <Property Name="Jumat" Type="Int32" />
          <Property Name="Sabtu" Type="Int32" />
          <NavigationProperty Name="tm_off_karyawan" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_off_karyawan" FromRole="tm_off_karyawan_line" ToRole="tm_off_karyawan" />
          <NavigationProperty Name="tm_shift_absensi_jumat" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_jumat" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_kamis" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_kamis" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_minggu" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_minggu" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_rabu" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_rabu" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_sabtu" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_sabtu" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_selasa" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_selasa" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_shift_absensi_senin" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_shift_absensi_senin" FromRole="tm_off_karyawan_line" ToRole="tm_shift_absensi" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_karyawan" FromRole="tm_off_karyawan_line" ToRole="tm_karyawan" />
        </EntityType>
        <Association Name="FK_tm_off_karyawan_line_tm_off_karyawan">
          <End Type="HRDModel.tm_off_karyawan" Role="tm_off_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_off_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Off_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_jumat">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Jumat" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_kamis">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Kamis" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_minggu">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Minggu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_rabu">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Rabu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_sabtu">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Sabtu" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_selasa">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Selasa" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_shift_absensi_senin">
          <End Type="HRDModel.tm_shift_absensi" Role="tm_shift_absensi" Multiplicity="0..1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_shift_absensi">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Senin" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_ter_pajak">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Kode" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="Mulai" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Sampai" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TarifPercent" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_gaji_line_tm_ter_pajak" FromRole="tm_ter_pajak" ToRole="tr_gaji_line" />
        </EntityType>
        <EntityType Name="tm_event_setting_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Event_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_event_setting" Relationship="HRDModel.FK_tm_event_setting_line_tm_event_setting" FromRole="tm_event_setting_line" ToRole="tm_event_setting" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_event_setting_line_tm_karyawan" FromRole="tm_event_setting_line" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_event_setting">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_Id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="MsMasuk" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="MsKeluar" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="NamaEvent" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_event_setting_line" Relationship="HRDModel.FK_tm_event_setting_line_tm_event_setting" FromRole="tm_event_setting" ToRole="tm_event_setting_line" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_event_setting_tm_cabang" FromRole="tm_event_setting" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_event_setting_tm_area" FromRole="tm_event_setting" ToRole="tm_area" />
        </EntityType>
        <Association Name="FK_tm_event_setting_line_tm_event_setting">
          <End Type="HRDModel.tm_event_setting" Role="tm_event_setting" Multiplicity="1" />
          <End Type="HRDModel.tm_event_setting_line" Role="tm_event_setting_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_event_setting">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting_line">
              <PropertyRef Name="Event_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_user_area">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="User_id" Type="Int32" Nullable="false" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_user" Relationship="HRDModel.FK_tm_user_area_tm_user" FromRole="tm_user_area" ToRole="tm_user" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_user_area_tm_area" FromRole="tm_user_area" ToRole="tm_area" />
        </EntityType>
        <Association Name="FK_tm_user_area_tm_user">
          <End Type="HRDModel.tm_user" Role="tm_user" Multiplicity="1" />
          <End Type="HRDModel.tm_user_area" Role="tm_user_area" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_user">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_area">
              <PropertyRef Name="User_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_pinjaman_bayar">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="KodeBayar" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Memo" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tr_pinjaman_bayar_line" Relationship="HRDModel.FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar" FromRole="tr_pinjaman_bayar" ToRole="tr_pinjaman_bayar_line" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_cabang" FromRole="tr_pinjaman_bayar" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_karyawan" FromRole="tr_pinjaman_bayar" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_area" FromRole="tr_pinjaman_bayar" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tr_pinjaman_bayar_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Bayar_id" Type="Int64" Nullable="false" />
          <Property Name="Pinjaman_id" Type="Int64" Nullable="false" />
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tr_pinjaman_bayar" Relationship="HRDModel.FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar" FromRole="tr_pinjaman_bayar_line" ToRole="tr_pinjaman_bayar" />
          <NavigationProperty Name="tr_register_pinjaman" Relationship="HRDModel.FK_tr_pinjaman_bayar_line_tr_register_pinjaman" FromRole="tr_pinjaman_bayar_line" ToRole="tr_register_pinjaman" />
          <NavigationProperty Name="tr_pinjaman_move" Relationship="HRDModel.FK_tr_pinjaman_move_tr_pinjaman_bayar_line" FromRole="tr_pinjaman_bayar_line" ToRole="tr_pinjaman_move" />
        </EntityType>
        <Association Name="FK_tr_pinjaman_bayar_line_tr_pinjaman_bayar">
          <End Type="HRDModel.tr_pinjaman_bayar" Role="tr_pinjaman_bayar" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_bayar_line" Role="tr_pinjaman_bayar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_pinjaman_bayar">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Bayar_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_line_tr_register_pinjaman">
          <End Type="HRDModel.tr_register_pinjaman" Role="tr_register_pinjaman" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_bayar_line" Role="tr_pinjaman_bayar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_register_pinjaman">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Pinjaman_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_pinjaman_move">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Pinjaman_id" Type="Int64" Nullable="false" />
          <Property Name="TipeTrans" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="TransDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Memo" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="PinjBayarLine_id" Type="Int64" />
          <Property Name="GajiLine_id" Type="Int64" />
          <NavigationProperty Name="tr_pinjaman_bayar_line" Relationship="HRDModel.FK_tr_pinjaman_move_tr_pinjaman_bayar_line" FromRole="tr_pinjaman_move" ToRole="tr_pinjaman_bayar_line" />
          <NavigationProperty Name="tr_register_pinjaman" Relationship="HRDModel.FK_tr_pinjaman_move_tr_register_pinjaman" FromRole="tr_pinjaman_move" ToRole="tr_register_pinjaman" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_pinjaman_move_tr_gaji_line" FromRole="tr_pinjaman_move" ToRole="tr_gaji_line" />
        </EntityType>
        <Association Name="FK_tr_pinjaman_move_tr_pinjaman_bayar_line">
          <End Type="HRDModel.tr_pinjaman_bayar_line" Role="tr_pinjaman_bayar_line" Multiplicity="0..1" />
          <End Type="HRDModel.tr_pinjaman_move" Role="tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_pinjaman_bayar_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="PinjBayarLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_move_tr_register_pinjaman">
          <End Type="HRDModel.tr_register_pinjaman" Role="tr_register_pinjaman" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_move" Role="tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_register_pinjaman">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="Pinjaman_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_libur_nasional">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NamaHariLibur" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Version" Type="Binary" Nullable="false" MaxLength="8" FixedLength="true" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="CutiBersama" Type="Boolean" Nullable="false" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tm_libur_nasional" FromRole="tm_libur_nasional" ToRole="tr_absensi" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_libur_nasional_tm_area" FromRole="tm_libur_nasional" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tr_absensi">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="MsJamMasuk" Type="DateTime" Precision="3" />
          <Property Name="JamMasuk" Type="DateTime" Precision="3" />
          <Property Name="MsJamKeluar" Type="DateTime" Precision="3" />
          <Property Name="JamKeluar" Type="DateTime" Precision="3" />
          <Property Name="Terlambat" Type="Int32" Nullable="false" />
          <Property Name="Overtime" Type="Int32" Nullable="false" />
          <Property Name="OverTime_HLibur" Type="Int32" Nullable="false" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Alpha_b" Type="Boolean" Nullable="false" />
          <Property Name="Cuti_b" Type="Boolean" Nullable="false" />
          <Property Name="Ijin_b" Type="Boolean" Nullable="false" />
          <Property Name="PulangCepat" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HKerja_1" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HKerja_2" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HKerja_3" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HLibur_1_7" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HLibur_8" Type="Int32" Nullable="false" />
          <Property Name="Lembur_HLibur_9_10" Type="Int32" Nullable="false" />
          <Property Name="Masuk_b" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Ijin_id" Type="Int32" />
          <Property Name="Off_b" Type="Boolean" Nullable="false" />
          <Property Name="PotongGaji" Type="Boolean" Nullable="false" />
          <Property Name="LemburLine_id" Type="Int32" />
          <Property Name="FromAbsensi" Type="Boolean" Nullable="false" />
          <Property Name="LiburNasional" Type="Boolean" Nullable="false" />
          <Property Name="CutiBersama" Type="Boolean" Nullable="false" />
          <Property Name="LiburNasional_id" Type="Int32" />
          <Property Name="GantiHari" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_libur_nasional" Relationship="HRDModel.FK_tr_absensi_tm_libur_nasional" FromRole="tr_absensi" ToRole="tm_libur_nasional" />
          <NavigationProperty Name="tr_ijin" Relationship="HRDModel.FK_tr_absensi_tr_ijin" FromRole="tr_absensi" ToRole="tr_ijin" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_absensi_tm_cabang" FromRole="tr_absensi" ToRole="tm_cabang" />
          <NavigationProperty Name="tr_spk_lembur_line" Relationship="HRDModel.FK_tr_absensi_tr_spk_lembur_line" FromRole="tr_absensi" ToRole="tr_spk_lembur_line" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_absensi_tm_karyawan" FromRole="tr_absensi" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_absensi_tm_area" FromRole="tr_absensi" ToRole="tm_area" />
        </EntityType>
        <Association Name="FK_tr_absensi_tm_libur_nasional">
          <End Type="HRDModel.tm_libur_nasional" Role="tm_libur_nasional" Multiplicity="0..1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_libur_nasional">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="LiburNasional_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tr_ijin">
          <End Type="HRDModel.tr_ijin" Role="tr_ijin" Multiplicity="0..1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_ijin">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Ijin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_gaji_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Gaji_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Pd_GajiPokok" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Jabatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Transport" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Makan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_PremiHadir" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Susu" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Kontrak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Probation" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_PremiAss" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Pajak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JKK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JKM" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalPendapatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JKK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JKM" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JPK_Mandiri" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_PPH21" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_SP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_SerPekerja" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_Kasbon" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalPotongan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="THP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pot_Alpha" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="HariKerja" Type="Int32" Nullable="false" />
          <Property Name="JumlahAlpha" Type="Int32" Nullable="false" />
          <Property Name="JumlahIjin" Type="Int32" Nullable="false" />
          <Property Name="JumlahIjinDispensasi" Type="Int32" Nullable="false" />
          <Property Name="JumlahCuti" Type="Int32" Nullable="false" />
          <Property Name="Pd_Lembur" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="JumlahSakit" Type="Int32" Nullable="false" />
          <Property Name="JumlahLembur" Type="Int32" Nullable="false" />
          <Property Name="JumlahPotongGaji" Type="Int32" Nullable="false" />
          <Property Name="Pd_Insentif" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Lain" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="DataDinas_id" Type="Int32" />
          <Property Name="StatusPTKP_id" Type="Int32" />
          <Property Name="Ter_pajak_id" Type="Int32" />
          <Property Name="Ter_pajak_percent" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tm_status_PTKP" Relationship="HRDModel.FK_tr_gaji_line_tm_status_PTKP" FromRole="tr_gaji_line" ToRole="tm_status_PTKP" />
          <NavigationProperty Name="tm_ter_pajak" Relationship="HRDModel.FK_tr_gaji_line_tm_ter_pajak" FromRole="tr_gaji_line" ToRole="tm_ter_pajak" />
          <NavigationProperty Name="tr_gaji" Relationship="HRDModel.FK_tr_gaji_line_tr_gaji" FromRole="tr_gaji_line" ToRole="tr_gaji" />
          <NavigationProperty Name="tr_pinjaman_move" Relationship="HRDModel.FK_tr_pinjaman_move_tr_gaji_line" FromRole="tr_gaji_line" ToRole="tr_pinjaman_move" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_gaji_line_tm_karyawan" FromRole="tr_gaji_line" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tr_gaji_line_tm_karyawan_datadinas" FromRole="tr_gaji_line" ToRole="tm_karyawan_datadinas" />
        </EntityType>
        <Association Name="FK_tr_gaji_line_tm_status_PTKP">
          <End Type="HRDModel.tm_status_PTKP" Role="tm_status_PTKP" Multiplicity="0..1" />
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_PTKP">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="StatusPTKP_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_ter_pajak">
          <End Type="HRDModel.tm_ter_pajak" Role="tm_ter_pajak" Multiplicity="0..1" />
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_ter_pajak">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Ter_pajak_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tr_gaji">
          <End Type="HRDModel.tr_gaji" Role="tr_gaji" Multiplicity="1" />
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_gaji">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Gaji_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_move_tr_gaji_line">
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="0..1" />
          <End Type="HRDModel.tr_pinjaman_move" Role="tr_pinjaman_move" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_gaji_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_move">
              <PropertyRef Name="GajiLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_absensi_setting">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="JamMasuk" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="JamKeluar" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ToleransiJamMasuk" Type="Int32" Nullable="false" />
          <Property Name="ToleransiJamKeluar" Type="Int32" Nullable="false" />
          <Property Name="Shift" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="JamMasukJumat" Type="DateTime" Precision="3" />
          <Property Name="JamKeluarJumat" Type="DateTime" Precision="3" />
          <Property Name="JamMasukSabtu" Type="DateTime" Precision="3" />
          <Property Name="JamKeluarSabtu" Type="DateTime" Precision="3" />
          <Property Name="JamMasukMinggu" Type="DateTime" Precision="3" />
          <Property Name="JamKeluarMinggu" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_absensi_setting_tm_cabang" FromRole="tm_absensi_setting" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_absensi_setting_tm_area" FromRole="tm_absensi_setting" ToRole="tm_area" />
          <Property Name="HariKhusus" Type="Boolean" Nullable="false" />
          <Property Name="StartDate" Type="DateTime" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Precision="0" />
        </EntityType>
        <EntityType Name="tm_agama">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="Int32" Nullable="false" />
          <Property Name="agama" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_agama" FromRole="tm_agama" ToRole="tm_karyawan" />
        </EntityType>
        <EntityType Name="tm_cabang">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="KodeCabang" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaCabang" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsHeadOffice" Type="Boolean" Nullable="false" />
          <Property Name="Alamat" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Telp" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Fax" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="UpahMinimum" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="UpahMinimum_Kes" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="SabtuLibur" Type="Boolean" Nullable="false" />
          <Property Name="MingguLibur" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_absensi_setting" Relationship="HRDModel.FK_tm_absensi_setting_tm_cabang" FromRole="tm_cabang" ToRole="tm_absensi_setting" />
          <NavigationProperty Name="tm_user" Relationship="HRDModel.FK_SiteUser_Cabang" FromRole="tm_cabang" ToRole="tm_user" />
          <NavigationProperty Name="tm_event_setting" Relationship="HRDModel.FK_tm_event_setting_tm_cabang" FromRole="tm_cabang" ToRole="tm_event_setting" />
          <NavigationProperty Name="tm_off_karyawan" Relationship="HRDModel.FK_tm_off_karyawan_tm_cabang" FromRole="tm_cabang" ToRole="tm_off_karyawan" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tm_cabang" FromRole="tm_cabang" ToRole="tr_absensi" />
          <NavigationProperty Name="tr_gaji" Relationship="HRDModel.FK_tr_gaji_tm_cabang" FromRole="tm_cabang" ToRole="tr_gaji" />
          <NavigationProperty Name="tr_ijin" Relationship="HRDModel.FK_tr_ijin_tm_cabang" FromRole="tm_cabang" ToRole="tr_ijin" />
          <NavigationProperty Name="tr_pinjaman_bayar" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_cabang" FromRole="tm_cabang" ToRole="tr_pinjaman_bayar" />
          <NavigationProperty Name="tr_register_pinjaman" Relationship="HRDModel.FK_tr_register_pinjaman_tm_cabang" FromRole="tm_cabang" ToRole="tr_register_pinjaman" />
          <NavigationProperty Name="tr_thr" Relationship="HRDModel.FK_tr_thr_tm_cabang" FromRole="tm_cabang" ToRole="tr_thr" />
          <NavigationProperty Name="tr_tunjangan_onetime" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_cabang" FromRole="tm_cabang" ToRole="tr_tunjangan_onetime" />
          <NavigationProperty Name="tr_kompensasi_kontrak" Relationship="HRDModel.FK_tr_kompensasi_kontrak_tm_cabang" FromRole="tm_cabang" ToRole="tr_kompensasi_kontrak" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_cabang" FromRole="tm_cabang" ToRole="tm_karyawan_datadinas" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_cabang" FromRole="tm_cabang" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_Cabang_Area" FromRole="tm_cabang" ToRole="tm_area" />
          <NavigationProperty Name="tm_bonus_thr" Relationship="HRDModel.FK_tm_bonus_thr_tm_cabang" FromRole="tm_cabang" ToRole="tm_bonus_thr" />
          <NavigationProperty Name="tr_spk_lembur" Relationship="HRDModel.FK_tr_spk_lembur_tm_cabang" FromRole="tm_cabang" ToRole="tr_spk_lembur" />
          <NavigationProperty Name="tr_cuti_besar" Relationship="HRDModel.FK_tr_cuti_besar_tm_cabang" FromRole="tm_cabang" ToRole="tr_cuti_besar" />
        </EntityType>
        <EntityType Name="tm_department">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NamaDepartemen" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_department" FromRole="tm_department" ToRole="tm_karyawan_datadinas" />
        </EntityType>
        <EntityType Name="tr_spk_lembur_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SPKLembur_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tr_spk_lembur_line" FromRole="tr_spk_lembur_line" ToRole="tr_absensi" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_spk_lembur_line_tm_karyawan" FromRole="tr_spk_lembur_line" ToRole="tm_karyawan" />
          <NavigationProperty Name="tr_spk_lembur" Relationship="HRDModel.FK_tr_spk_lembur_line_tr_spk_lembur" FromRole="tr_spk_lembur_line" ToRole="tr_spk_lembur" />
        </EntityType>
        <EntityType Name="tr_thr">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="KodeTHR" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Periode" Type="String" Nullable="false" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="PeriodeDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Keterangan" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="isCompleted" Type="Boolean" Nullable="false" />
          <Property Name="TipeTHR" Type="Int32" Nullable="false" />
          <Property Name="GajiPokok_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Jabatan_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Transport_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Susu_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Makan_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Kontrak_b" Type="Boolean" Nullable="false" />
          <Property Name="T_Probation_b" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_thr_tm_cabang" FromRole="tr_thr" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_thr_tm_area" FromRole="tr_thr" ToRole="tm_area" />
          <NavigationProperty Name="tr_thr_line" Relationship="HRDModel.FK_tr_thr_line_tr_thr" FromRole="tr_thr" ToRole="tr_thr_line" />
        </EntityType>
        <EntityType Name="tr_tunjangan_onetime">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="PeriodeGaji" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Tahun" Type="String" Nullable="false" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="Bulan" Type="String" Nullable="false" MaxLength="2" FixedLength="false" Unicode="false" />
          <Property Name="TunjanganInsentif" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TunjanganLain" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_cabang" FromRole="tr_tunjangan_onetime" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_karyawan" FromRole="tr_tunjangan_onetime" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_area" FromRole="tr_tunjangan_onetime" ToRole="tm_area" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="vw_KaryawanAkanHabisKontrak">
          <Key>
            <PropertyRef Name="NIK" />
            <PropertyRef Name="Nama" />
            <PropertyRef Name="Area_id" />
            <PropertyRef Name="Cabang_id" />
            <PropertyRef Name="KodeArea" />
            <PropertyRef Name="NamaArea" />
            <PropertyRef Name="KodeCabang" />
            <PropertyRef Name="NamaCabang" />
          </Key>
          <Property Name="NIK" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Nama" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="TglAkhirKontrak" Type="DateTime" Precision="3" />
          <Property Name="Id" Type="Int32" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="KodeArea" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaArea" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="KodeCabang" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaCabang" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
        </EntityType>
        <Association Name="FK_tm_absensi_setting_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_absensi_setting" Role="tm_absensi_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_absensi_setting">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUser_Cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="0..1" />
          <End Type="HRDModel.tm_user" Role="tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_event_setting" Role="tm_event_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_off_karyawan" Role="tm_off_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_gaji" Role="tr_gaji" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_ijin" Role="tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_bayar" Role="tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_register_pinjaman" Role="tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_thr" Role="tr_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_tunjangan_onetime" Role="tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tr_spk_lembur_line">
          <End Type="HRDModel.tr_spk_lembur_line" Role="tr_spk_lembur_line" Multiplicity="0..1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_spk_lembur_line">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="LemburLine_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_kompensasi_kontrak">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="NoKompensasi" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Periode" Type="String" Nullable="false" MaxLength="7" FixedLength="false" Unicode="false" />
          <Property Name="PeriodeDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Status" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_kompensasi_kontrak_tm_cabang" FromRole="tr_kompensasi_kontrak" ToRole="tm_cabang" />
          <NavigationProperty Name="tr_kompensasi_kontrak_line" Relationship="HRDModel.FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak" FromRole="tr_kompensasi_kontrak" ToRole="tr_kompensasi_kontrak_line" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_kompensasi_kontrak_tm_area" FromRole="tr_kompensasi_kontrak" ToRole="tm_area" />
        </EntityType>
        <EntityType Name="tr_kompensasi_kontrak_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Kompensasi_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="TanggalAwalKontrak" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="TanggalAkhirKontrak" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="LamaKerja" Type="Int32" Nullable="false" />
          <Property Name="NilaiKompensasi" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tr_kompensasi_kontrak" Relationship="HRDModel.FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak" FromRole="tr_kompensasi_kontrak_line" ToRole="tr_kompensasi_kontrak" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_kompensasi_kontrak_line_tm_karyawan" FromRole="tr_kompensasi_kontrak_line" ToRole="tm_karyawan" />
        </EntityType>
        <Association Name="FK_tr_kompensasi_kontrak_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_kompensasi_kontrak" Role="tr_kompensasi_kontrak" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_line_tr_kompensasi_kontrak">
          <End Type="HRDModel.tr_kompensasi_kontrak" Role="tr_kompensasi_kontrak" Multiplicity="1" />
          <End Type="HRDModel.tr_kompensasi_kontrak_line" Role="tr_kompensasi_kontrak_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak_line">
              <PropertyRef Name="Kompensasi_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_karyawan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="NIK" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Nama" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Tempat" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="TglLahir" Type="DateTime" Precision="3" />
          <Property Name="Umur" Type="Int32" />
          <Property Name="JenisKelamin_id" Type="Int32" />
          <Property Name="Agama_id" Type="Int32" />
          <Property Name="Alamat" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="Kota" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Telp" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="StatusPerkawinan_id" Type="Int32" />
          <Property Name="Pendidikan_id" Type="Int32" />
          <Property Name="Jurusan_id" Type="Int32" />
          <Property Name="Alumni" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="TahunLulus" Type="String" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="TglMasuk" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Berhenti_b" Type="Boolean" Nullable="false" />
          <Property Name="Tgl_Berhenti" Type="DateTime" Precision="3" />
          <Property Name="AlasanBerhenti" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="NamaBank_id" Type="Int32" />
          <Property Name="NoRekening" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="NamaAccount" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="UserName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Password" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Photo" Type="Binary" MaxLength="Max" FixedLength="false" />
          <Property Name="Token" Type="Guid" />
          <Property Name="TokenExpired" Type="DateTime" Precision="3" />
          <Property Name="NoKTP" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Ship" Type="Int32" />
          <Property Name="Createdby" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Version" Type="Binary" Nullable="false" MaxLength="8" FixedLength="true" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="NPWP" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsBPJS_Kes" Type="Boolean" Nullable="false" />
          <Property Name="IsBPJS_TK" Type="Boolean" Nullable="false" />
          <Property Name="StatusPerkawinan_NonPTKP_id" Type="Int32" />
          <Property Name="KaryawanTetap" Type="Boolean" Nullable="false" />
          <Property Name="TipeAbsensiOFF" Type="Int16" Nullable="false" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="TglReloadCuti" Type="DateTime" Precision="0" />
          <NavigationProperty Name="tm_agama" Relationship="HRDModel.FK_tm_karyawan_tm_agama" FromRole="tm_karyawan" ToRole="tm_agama" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_karyawan_tm_cabang" FromRole="tm_karyawan" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_event_setting_line" Relationship="HRDModel.FK_tm_event_setting_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tm_event_setting_line" />
          <NavigationProperty Name="tm_jenis_kelamin" Relationship="HRDModel.FK_tm_karyawan_tm_jenis_kelamin" FromRole="tm_karyawan" ToRole="tm_jenis_kelamin" />
          <NavigationProperty Name="tm_jurusan" Relationship="HRDModel.FK_tm_karyawan_tm_jurusan" FromRole="tm_karyawan" ToRole="tm_jurusan" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_karyawan" FromRole="tm_karyawan" ToRole="tm_karyawan_datadinas" />
          <NavigationProperty Name="tm_namabank" Relationship="HRDModel.FK_tm_karyawan_tm_namabank" FromRole="tm_karyawan" ToRole="tm_namabank" />
          <NavigationProperty Name="tm_pendidikan" Relationship="HRDModel.FK_tm_karyawan_tm_pendidikan" FromRole="tm_karyawan" ToRole="tm_pendidikan" />
          <NavigationProperty Name="tm_status_perkawinan" Relationship="HRDModel.FK_tm_karyawan_tm_status_perkawinan" FromRole="tm_karyawan" ToRole="tm_status_perkawinan" />
          <NavigationProperty Name="tm_status_PTKP" Relationship="HRDModel.FK_tm_karyawan_tm_status_PTKP" FromRole="tm_karyawan" ToRole="tm_status_PTKP" />
          <NavigationProperty Name="tm_off_karyawan_line" Relationship="HRDModel.FK_tm_off_karyawan_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tm_off_karyawan_line" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_absensi" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_gaji_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_gaji_line" />
          <NavigationProperty Name="tr_ijin" Relationship="HRDModel.FK_tr_ijin_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_ijin" />
          <NavigationProperty Name="tr_kompensasi_kontrak_line" Relationship="HRDModel.FK_tr_kompensasi_kontrak_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_kompensasi_kontrak_line" />
          <NavigationProperty Name="tr_pinjaman_bayar" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_pinjaman_bayar" />
          <NavigationProperty Name="tr_register_pinjaman" Relationship="HRDModel.FK_tr_register_pinjaman_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_register_pinjaman" />
          <NavigationProperty Name="tr_spk_lembur_line" Relationship="HRDModel.FK_tr_spk_lembur_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_spk_lembur_line" />
          <NavigationProperty Name="tr_tunjangan_onetime" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_tunjangan_onetime" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_karyawan_tm_area" FromRole="tm_karyawan" ToRole="tm_area" />
          <NavigationProperty Name="tm_bonus_thr" Relationship="HRDModel.FK_tm_bonus_thr_tm_karyawan" FromRole="tm_karyawan" ToRole="tm_bonus_thr" />
          <NavigationProperty Name="tr_thr_line" Relationship="HRDModel.FK_tr_thr_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_thr_line" />
          <NavigationProperty Name="tr_cuti_besar_line" Relationship="HRDModel.FK_tr_cuti_besar_line_tm_karyawan" FromRole="tm_karyawan" ToRole="tr_cuti_besar_line" />
        </EntityType>
        <EntityType Name="tm_karyawan_datadinas">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Department_id" Type="Int32" />
          <Property Name="Jabatan_id" Type="Int32" />
          <Property Name="Tetap" Type="Boolean" Nullable="false" />
          <Property Name="TglTetap" Type="DateTime" Precision="3" />
          <Property Name="TglAwalKontrak" Type="DateTime" Precision="3" />
          <Property Name="TglAkhirKontrak" Type="DateTime" Precision="3" />
          <Property Name="Pd_GajiPokok" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Jabatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Makan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_PremiHadir" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Susu" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JKK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JKM" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalPendapatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JKK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JKM" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_P_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JHT" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JPK" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_K_JPK_Mandiri" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_PPH21" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_SP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TotalPotongan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="THP" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="NoKontrakKerja" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Pd_T_Transport" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Kontrak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Probation" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_PremiAss" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Pajak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_SerPekerja" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="NIK_Lama" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="SaldoAwalCuti" Type="Int32" Nullable="false" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_cabang" FromRole="tm_karyawan_datadinas" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_department" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_department" FromRole="tm_karyawan_datadinas" ToRole="tm_department" />
          <NavigationProperty Name="tm_jabatan" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_jabatan" FromRole="tm_karyawan_datadinas" ToRole="tm_jabatan" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_karyawan" FromRole="tm_karyawan_datadinas" ToRole="tm_karyawan" />
          <NavigationProperty Name="tr_gaji_line" Relationship="HRDModel.FK_tr_gaji_line_tm_karyawan_datadinas" FromRole="tm_karyawan_datadinas" ToRole="tr_gaji_line" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_area" FromRole="tm_karyawan_datadinas" ToRole="tm_area" />
          <NavigationProperty Name="tr_thr_line" Relationship="HRDModel.FK_tr_thr_line_tm_karyawan_datadinas" FromRole="tm_karyawan_datadinas" ToRole="tr_thr_line" />
        </EntityType>
        <Association Name="FK_tm_karyawan_tm_agama">
          <End Type="HRDModel.tm_agama" Role="tm_agama" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_agama">
              <PropertyRef Name="id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Agama_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_department">
          <End Type="HRDModel.tm_department" Role="tm_department" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_department">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Department_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tm_event_setting_line" Role="tm_event_setting_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_jabatan">
          <End Type="HRDModel.tm_jabatan" Role="tm_jabatan" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jabatan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Jabatan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_jenis_kelamin">
          <End Type="HRDModel.tm_jenis_kelamin" Role="tm_jenis_kelamin" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jenis_kelamin">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="JenisKelamin_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_jurusan">
          <End Type="HRDModel.tm_jurusan" Role="tm_jurusan" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_jurusan">
              <PropertyRef Name="id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Jurusan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_namabank">
          <End Type="HRDModel.tm_namabank" Role="tm_namabank" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_namabank">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="NamaBank_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_pendidikan">
          <End Type="HRDModel.tm_pendidikan" Role="tm_pendidikan" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_pendidikan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Pendidikan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_status_perkawinan">
          <End Type="HRDModel.tm_status_perkawinan" Role="tm_status_perkawinan" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_perkawinan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="StatusPerkawinan_NonPTKP_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_status_PTKP">
          <End Type="HRDModel.tm_status_PTKP" Role="tm_status_PTKP" Multiplicity="0..1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_status_PTKP">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="StatusPerkawinan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tm_off_karyawan_line" Role="tm_off_karyawan_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_ijin" Role="tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_kompensasi_kontrak_line" Role="tr_kompensasi_kontrak_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_bayar" Role="tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_register_pinjaman" Role="tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_spk_lembur_line" Role="tr_spk_lembur_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_tunjangan_onetime" Role="tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_line_tm_karyawan_datadinas">
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="0..1" />
          <End Type="HRDModel.tr_gaji_line" Role="tr_gaji_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan_datadinas">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji_line">
              <PropertyRef Name="DataDinas_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_area">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeArea" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaArea" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="KodeNIK_Tetap" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="KodeNIK_Kontrak" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="NoRekeningBank" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="TglCustOffCuti" Type="DateTime" Precision="0" />
          <Property Name="FlatRateBPJS" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_absensi_setting" Relationship="HRDModel.FK_tm_absensi_setting_tm_area" FromRole="tm_area" ToRole="tm_absensi_setting" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_Cabang_Area" FromRole="tm_area" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_user" Relationship="HRDModel.FK_SiteUser_Area" FromRole="tm_area" ToRole="tm_user" />
          <NavigationProperty Name="tm_event_setting" Relationship="HRDModel.FK_tm_event_setting_tm_area" FromRole="tm_area" ToRole="tm_event_setting" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tm_karyawan_datadinas_tm_area" FromRole="tm_area" ToRole="tm_karyawan_datadinas" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_karyawan_tm_area" FromRole="tm_area" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_libur_nasional" Relationship="HRDModel.FK_tm_libur_nasional_tm_area" FromRole="tm_area" ToRole="tm_libur_nasional" />
          <NavigationProperty Name="tm_off_karyawan" Relationship="HRDModel.FK_tm_off_karyawan_tm_area" FromRole="tm_area" ToRole="tm_off_karyawan" />
          <NavigationProperty Name="tm_user_area" Relationship="HRDModel.FK_tm_user_area_tm_area" FromRole="tm_area" ToRole="tm_user_area" />
          <NavigationProperty Name="tr_absensi" Relationship="HRDModel.FK_tr_absensi_tm_area" FromRole="tm_area" ToRole="tr_absensi" />
          <NavigationProperty Name="tr_gaji" Relationship="HRDModel.FK_tr_gaji_tm_area" FromRole="tm_area" ToRole="tr_gaji" />
          <NavigationProperty Name="tr_ijin" Relationship="HRDModel.FK_tr_ijin_tm_area" FromRole="tm_area" ToRole="tr_ijin" />
          <NavigationProperty Name="tr_kompensasi_kontrak" Relationship="HRDModel.FK_tr_kompensasi_kontrak_tm_area" FromRole="tm_area" ToRole="tr_kompensasi_kontrak" />
          <NavigationProperty Name="tr_pinjaman_bayar" Relationship="HRDModel.FK_tr_pinjaman_bayar_tm_area" FromRole="tm_area" ToRole="tr_pinjaman_bayar" />
          <NavigationProperty Name="tr_register_pinjaman" Relationship="HRDModel.FK_tr_register_pinjaman_tm_area" FromRole="tm_area" ToRole="tr_register_pinjaman" />
          <NavigationProperty Name="tr_thr" Relationship="HRDModel.FK_tr_thr_tm_area" FromRole="tm_area" ToRole="tr_thr" />
          <NavigationProperty Name="tr_tunjangan_onetime" Relationship="HRDModel.FK_tr_tunjangan_onetime_tm_area" FromRole="tm_area" ToRole="tr_tunjangan_onetime" />
          <NavigationProperty Name="tm_bonus_thr" Relationship="HRDModel.FK_tm_bonus_thr_tm_area" FromRole="tm_area" ToRole="tm_bonus_thr" />
          <NavigationProperty Name="tr_spk_lembur" Relationship="HRDModel.FK_tr_spk_lembur_tm_area" FromRole="tm_area" ToRole="tr_spk_lembur" />
          <NavigationProperty Name="tr_cuti_besar" Relationship="HRDModel.FK_tr_cuti_besar_tm_area" FromRole="tm_area" ToRole="tr_cuti_besar" />
        </EntityType>
        <Association Name="FK_tm_absensi_setting_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_absensi_setting" Role="tm_absensi_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_absensi_setting">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Cabang_Area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_cabang">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_SiteUser_Area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="0..1" />
          <End Type="HRDModel.tm_user" Role="tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_event_setting_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_event_setting" Role="tm_event_setting" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_event_setting">
              <PropertyRef Name="Area_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_datadinas_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan_datadinas">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_karyawan_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_karyawan">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_libur_nasional_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_libur_nasional" Role="tm_libur_nasional" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_libur_nasional">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_off_karyawan_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_off_karyawan" Role="tm_off_karyawan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_off_karyawan">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_user_area_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_user_area" Role="tm_user_area" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user_area">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_absensi_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_absensi" Role="tr_absensi" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_absensi">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_gaji_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_gaji" Role="tr_gaji" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_gaji">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_ijin_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_ijin" Role="tr_ijin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_ijin">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kompensasi_kontrak_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_kompensasi_kontrak" Role="tr_kompensasi_kontrak" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kompensasi_kontrak">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_pinjaman_bayar_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_pinjaman_bayar" Role="tr_pinjaman_bayar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_pinjaman_bayar">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_register_pinjaman_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_register_pinjaman" Role="tr_register_pinjaman" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_register_pinjaman">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_thr" Role="tr_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_tunjangan_onetime_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_tunjangan_onetime" Role="tr_tunjangan_onetime" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_tunjangan_onetime">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_bonus_thr">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="Periode" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Tahun" Type="String" Nullable="false" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="Bulan" Type="String" Nullable="false" MaxLength="2" FixedLength="false" Unicode="false" />
          <Property Name="Bonus" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tm_bonus_thr_tm_area" FromRole="tm_bonus_thr" ToRole="tm_area" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tm_bonus_thr_tm_cabang" FromRole="tm_bonus_thr" ToRole="tm_cabang" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tm_bonus_thr_tm_karyawan" FromRole="tm_bonus_thr" ToRole="tm_karyawan" />
          <NavigationProperty Name="tr_thr_line" Relationship="HRDModel.FK_tr_thr_line_tm_bonus_thr" FromRole="tm_bonus_thr" ToRole="tr_thr_line" />
        </EntityType>
        <Association Name="FK_tm_bonus_thr_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tm_bonus_thr" Role="tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_bonus_thr_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tm_bonus_thr" Role="tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_bonus_thr_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tm_bonus_thr" Role="tm_bonus_thr" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_bonus_thr">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_thr_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Thr_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="GajiPokok" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="T_Jabatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Bonus" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Bonus_id" Type="Int32" />
          <Property Name="DataDinas_id" Type="Int32" />
          <Property Name="T_Transport" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="T_Susu" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="T_Makan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="T_Kontrak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="T_Probation" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Pajak" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pt_PPH21" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="TahunKerja" Type="Int32" Nullable="false" />
          <Property Name="PotMasaKerja" Type="Int32" Nullable="false" />
          <Property Name="StatusPTKP_id" Type="Int32" />
          <Property Name="Ter_pajak_id" Type="Int32" />
          <Property Name="Ter_pajak_percent" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tm_bonus_thr" Relationship="HRDModel.FK_tr_thr_line_tm_bonus_thr" FromRole="tr_thr_line" ToRole="tm_bonus_thr" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_thr_line_tm_karyawan" FromRole="tr_thr_line" ToRole="tm_karyawan" />
          <NavigationProperty Name="tm_karyawan_datadinas" Relationship="HRDModel.FK_tr_thr_line_tm_karyawan_datadinas" FromRole="tr_thr_line" ToRole="tm_karyawan_datadinas" />
          <NavigationProperty Name="tr_thr" Relationship="HRDModel.FK_tr_thr_line_tr_thr" FromRole="tr_thr_line" ToRole="tr_thr" />
        </EntityType>
        <Association Name="FK_tr_thr_line_tm_bonus_thr">
          <End Type="HRDModel.tm_bonus_thr" Role="tm_bonus_thr" Multiplicity="0..1" />
          <End Type="HRDModel.tr_thr_line" Role="tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_bonus_thr">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Bonus_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_thr_line" Role="tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tm_karyawan_datadinas">
          <End Type="HRDModel.tm_karyawan_datadinas" Role="tm_karyawan_datadinas" Multiplicity="0..1" />
          <End Type="HRDModel.tr_thr_line" Role="tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan_datadinas">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="DataDinas_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_thr_line_tr_thr">
          <End Type="HRDModel.tr_thr" Role="tr_thr" Multiplicity="1" />
          <End Type="HRDModel.tr_thr_line" Role="tr_thr_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_thr">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_thr_line">
              <PropertyRef Name="Thr_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_spk_lembur">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="NoSpk" Type="String" Nullable="false" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="Version" Type="Binary" Nullable="false" MaxLength="8" FixedLength="true" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="TotalJam" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_spk_lembur_tm_area" FromRole="tr_spk_lembur" ToRole="tm_area" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_spk_lembur_tm_cabang" FromRole="tr_spk_lembur" ToRole="tm_cabang" />
          <NavigationProperty Name="tr_spk_lembur_line" Relationship="HRDModel.FK_tr_spk_lembur_line_tr_spk_lembur" FromRole="tr_spk_lembur" ToRole="tr_spk_lembur_line" />
        </EntityType>
        <Association Name="FK_tr_spk_lembur_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_spk_lembur" Role="tr_spk_lembur" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_spk_lembur" Role="tr_spk_lembur" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_spk_lembur_line_tr_spk_lembur">
          <End Type="HRDModel.tr_spk_lembur" Role="tr_spk_lembur" Multiplicity="1" />
          <End Type="HRDModel.tr_spk_lembur_line" Role="tr_spk_lembur_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_spk_lembur">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_spk_lembur_line">
              <PropertyRef Name="SPKLembur_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tr_cuti_besar">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Area_id" Type="Int32" Nullable="false" />
          <Property Name="Cabang_id" Type="Int32" Nullable="false" />
          <Property Name="NoCutiBesar" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Tanggal" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Tahun" Type="String" Nullable="false" MaxLength="4" FixedLength="false" Unicode="false" />
          <Property Name="Status" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Keterangan" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="PostedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PostedDate" Type="DateTime" Precision="3" />
          <Property Name="Posted" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="tm_area" Relationship="HRDModel.FK_tr_cuti_besar_tm_area" FromRole="tr_cuti_besar" ToRole="tm_area" />
          <NavigationProperty Name="tm_cabang" Relationship="HRDModel.FK_tr_cuti_besar_tm_cabang" FromRole="tr_cuti_besar" ToRole="tm_cabang" />
          <NavigationProperty Name="tr_cuti_besar_line" Relationship="HRDModel.FK_tr_cuti_besar_line_tr_cuti_besar" FromRole="tr_cuti_besar" ToRole="tr_cuti_besar_line" />
        </EntityType>
        <EntityType Name="tr_cuti_besar_line">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CutiBesar_id" Type="Int32" Nullable="false" />
          <Property Name="Karyawan_id" Type="Int32" Nullable="false" />
          <Property Name="TanggalTetap" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="LamaKerja" Type="Int32" Nullable="false" />
          <Property Name="Pd_GajiPokok" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Jabatan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Susu" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Probation" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Kehadiran" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Pd_T_Makan" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <NavigationProperty Name="tm_karyawan" Relationship="HRDModel.FK_tr_cuti_besar_line_tm_karyawan" FromRole="tr_cuti_besar_line" ToRole="tm_karyawan" />
          <NavigationProperty Name="tr_cuti_besar" Relationship="HRDModel.FK_tr_cuti_besar_line_tr_cuti_besar" FromRole="tr_cuti_besar_line" ToRole="tr_cuti_besar" />
        </EntityType>
        <Association Name="FK_tr_cuti_besar_tm_area">
          <End Type="HRDModel.tm_area" Role="tm_area" Multiplicity="1" />
          <End Type="HRDModel.tr_cuti_besar" Role="tr_cuti_besar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_area">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar">
              <PropertyRef Name="Area_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_tm_cabang">
          <End Type="HRDModel.tm_cabang" Role="tm_cabang" Multiplicity="1" />
          <End Type="HRDModel.tr_cuti_besar" Role="tr_cuti_besar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_cabang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar">
              <PropertyRef Name="Cabang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_line_tm_karyawan">
          <End Type="HRDModel.tm_karyawan" Role="tm_karyawan" Multiplicity="1" />
          <End Type="HRDModel.tr_cuti_besar_line" Role="tr_cuti_besar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_karyawan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar_line">
              <PropertyRef Name="Karyawan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_cuti_besar_line_tr_cuti_besar">
          <End Type="HRDModel.tr_cuti_besar" Role="tr_cuti_besar" Multiplicity="1" />
          <End Type="HRDModel.tr_cuti_besar_line" Role="tr_cuti_besar_line" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_cuti_besar">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_cuti_besar_line">
              <PropertyRef Name="CutiBesar_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="HRDModelStoreContainer" CdmEntityContainer="HRDEntities" >
          <EntitySetMapping Name="tm_menu_role">
            <EntityTypeMapping TypeName="HRDModel.tm_menu_role">
              <MappingFragment StoreEntitySet="tm_menu_role">
                <ScalarProperty Name="Role_id" ColumnName="Role_id" />
                <ScalarProperty Name="SiteMenu_id" ColumnName="SiteMenu_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_role">
            <EntityTypeMapping TypeName="HRDModel.tm_role">
              <MappingFragment StoreEntitySet="tm_role">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="RoleName" ColumnName="RoleName" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_user">
            <EntityTypeMapping TypeName="HRDModel.tm_user">
              <MappingFragment StoreEntitySet="tm_user">
                <ScalarProperty Name="PermisionArea" ColumnName="PermisionArea" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="isLockedOut" ColumnName="isLockedOut" />
                <ScalarProperty Name="isApproved" ColumnName="isApproved" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="Username" ColumnName="Username" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_user_role">
            <EntityTypeMapping TypeName="HRDModel.tm_user_role">
              <MappingFragment StoreEntitySet="tm_user_role">
                <ScalarProperty Name="Role_id" ColumnName="Role_id" />
                <ScalarProperty Name="User_id" ColumnName="User_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_menu">
            <EntityTypeMapping TypeName="HRDModel.tm_menu">
              <MappingFragment StoreEntitySet="tm_menu">
                <ScalarProperty Name="BeginGroup" ColumnName="BeginGroup" />
                <ScalarProperty Name="Parent" ColumnName="Parent" />
                <ScalarProperty Name="Url" ColumnName="Url" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="Title" ColumnName="Title" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_jenis_kelamin">
            <EntityTypeMapping TypeName="HRDModel.tm_jenis_kelamin">
              <MappingFragment StoreEntitySet="tm_jenis_kelamin">
                <ScalarProperty Name="JenisKelamin" ColumnName="JenisKelamin" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_jurusan">
            <EntityTypeMapping TypeName="HRDModel.tm_jurusan">
              <MappingFragment StoreEntitySet="tm_jurusan">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Createdby" ColumnName="Createdby" />
                <ScalarProperty Name="NamaJurusan" ColumnName="NamaJurusan" />
                <ScalarProperty Name="KodeJurusan" ColumnName="KodeJurusan" />
                <ScalarProperty Name="Pendidikan_id" ColumnName="Pendidikan_id" />
                <ScalarProperty Name="id" ColumnName="id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_pendidikan">
            <EntityTypeMapping TypeName="HRDModel.tm_pendidikan">
              <MappingFragment StoreEntitySet="tm_pendidikan">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Createdby" ColumnName="Createdby" />
                <ScalarProperty Name="NamaPendidikan" ColumnName="NamaPendidikan" />
                <ScalarProperty Name="KodePendidikan" ColumnName="KodePendidikan" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_status_PTKP">
            <EntityTypeMapping TypeName="HRDModel.tm_status_PTKP">
              <MappingFragment StoreEntitySet="tm_status_PTKP">
                <ScalarProperty Name="PTKP" ColumnName="PTKP" />
                <ScalarProperty Name="StatusPernikahan" ColumnName="StatusPernikahan" />
                <ScalarProperty Name="KodeStatus" ColumnName="KodeStatus" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_jabatan">
            <EntityTypeMapping TypeName="HRDModel.tm_jabatan">
              <MappingFragment StoreEntitySet="tm_jabatan">
                <ScalarProperty Name="JumlahCutiTahunan" ColumnName="JumlahCutiTahunan" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Jabatan" ColumnName="Jabatan" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_namabank">
            <EntityTypeMapping TypeName="HRDModel.tm_namabank">
              <MappingFragment StoreEntitySet="tm_namabank">
                <ScalarProperty Name="KodeKliring" ColumnName="KodeKliring" />
                <ScalarProperty Name="NamaBank" ColumnName="NamaBank" />
                <ScalarProperty Name="KodeBank" ColumnName="KodeBank" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_ijin">
            <EntityTypeMapping TypeName="HRDModel.tr_ijin">
              <MappingFragment StoreEntitySet="tr_ijin">
                <ScalarProperty Name="Photo" ColumnName="Photo" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Ijin_id" ColumnName="Ijin_id" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_gaji">
            <EntityTypeMapping TypeName="HRDModel.tr_gaji">
              <MappingFragment StoreEntitySet="tr_gaji">
                <ScalarProperty Name="NoVoucher" ColumnName="NoVoucher" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="KurangBayar" ColumnName="KurangBayar" />
                <ScalarProperty Name="Bayar" ColumnName="Bayar" />
                <ScalarProperty Name="TotalNet" ColumnName="TotalNet" />
                <ScalarProperty Name="TotalPotongan" ColumnName="TotalPotongan" />
                <ScalarProperty Name="TotalPendapatan" ColumnName="TotalPendapatan" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="Bulan" ColumnName="Bulan" />
                <ScalarProperty Name="Tahun" ColumnName="Tahun" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_status_perkawinan">
            <EntityTypeMapping TypeName="HRDModel.tm_status_perkawinan">
              <MappingFragment StoreEntitySet="tm_status_perkawinan">
                <ScalarProperty Name="StatusPernikahan" ColumnName="StatusPernikahan" />
                <ScalarProperty Name="KodeStatus" ColumnName="KodeStatus" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_ijin_master">
            <EntityTypeMapping TypeName="HRDModel.tm_ijin_master">
              <MappingFragment StoreEntitySet="tm_ijin_master">
                <ScalarProperty Name="IsMasuk" ColumnName="IsMasuk" />
                <ScalarProperty Name="PotongCutiTahunan" ColumnName="PotongCutiTahunan" />
                <ScalarProperty Name="PotongGaji" ColumnName="PotongGaji" />
                <ScalarProperty Name="Max" ColumnName="Max" />
                <ScalarProperty Name="IsCuti" ColumnName="IsCuti" />
                <ScalarProperty Name="NamaIjin" ColumnName="NamaIjin" />
                <ScalarProperty Name="KodeIjin" ColumnName="KodeIjin" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_register_pinjaman">
            <EntityTypeMapping TypeName="HRDModel.tr_register_pinjaman">
              <MappingFragment StoreEntitySet="tr_register_pinjaman">
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="NoVoucher" ColumnName="NoVoucher" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CicilanPerBulam" ColumnName="CicilanPerBulam" />
                <ScalarProperty Name="Saldo" ColumnName="Saldo" />
                <ScalarProperty Name="TotalBayar" ColumnName="TotalBayar" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="Memo" ColumnName="Memo" />
                <ScalarProperty Name="Amount" ColumnName="Amount" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="KodePinjaman" ColumnName="KodePinjaman" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_shift_absensi">
            <EntityTypeMapping TypeName="HRDModel.tm_shift_absensi">
              <MappingFragment StoreEntitySet="tm_shift_absensi">
                <ScalarProperty Name="Deskripsi" ColumnName="Deskripsi" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Createdby" ColumnName="Createdby" />
                <ScalarProperty Name="EndTime" ColumnName="EndTime" />
                <ScalarProperty Name="StartTime" ColumnName="StartTime" />
                <ScalarProperty Name="IsOff" ColumnName="IsOff" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_off_karyawan">
            <EntityTypeMapping TypeName="HRDModel.tm_off_karyawan">
              <MappingFragment StoreEntitySet="tm_off_karyawan">
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Createdby" ColumnName="Createdby" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="TglBerlaku" ColumnName="TglBerlaku" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_off_karyawan_line">
            <EntityTypeMapping TypeName="HRDModel.tm_off_karyawan_line">
              <MappingFragment StoreEntitySet="tm_off_karyawan_line">
                <ScalarProperty Name="Sabtu" ColumnName="Sabtu" />
                <ScalarProperty Name="Jumat" ColumnName="Jumat" />
                <ScalarProperty Name="Kamis" ColumnName="Kamis" />
                <ScalarProperty Name="Rabu" ColumnName="Rabu" />
                <ScalarProperty Name="Selasa" ColumnName="Selasa" />
                <ScalarProperty Name="Senin" ColumnName="Senin" />
                <ScalarProperty Name="Minggu" ColumnName="Minggu" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Off_id" ColumnName="Off_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_ter_pajak">
            <EntityTypeMapping TypeName="HRDModel.tm_ter_pajak">
              <MappingFragment StoreEntitySet="tm_ter_pajak">
                <ScalarProperty Name="TarifPercent" ColumnName="TarifPercent" />
                <ScalarProperty Name="Sampai" ColumnName="Sampai" />
                <ScalarProperty Name="Mulai" ColumnName="Mulai" />
                <ScalarProperty Name="Kode" ColumnName="Kode" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_event_setting_line">
            <EntityTypeMapping TypeName="HRDModel.tm_event_setting_line">
              <MappingFragment StoreEntitySet="tm_event_setting_line">
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Event_id" ColumnName="Event_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_event_setting">
            <EntityTypeMapping TypeName="HRDModel.tm_event_setting">
              <MappingFragment StoreEntitySet="tm_event_setting">
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="NamaEvent" ColumnName="NamaEvent" />
                <ScalarProperty Name="MsKeluar" ColumnName="MsKeluar" />
                <ScalarProperty Name="MsMasuk" ColumnName="MsMasuk" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_Id" ColumnName="Area_Id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_user_area">
            <EntityTypeMapping TypeName="HRDModel.tm_user_area">
              <MappingFragment StoreEntitySet="tm_user_area">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="User_id" ColumnName="User_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_pinjaman_bayar">
            <EntityTypeMapping TypeName="HRDModel.tr_pinjaman_bayar">
              <MappingFragment StoreEntitySet="tr_pinjaman_bayar">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="Memo" ColumnName="Memo" />
                <ScalarProperty Name="Amount" ColumnName="Amount" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="KodeBayar" ColumnName="KodeBayar" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_pinjaman_bayar_line">
            <EntityTypeMapping TypeName="HRDModel.tr_pinjaman_bayar_line">
              <MappingFragment StoreEntitySet="tr_pinjaman_bayar_line">
                <ScalarProperty Name="Amount" ColumnName="Amount" />
                <ScalarProperty Name="Pinjaman_id" ColumnName="Pinjaman_id" />
                <ScalarProperty Name="Bayar_id" ColumnName="Bayar_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_pinjaman_move">
            <EntityTypeMapping TypeName="HRDModel.tr_pinjaman_move">
              <MappingFragment StoreEntitySet="tr_pinjaman_move">
                <ScalarProperty Name="GajiLine_id" ColumnName="GajiLine_id" />
                <ScalarProperty Name="PinjBayarLine_id" ColumnName="PinjBayarLine_id" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Memo" ColumnName="Memo" />
                <ScalarProperty Name="Amount" ColumnName="Amount" />
                <ScalarProperty Name="TransDate" ColumnName="TransDate" />
                <ScalarProperty Name="TipeTrans" ColumnName="TipeTrans" />
                <ScalarProperty Name="Pinjaman_id" ColumnName="Pinjaman_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_libur_nasional">
            <EntityTypeMapping TypeName="HRDModel.tm_libur_nasional">
              <MappingFragment StoreEntitySet="tm_libur_nasional">
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="CutiBersama" ColumnName="CutiBersama" />
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="NamaHariLibur" ColumnName="NamaHariLibur" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_absensi">
            <EntityTypeMapping TypeName="HRDModel.tr_absensi">
              <MappingFragment StoreEntitySet="tr_absensi">
                <ScalarProperty Name="GantiHari" ColumnName="GantiHari" />
                <ScalarProperty Name="LiburNasional_id" ColumnName="LiburNasional_id" />
                <ScalarProperty Name="CutiBersama" ColumnName="CutiBersama" />
                <ScalarProperty Name="LiburNasional" ColumnName="LiburNasional" />
                <ScalarProperty Name="FromAbsensi" ColumnName="FromAbsensi" />
                <ScalarProperty Name="LemburLine_id" ColumnName="LemburLine_id" />
                <ScalarProperty Name="PotongGaji" ColumnName="PotongGaji" />
                <ScalarProperty Name="Off_b" ColumnName="Off_b" />
                <ScalarProperty Name="Ijin_id" ColumnName="Ijin_id" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Masuk_b" ColumnName="Masuk_b" />
                <ScalarProperty Name="Lembur_HLibur_9_10" ColumnName="Lembur_HLibur_9_10" />
                <ScalarProperty Name="Lembur_HLibur_8" ColumnName="Lembur_HLibur_8" />
                <ScalarProperty Name="Lembur_HLibur_1_7" ColumnName="Lembur_HLibur_1_7" />
                <ScalarProperty Name="Lembur_HKerja_3" ColumnName="Lembur_HKerja_3" />
                <ScalarProperty Name="Lembur_HKerja_2" ColumnName="Lembur_HKerja_2" />
                <ScalarProperty Name="Lembur_HKerja_1" ColumnName="Lembur_HKerja_1" />
                <ScalarProperty Name="PulangCepat" ColumnName="PulangCepat" />
                <ScalarProperty Name="Ijin_b" ColumnName="Ijin_b" />
                <ScalarProperty Name="Cuti_b" ColumnName="Cuti_b" />
                <ScalarProperty Name="Alpha_b" ColumnName="Alpha_b" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="OverTime_HLibur" ColumnName="OverTime_HLibur" />
                <ScalarProperty Name="Overtime" ColumnName="Overtime" />
                <ScalarProperty Name="Terlambat" ColumnName="Terlambat" />
                <ScalarProperty Name="JamKeluar" ColumnName="JamKeluar" />
                <ScalarProperty Name="MsJamKeluar" ColumnName="MsJamKeluar" />
                <ScalarProperty Name="JamMasuk" ColumnName="JamMasuk" />
                <ScalarProperty Name="MsJamMasuk" ColumnName="MsJamMasuk" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="karyawan_id" ColumnName="karyawan_id" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_gaji_line">
            <EntityTypeMapping TypeName="HRDModel.tr_gaji_line">
              <MappingFragment StoreEntitySet="tr_gaji_line">
                <ScalarProperty Name="Ter_pajak_percent" ColumnName="Ter_pajak_percent" />
                <ScalarProperty Name="Ter_pajak_id" ColumnName="Ter_pajak_id" />
                <ScalarProperty Name="StatusPTKP_id" ColumnName="StatusPTKP_id" />
                <ScalarProperty Name="DataDinas_id" ColumnName="DataDinas_id" />
                <ScalarProperty Name="Pd_T_Lain" ColumnName="Pd_T_Lain" />
                <ScalarProperty Name="Pd_Insentif" ColumnName="Pd_Insentif" />
                <ScalarProperty Name="JumlahPotongGaji" ColumnName="JumlahPotongGaji" />
                <ScalarProperty Name="JumlahLembur" ColumnName="JumlahLembur" />
                <ScalarProperty Name="JumlahSakit" ColumnName="JumlahSakit" />
                <ScalarProperty Name="Pd_Lembur" ColumnName="Pd_Lembur" />
                <ScalarProperty Name="JumlahCuti" ColumnName="JumlahCuti" />
                <ScalarProperty Name="JumlahIjinDispensasi" ColumnName="JumlahIjinDispensasi" />
                <ScalarProperty Name="JumlahIjin" ColumnName="JumlahIjin" />
                <ScalarProperty Name="JumlahAlpha" ColumnName="JumlahAlpha" />
                <ScalarProperty Name="HariKerja" ColumnName="HariKerja" />
                <ScalarProperty Name="Pot_Alpha" ColumnName="Pot_Alpha" />
                <ScalarProperty Name="THP" ColumnName="THP" />
                <ScalarProperty Name="TotalPotongan" ColumnName="TotalPotongan" />
                <ScalarProperty Name="Pt_Kasbon" ColumnName="Pt_Kasbon" />
                <ScalarProperty Name="Pt_SerPekerja" ColumnName="Pt_SerPekerja" />
                <ScalarProperty Name="Pt_SP" ColumnName="Pt_SP" />
                <ScalarProperty Name="Pt_PPH21" ColumnName="Pt_PPH21" />
                <ScalarProperty Name="Pt_K_JPK_Mandiri" ColumnName="Pt_K_JPK_Mandiri" />
                <ScalarProperty Name="Pt_K_JP" ColumnName="Pt_K_JP" />
                <ScalarProperty Name="Pt_K_JPK" ColumnName="Pt_K_JPK" />
                <ScalarProperty Name="Pt_K_JHT" ColumnName="Pt_K_JHT" />
                <ScalarProperty Name="Pt_P_JP" ColumnName="Pt_P_JP" />
                <ScalarProperty Name="Pt_P_JPK" ColumnName="Pt_P_JPK" />
                <ScalarProperty Name="Pt_P_JHT" ColumnName="Pt_P_JHT" />
                <ScalarProperty Name="Pt_P_JKM" ColumnName="Pt_P_JKM" />
                <ScalarProperty Name="Pt_P_JKK" ColumnName="Pt_P_JKK" />
                <ScalarProperty Name="TotalPendapatan" ColumnName="TotalPendapatan" />
                <ScalarProperty Name="Pd_T_JP" ColumnName="Pd_T_JP" />
                <ScalarProperty Name="Pd_T_JPK" ColumnName="Pd_T_JPK" />
                <ScalarProperty Name="Pd_T_JHT" ColumnName="Pd_T_JHT" />
                <ScalarProperty Name="Pd_T_JKM" ColumnName="Pd_T_JKM" />
                <ScalarProperty Name="Pd_T_JKK" ColumnName="Pd_T_JKK" />
                <ScalarProperty Name="Pd_T_Pajak" ColumnName="Pd_T_Pajak" />
                <ScalarProperty Name="Pd_T_PremiAss" ColumnName="Pd_T_PremiAss" />
                <ScalarProperty Name="Pd_T_Probation" ColumnName="Pd_T_Probation" />
                <ScalarProperty Name="Pd_T_Kontrak" ColumnName="Pd_T_Kontrak" />
                <ScalarProperty Name="Pd_T_Susu" ColumnName="Pd_T_Susu" />
                <ScalarProperty Name="Pd_T_PremiHadir" ColumnName="Pd_T_PremiHadir" />
                <ScalarProperty Name="Pd_T_Makan" ColumnName="Pd_T_Makan" />
                <ScalarProperty Name="Pd_T_Transport" ColumnName="Pd_T_Transport" />
                <ScalarProperty Name="Pd_T_Jabatan" ColumnName="Pd_T_Jabatan" />
                <ScalarProperty Name="Pd_GajiPokok" ColumnName="Pd_GajiPokok" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Gaji_id" ColumnName="Gaji_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_absensi_setting">
            <EntityTypeMapping TypeName="HRDModel.tm_absensi_setting">
              <MappingFragment StoreEntitySet="tm_absensi_setting">
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="HariKhusus" ColumnName="HariKhusus" />
                <ScalarProperty Name="JamKeluarMinggu" ColumnName="JamKeluarMinggu" />
                <ScalarProperty Name="JamMasukMinggu" ColumnName="JamMasukMinggu" />
                <ScalarProperty Name="JamKeluarSabtu" ColumnName="JamKeluarSabtu" />
                <ScalarProperty Name="JamMasukSabtu" ColumnName="JamMasukSabtu" />
                <ScalarProperty Name="JamKeluarJumat" ColumnName="JamKeluarJumat" />
                <ScalarProperty Name="JamMasukJumat" ColumnName="JamMasukJumat" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Shift" ColumnName="Shift" />
                <ScalarProperty Name="ToleransiJamKeluar" ColumnName="ToleransiJamKeluar" />
                <ScalarProperty Name="ToleransiJamMasuk" ColumnName="ToleransiJamMasuk" />
                <ScalarProperty Name="JamKeluar" ColumnName="JamKeluar" />
                <ScalarProperty Name="JamMasuk" ColumnName="JamMasuk" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_agama">
            <EntityTypeMapping TypeName="HRDModel.tm_agama">
              <MappingFragment StoreEntitySet="tm_agama">
                <ScalarProperty Name="agama" ColumnName="agama" />
                <ScalarProperty Name="id" ColumnName="id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_cabang">
            <EntityTypeMapping TypeName="HRDModel.tm_cabang">
              <MappingFragment StoreEntitySet="tm_cabang">
                <ScalarProperty Name="MingguLibur" ColumnName="MingguLibur" />
                <ScalarProperty Name="SabtuLibur" ColumnName="SabtuLibur" />
                <ScalarProperty Name="UpahMinimum_Kes" ColumnName="UpahMinimum_Kes" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpahMinimum" ColumnName="UpahMinimum" />
                <ScalarProperty Name="Fax" ColumnName="Fax" />
                <ScalarProperty Name="Telp" ColumnName="Telp" />
                <ScalarProperty Name="Alamat" ColumnName="Alamat" />
                <ScalarProperty Name="IsHeadOffice" ColumnName="IsHeadOffice" />
                <ScalarProperty Name="NamaCabang" ColumnName="NamaCabang" />
                <ScalarProperty Name="KodeCabang" ColumnName="KodeCabang" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_department">
            <EntityTypeMapping TypeName="HRDModel.tm_department">
              <MappingFragment StoreEntitySet="tm_department">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="NamaDepartemen" ColumnName="NamaDepartemen" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_spk_lembur_line">
            <EntityTypeMapping TypeName="HRDModel.tr_spk_lembur_line">
              <MappingFragment StoreEntitySet="tr_spk_lembur_line">
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="SPKLembur_id" ColumnName="SPKLembur_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_thr">
            <EntityTypeMapping TypeName="HRDModel.tr_thr">
              <MappingFragment StoreEntitySet="tr_thr">
                <ScalarProperty Name="T_Probation_b" ColumnName="T_Probation_b" />
                <ScalarProperty Name="T_Kontrak_b" ColumnName="T_Kontrak_b" />
                <ScalarProperty Name="T_Makan_b" ColumnName="T_Makan_b" />
                <ScalarProperty Name="T_Susu_b" ColumnName="T_Susu_b" />
                <ScalarProperty Name="T_Transport_b" ColumnName="T_Transport_b" />
                <ScalarProperty Name="T_Jabatan_b" ColumnName="T_Jabatan_b" />
                <ScalarProperty Name="GajiPokok_b" ColumnName="GajiPokok_b" />
                <ScalarProperty Name="TipeTHR" ColumnName="TipeTHR" />
                <ScalarProperty Name="isCompleted" ColumnName="isCompleted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="Total" ColumnName="Total" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="PeriodeDate" ColumnName="PeriodeDate" />
                <ScalarProperty Name="Periode" ColumnName="Periode" />
                <ScalarProperty Name="KodeTHR" ColumnName="KodeTHR" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_tunjangan_onetime">
            <EntityTypeMapping TypeName="HRDModel.tr_tunjangan_onetime">
              <MappingFragment StoreEntitySet="tr_tunjangan_onetime">
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="TunjanganLain" ColumnName="TunjanganLain" />
                <ScalarProperty Name="TunjanganInsentif" ColumnName="TunjanganInsentif" />
                <ScalarProperty Name="Bulan" ColumnName="Bulan" />
                <ScalarProperty Name="Tahun" ColumnName="Tahun" />
                <ScalarProperty Name="PeriodeGaji" ColumnName="PeriodeGaji" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="vw_KaryawanAkanHabisKontrak">
            <EntityTypeMapping TypeName="HRDModel.vw_KaryawanAkanHabisKontrak">
              <MappingFragment StoreEntitySet="vw_KaryawanAkanHabisKontrak">
                <ScalarProperty Name="NamaCabang" ColumnName="NamaCabang" />
                <ScalarProperty Name="KodeCabang" ColumnName="KodeCabang" />
                <ScalarProperty Name="NamaArea" ColumnName="NamaArea" />
                <ScalarProperty Name="KodeArea" ColumnName="KodeArea" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="TglAkhirKontrak" ColumnName="TglAkhirKontrak" />
                <ScalarProperty Name="Nama" ColumnName="Nama" />
                <ScalarProperty Name="NIK" ColumnName="NIK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_kompensasi_kontrak">
            <EntityTypeMapping TypeName="HRDModel.tr_kompensasi_kontrak">
              <MappingFragment StoreEntitySet="tr_kompensasi_kontrak">
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Total" ColumnName="Total" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="PeriodeDate" ColumnName="PeriodeDate" />
                <ScalarProperty Name="Periode" ColumnName="Periode" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="NoKompensasi" ColumnName="NoKompensasi" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_kompensasi_kontrak_line">
            <EntityTypeMapping TypeName="HRDModel.tr_kompensasi_kontrak_line">
              <MappingFragment StoreEntitySet="tr_kompensasi_kontrak_line">
                <ScalarProperty Name="NilaiKompensasi" ColumnName="NilaiKompensasi" />
                <ScalarProperty Name="LamaKerja" ColumnName="LamaKerja" />
                <ScalarProperty Name="TanggalAkhirKontrak" ColumnName="TanggalAkhirKontrak" />
                <ScalarProperty Name="TanggalAwalKontrak" ColumnName="TanggalAwalKontrak" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Kompensasi_id" ColumnName="Kompensasi_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_karyawan">
            <EntityTypeMapping TypeName="HRDModel.tm_karyawan">
              <MappingFragment StoreEntitySet="tm_karyawan">
                <ScalarProperty Name="TglReloadCuti" ColumnName="TglReloadCuti" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="TipeAbsensiOFF" ColumnName="TipeAbsensiOFF" />
                <ScalarProperty Name="KaryawanTetap" ColumnName="KaryawanTetap" />
                <ScalarProperty Name="StatusPerkawinan_NonPTKP_id" ColumnName="StatusPerkawinan_NonPTKP_id" />
                <ScalarProperty Name="IsBPJS_TK" ColumnName="IsBPJS_TK" />
                <ScalarProperty Name="IsBPJS_Kes" ColumnName="IsBPJS_Kes" />
                <ScalarProperty Name="NPWP" ColumnName="NPWP" />
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Createdby" ColumnName="Createdby" />
                <ScalarProperty Name="Ship" ColumnName="Ship" />
                <ScalarProperty Name="NoKTP" ColumnName="NoKTP" />
                <ScalarProperty Name="TokenExpired" ColumnName="TokenExpired" />
                <ScalarProperty Name="Token" ColumnName="Token" />
                <ScalarProperty Name="Photo" ColumnName="Photo" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="NamaAccount" ColumnName="NamaAccount" />
                <ScalarProperty Name="NoRekening" ColumnName="NoRekening" />
                <ScalarProperty Name="NamaBank_id" ColumnName="NamaBank_id" />
                <ScalarProperty Name="AlasanBerhenti" ColumnName="AlasanBerhenti" />
                <ScalarProperty Name="Tgl_Berhenti" ColumnName="Tgl_Berhenti" />
                <ScalarProperty Name="Berhenti_b" ColumnName="Berhenti_b" />
                <ScalarProperty Name="TglMasuk" ColumnName="TglMasuk" />
                <ScalarProperty Name="TahunLulus" ColumnName="TahunLulus" />
                <ScalarProperty Name="Alumni" ColumnName="Alumni" />
                <ScalarProperty Name="Jurusan_id" ColumnName="Jurusan_id" />
                <ScalarProperty Name="Pendidikan_id" ColumnName="Pendidikan_id" />
                <ScalarProperty Name="StatusPerkawinan_id" ColumnName="StatusPerkawinan_id" />
                <ScalarProperty Name="Telp" ColumnName="Telp" />
                <ScalarProperty Name="Kota" ColumnName="Kota" />
                <ScalarProperty Name="Alamat" ColumnName="Alamat" />
                <ScalarProperty Name="Agama_id" ColumnName="Agama_id" />
                <ScalarProperty Name="JenisKelamin_id" ColumnName="JenisKelamin_id" />
                <ScalarProperty Name="Umur" ColumnName="Umur" />
                <ScalarProperty Name="TglLahir" ColumnName="TglLahir" />
                <ScalarProperty Name="Tempat" ColumnName="Tempat" />
                <ScalarProperty Name="Nama" ColumnName="Nama" />
                <ScalarProperty Name="NIK" ColumnName="NIK" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_karyawan_datadinas">
            <EntityTypeMapping TypeName="HRDModel.tm_karyawan_datadinas">
              <MappingFragment StoreEntitySet="tm_karyawan_datadinas">
                <ScalarProperty Name="SaldoAwalCuti" ColumnName="SaldoAwalCuti" />
                <ScalarProperty Name="NIK_Lama" ColumnName="NIK_Lama" />
                <ScalarProperty Name="Pt_SerPekerja" ColumnName="Pt_SerPekerja" />
                <ScalarProperty Name="Pd_T_Pajak" ColumnName="Pd_T_Pajak" />
                <ScalarProperty Name="Pd_T_PremiAss" ColumnName="Pd_T_PremiAss" />
                <ScalarProperty Name="Pd_T_Probation" ColumnName="Pd_T_Probation" />
                <ScalarProperty Name="Pd_T_Kontrak" ColumnName="Pd_T_Kontrak" />
                <ScalarProperty Name="Pd_T_Transport" ColumnName="Pd_T_Transport" />
                <ScalarProperty Name="NoKontrakKerja" ColumnName="NoKontrakKerja" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="THP" ColumnName="THP" />
                <ScalarProperty Name="TotalPotongan" ColumnName="TotalPotongan" />
                <ScalarProperty Name="Pt_SP" ColumnName="Pt_SP" />
                <ScalarProperty Name="Pt_PPH21" ColumnName="Pt_PPH21" />
                <ScalarProperty Name="Pt_K_JPK_Mandiri" ColumnName="Pt_K_JPK_Mandiri" />
                <ScalarProperty Name="Pt_K_JP" ColumnName="Pt_K_JP" />
                <ScalarProperty Name="Pt_K_JPK" ColumnName="Pt_K_JPK" />
                <ScalarProperty Name="Pt_K_JHT" ColumnName="Pt_K_JHT" />
                <ScalarProperty Name="Pt_P_JP" ColumnName="Pt_P_JP" />
                <ScalarProperty Name="Pt_P_JPK" ColumnName="Pt_P_JPK" />
                <ScalarProperty Name="Pt_P_JHT" ColumnName="Pt_P_JHT" />
                <ScalarProperty Name="Pt_P_JKM" ColumnName="Pt_P_JKM" />
                <ScalarProperty Name="Pt_P_JKK" ColumnName="Pt_P_JKK" />
                <ScalarProperty Name="TotalPendapatan" ColumnName="TotalPendapatan" />
                <ScalarProperty Name="Pd_T_JP" ColumnName="Pd_T_JP" />
                <ScalarProperty Name="Pd_T_JPK" ColumnName="Pd_T_JPK" />
                <ScalarProperty Name="Pd_T_JHT" ColumnName="Pd_T_JHT" />
                <ScalarProperty Name="Pd_T_JKM" ColumnName="Pd_T_JKM" />
                <ScalarProperty Name="Pd_T_JKK" ColumnName="Pd_T_JKK" />
                <ScalarProperty Name="Pd_T_Susu" ColumnName="Pd_T_Susu" />
                <ScalarProperty Name="Pd_T_PremiHadir" ColumnName="Pd_T_PremiHadir" />
                <ScalarProperty Name="Pd_T_Makan" ColumnName="Pd_T_Makan" />
                <ScalarProperty Name="Pd_T_Jabatan" ColumnName="Pd_T_Jabatan" />
                <ScalarProperty Name="Pd_GajiPokok" ColumnName="Pd_GajiPokok" />
                <ScalarProperty Name="TglAkhirKontrak" ColumnName="TglAkhirKontrak" />
                <ScalarProperty Name="TglAwalKontrak" ColumnName="TglAwalKontrak" />
                <ScalarProperty Name="TglTetap" ColumnName="TglTetap" />
                <ScalarProperty Name="Tetap" ColumnName="Tetap" />
                <ScalarProperty Name="Jabatan_id" ColumnName="Jabatan_id" />
                <ScalarProperty Name="Department_id" ColumnName="Department_id" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_area">
            <EntityTypeMapping TypeName="HRDModel.tm_area">
              <MappingFragment StoreEntitySet="tm_area">
                <ScalarProperty Name="FlatRateBPJS" ColumnName="FlatRateBPJS" />
                <ScalarProperty Name="TglCustOffCuti" ColumnName="TglCustOffCuti" />
                <ScalarProperty Name="NoRekeningBank" ColumnName="NoRekeningBank" />
                <ScalarProperty Name="KodeNIK_Kontrak" ColumnName="KodeNIK_Kontrak" />
                <ScalarProperty Name="KodeNIK_Tetap" ColumnName="KodeNIK_Tetap" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="NamaArea" ColumnName="NamaArea" />
                <ScalarProperty Name="KodeArea" ColumnName="KodeArea" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_bonus_thr">
            <EntityTypeMapping TypeName="HRDModel.tm_bonus_thr">
              <MappingFragment StoreEntitySet="tm_bonus_thr">
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Bonus" ColumnName="Bonus" />
                <ScalarProperty Name="Bulan" ColumnName="Bulan" />
                <ScalarProperty Name="Tahun" ColumnName="Tahun" />
                <ScalarProperty Name="Periode" ColumnName="Periode" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_thr_line">
            <EntityTypeMapping TypeName="HRDModel.tr_thr_line">
              <MappingFragment StoreEntitySet="tr_thr_line">
                <ScalarProperty Name="Ter_pajak_percent" ColumnName="Ter_pajak_percent" />
                <ScalarProperty Name="Ter_pajak_id" ColumnName="Ter_pajak_id" />
                <ScalarProperty Name="StatusPTKP_id" ColumnName="StatusPTKP_id" />
                <ScalarProperty Name="PotMasaKerja" ColumnName="PotMasaKerja" />
                <ScalarProperty Name="TahunKerja" ColumnName="TahunKerja" />
                <ScalarProperty Name="Pt_PPH21" ColumnName="Pt_PPH21" />
                <ScalarProperty Name="Total" ColumnName="Total" />
                <ScalarProperty Name="Pd_T_Pajak" ColumnName="Pd_T_Pajak" />
                <ScalarProperty Name="T_Probation" ColumnName="T_Probation" />
                <ScalarProperty Name="T_Kontrak" ColumnName="T_Kontrak" />
                <ScalarProperty Name="T_Makan" ColumnName="T_Makan" />
                <ScalarProperty Name="T_Susu" ColumnName="T_Susu" />
                <ScalarProperty Name="T_Transport" ColumnName="T_Transport" />
                <ScalarProperty Name="DataDinas_id" ColumnName="DataDinas_id" />
                <ScalarProperty Name="Bonus_id" ColumnName="Bonus_id" />
                <ScalarProperty Name="Bonus" ColumnName="Bonus" />
                <ScalarProperty Name="T_Jabatan" ColumnName="T_Jabatan" />
                <ScalarProperty Name="GajiPokok" ColumnName="GajiPokok" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="Thr_id" ColumnName="Thr_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_spk_lembur">
            <EntityTypeMapping TypeName="HRDModel.tr_spk_lembur">
              <MappingFragment StoreEntitySet="tr_spk_lembur">
                <ScalarProperty Name="TotalJam" ColumnName="TotalJam" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="NoSpk" ColumnName="NoSpk" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_cuti_besar">
            <EntityTypeMapping TypeName="HRDModel.tr_cuti_besar">
              <MappingFragment StoreEntitySet="tr_cuti_besar">
                <ScalarProperty Name="Posted" ColumnName="Posted" />
                <ScalarProperty Name="PostedDate" ColumnName="PostedDate" />
                <ScalarProperty Name="PostedBy" ColumnName="PostedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Total" ColumnName="Total" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="Tahun" ColumnName="Tahun" />
                <ScalarProperty Name="Tanggal" ColumnName="Tanggal" />
                <ScalarProperty Name="NoCutiBesar" ColumnName="NoCutiBesar" />
                <ScalarProperty Name="Cabang_id" ColumnName="Cabang_id" />
                <ScalarProperty Name="Area_id" ColumnName="Area_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_cuti_besar_line">
            <EntityTypeMapping TypeName="HRDModel.tr_cuti_besar_line">
              <MappingFragment StoreEntitySet="tr_cuti_besar_line">
                <ScalarProperty Name="Total" ColumnName="Total" />
                <ScalarProperty Name="Pd_T_Makan" ColumnName="Pd_T_Makan" />
                <ScalarProperty Name="Pd_T_Kehadiran" ColumnName="Pd_T_Kehadiran" />
                <ScalarProperty Name="Pd_T_Probation" ColumnName="Pd_T_Probation" />
                <ScalarProperty Name="Pd_T_Susu" ColumnName="Pd_T_Susu" />
                <ScalarProperty Name="Pd_T_Jabatan" ColumnName="Pd_T_Jabatan" />
                <ScalarProperty Name="Pd_GajiPokok" ColumnName="Pd_GajiPokok" />
                <ScalarProperty Name="LamaKerja" ColumnName="LamaKerja" />
                <ScalarProperty Name="TanggalTetap" ColumnName="TanggalTetap" />
                <ScalarProperty Name="Karyawan_id" ColumnName="Karyawan_id" />
                <ScalarProperty Name="CutiBesar_id" ColumnName="CutiBesar_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>