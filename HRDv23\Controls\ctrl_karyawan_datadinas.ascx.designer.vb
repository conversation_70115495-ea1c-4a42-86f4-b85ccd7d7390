﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated. 
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On


Partial Public Class ctrl_karyawan_datadinas
    
    '''<summary>
    '''ASPxFormLayout2 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout2 As Global.DevExpress.Web.ASPxFormLayout
    
    '''<summary>
    '''cb_area control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_area As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_cabang control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_cabang As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_karyawan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_karyawan As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''txt_tanggal control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tanggal As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''cb_department control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_department As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_jabatan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_jabatan As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''chk_tetap control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents chk_tetap As Global.DevExpress.Web.ASPxCheckBox
    
    '''<summary>
    '''txt_tgl_tetap control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tgl_tetap As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_tgl_awal_kontrak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tgl_awal_kontrak As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_tgl_akhir_kontrak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tgl_akhir_kontrak As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_nokontrak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_nokontrak As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''txt_reload_cuti control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_reload_cuti As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_durasi_kontrak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_durasi_kontrak As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_gajipokok control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_gajipokok As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jabatan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jabatan As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_transport control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_transport As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_makan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_makan As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_premihadir control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_premihadir As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_susu control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_susu As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_kontrak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_kontrak As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_probation control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_probation As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_premi_ass control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_premi_ass As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_pajak control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_pajak As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jkk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jkk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jkm control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jkm As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jht control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jht As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jpk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jpk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pd_jp control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pd_jp As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_p_jkk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_p_jkk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_p_jkm control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_p_jkm As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_p_jht control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_p_jht As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_p_jpk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_p_jpk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_p_jp control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_p_jp As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_k_jht control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_k_jht As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_k_jpk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_k_jpk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_k_jp control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_k_jp As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_pph21 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_pph21 As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_sp control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_sp As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_pt_ser_pekerja control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_pt_ser_pekerja As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_total_pend control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_total_pend As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_total_pot control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_total_pot As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_net control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_net As Global.DevExpress.Web.ASPxSpinEdit
End Class
