﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{93A2FB6B-4503-4DA1-80F4-BA3CA6DBB7D8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>HRD.Domain</RootNamespace>
    <AssemblyName>HRD.Domain</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>HRD.Domain.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>HRD.Domain.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.DataAccess.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationUser.vb" />
    <Compile Include="ModelHrd.Context.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelHrd.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="ModelHrd.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelHrd.edmx</DependentUpon>
    </Compile>
    <Compile Include="ModelHrd.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Partial\tm_bonus_thr.vb" />
    <Compile Include="Partial\tm_event_setting_line.vb" />
    <Compile Include="Partial\tm_jurusan.vb" />
    <Compile Include="Partial\tm_karyawan_datadinas.vb" />
    <Compile Include="Partial\tm_menu_role.vb" />
    <Compile Include="Partial\tm_off_karyawan_line.vb" />
    <Compile Include="Partial\tm_user_role.vb" />
    <Compile Include="Partial\tr_absensi.vb" />
    <Compile Include="Partial\tr_kompensasi_kontrak.vb" />
    <Compile Include="Partial\tr_kompensasi_kontrak_line.vb" />
    <Compile Include="Partial\tr_pinjaman_bayar_line.vb" />
    <Compile Include="Partial\tr_spk_lembur_line.vb" />
    <Compile Include="Specification\AndSpecification.vb" />
    <Compile Include="Specification\CompositeSpecification.vb" />
    <Compile Include="Specification\ISpecification.vb" />
    <Compile Include="Specification\NotSpecification.vb" />
    <Compile Include="tm_absensi_setting.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_agama.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_area.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_bonus_thr.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_cabang.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_department.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_event_setting.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_event_setting_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_ijin_master.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_jabatan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_jenis_kelamin.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_jurusan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_karyawan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_karyawan_datadinas.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_libur_nasional.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_menu.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_menu_role.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_namabank.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_off_karyawan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_off_karyawan_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_pendidikan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_role.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_shift_absensi.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_status_perkawinan.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_status_PTKP.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_ter_pajak.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_user.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_user_area.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tm_user_role.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_absensi.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_cuti_besar.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_cuti_besar_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_gaji.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_gaji_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_ijin.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_kompensasi_kontrak.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_kompensasi_kontrak_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_pinjaman_bayar.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_pinjaman_bayar_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_pinjaman_move.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_register_pinjaman.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_spk_lembur.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_spk_lembur_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_thr.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_thr_line.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="tr_tunjangan_onetime.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_KaryawanAkanHabisKontrak.vb">
      <DependentUpon>ModelHrd.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="ModelHrd.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>ModelHrd.Designer.vb</LastGenOutput>
    </EntityDeploy>
    <None Include="App.Config" />
    <None Include="ModelHrd.edmx.diagram">
      <DependentUpon>ModelHrd.edmx</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ModelHrd.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>ModelHrd.Context.vb</LastGenOutput>
      <DependentUpon>ModelHrd.edmx</DependentUpon>
    </Content>
    <Content Include="ModelHrd.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ModelHrd.edmx</DependentUpon>
      <LastGenOutput>ModelHrd.vb</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>