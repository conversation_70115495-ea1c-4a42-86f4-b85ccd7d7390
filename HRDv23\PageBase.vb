﻿Imports System.Management
Imports HRD.Dao
Imports HRD.Helpers
Imports Z.EntityFramework.Plus

Public Class PageBase
    Inherits Page

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New MenuManager(daoFactory)
    Sub New(menu_id As String)
        If IsPostBack Then
            Return
        End If
        Dim macAddress As String = GetMacAddress()
        If macAddress <> "40:F2:E9:D2:F8:A3" AndAlso macAddress <> "02:50:70:FA:89:66" AndAlso macAddress <> "3C:F8:62:27:8F:8A" Then
            ExceptionManager.DoLogAndGetFriendlyMessageForException(New Exception($"Mac Address : {macAddress}"))
            HttpContext.Current.Response.RedirectToRoute("Login")
        End If

        If Not AuthHelper.IsAuthenticated Then
            HttpContext.Current.Response.RedirectToRoute("Login")
            Return
        End If
        If AuthHelper.GetLoggedInUserInfo Is Nothing Then
            HttpContext.Current.Response.RedirectToRoute("Login")
            Return
        End If

        If menu_id = "1" Then

        Else
            If HttpContext.Current.User.IsInRole("Administrator") Then

            Else
                Dim tmenu = mgr.DataAccess.FindBy(Function(f) f.Id = menu_id).FromCache(absoluteExpiration:=Now.AddMinutes(2)).FirstOrDefault
                Dim tRoles = tmenu.tm_menu_role.Select(Function(s) s.tm_role.RoleName).ToList

                If myFunction.CekRoleMenu(tRoles) Then
                Else
                    HttpContext.Current.Response.RedirectToRoute("Login")
                End If
            End If
        End If
    End Sub
    Public Function GetMacAddress() As String
        Dim mc As New ManagementClass("Win32_NetworkAdapterConfiguration")
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        Dim macAddress As String = String.Empty
        For Each mo As ManagementObject In moc
            If mo.Item("IPEnabled") Then
                macAddress = mo.Item("MacAddress").ToString()
                Exit For
            End If
        Next
        Return macAddress
    End Function
End Class
