﻿Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.ComponentModel
Imports HRD.Helpers

' To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line.
<System.Web.Script.Services.ScriptService()>
<System.Web.Services.WebService(Namespace:="http://tempuri.org/")> _
<System.Web.Services.WebServiceBinding(ConformsTo:=WsiProfiles.BasicProfile1_1)> _
<ToolboxItem(False)> _
Public Class HRDWebService
    Inherits System.Web.Services.WebService

    <WebMethod(True)>
    Public Function CalculateAssuransi(gajiPokok As Decimal, id_kar As String, kantor_id As String) As Object
        Return myFunction.CalculateAssuransi(gajiPokok, id_kar, kantor_id)

    End Function

    <WebMethod(True)>
    Public Function CalculatePPH21(totalPendapatan As Decimal, id_kar As String) As Object
        Dim kar = myFunction.tm_karyawan(id_kar)
        Dim pph = myFunction.PPh21CalculatorBulanan(totalPendapatan, kar.tm_status_PTKP.KodeStatus)
        Return New With {Key .PPH21 = pph.nilaiPajak}
    End Function

    <WebMethod(True)>
    Public Function GetDataDinas(id_kar As String) As Object
        Dim o = myFunction.tm_karyawan_datadinasByIdKaryawan(id_kar)
        If o IsNot Nothing Then
            Return New With {Key .Department_id = o.Department_id, Key .Jabatan_id = o.Jabatan_id, Key .Tetap = o.Tetap, Key .TglTetap = If(o.TglTetap.HasValue, Format(o.TglTetap, "yyyy-MM-dd"), Nothing) _
                , Key .TglAwalKontrak = If(o.TglAwalKontrak.HasValue, Format(o.TglAwalKontrak, "yyyy-MM-dd"), Nothing), Key .TglAkhirKontrak = If(o.TglAkhirKontrak.HasValue, Format(o.TglAkhirKontrak, "yyyy-MM-dd"), Nothing), Key .NoKontrakKerja = o.NoKontrakKerja _
                , Key .Pd_GajiPokok = o.Pd_GajiPokok, Key .Pd_T_Jabatan = o.Pd_T_Jabatan, Key .Pd_T_Transport = o.Pd_T_Transport, Key .Pd_T_Makan = o.Pd_T_Makan _
                , Key .Pd_T_PremiHadir = o.Pd_T_PremiHadir, Key .Pd_T_Susu = o.Pd_T_Susu, Key .Pd_T_Kontrak = o.Pd_T_Kontrak, Key .Pd_T_Probation = o.Pd_T_Probation _
                , Key .Pd_T_PremiAss = o.Pd_T_PremiAss, Key .Pd_T_Pajak = o.Pd_T_Pajak, Key .Pt_SP = o.Pt_SP, Key .Pt_SerPekerja = o.Pt_SerPekerja, Key .TglReloadCuti = If(o.tm_karyawan.TglReloadCuti.HasValue, Format(o.tm_karyawan.TglReloadCuti, "yyyy-MM-dd"), Nothing)}
        End If
        Return New With {Key .Department_id = Nothing, Key .Jabatan_id = Nothing, Key .Tetap = False, Key .TglTetap = Nothing _
                , Key .TglAwalKontrak = Nothing, Key .TglAkhirKontrak = Nothing, Key .NoKontrakKerja = Nothing, Key .Pd_GajiPokok = 0, Key .Pd_T_Jabatan = 0 _
                , Key .Pd_T_Transport = 0, Key .Pd_T_Makan = 0, Key .Pd_T_PremiHadir = 0, Key .Pd_T_Susu = 0, Key .Pd_T_Kontrak = 0, Key .Pd_T_Probation = 0 _
                , Key .Pd_T_PremiAss = 0, Key .Pd_T_Pajak = 0, Key .Pt_SP = 0, Key .Pt_SerPekerja = 0, Key .TglReloadCuti = Nothing}
    End Function
End Class