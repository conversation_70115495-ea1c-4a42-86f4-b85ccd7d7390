﻿<h1 class="title" id="MainHeader">Main Header</h1>
<p class="lead">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat.</p>
<h2 id="h2Header">Sub Header</h2>
<p>
    <img class="img-responsive img-float-left" src="Content/Photo/Alberto_Alonso.jpg" style="width:33%;" />
    Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.
</p>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>
<p class="indent-p"><b>Indented Paragraph</b>. Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>
<h2 class="category" id="Headings">Headings</h2>
<div class="bordered-block">
    <div>
        <h1>h1. Header</h1>
        <h2>h2. Header</h2>
        <h3>h3. Sub Header</h3>
        <h4>h4. Sub Header</h4>
        <h5>h5. Sub Header</h5>
        <h6>h6. Sub Header</h6>
    </div>
</div>

<h1>h1. Header</h1>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>

<h2>h2. Sub Header</h2>
<h3>h3. Sub Header</h3>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>

<h4>h4. Sub Header</h4>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>

<h5>h5. Sub Header</h5>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>

<h6>h6. Sub Header</h6>
<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam ultrices. Quisque porta lorem cursus erat. Mauris at est ac risus tempus consequat. In euismod nisi porta augue. Quisque at est. Duis id libero. Nulla quis erat eu justo adipiscing placerat. Nam lorem.</p>

<h2 class="category" id="Lists">Lists</h2>
<p>Some paragraph.</p>
<ul>
    <li>Lorem ipsum dolor sit amet</li>
    <li>Consectetur adipiscing elit</li>
    <li>
        Nulla volutpat aliquam velit
        <ul>
            <li>Phasellus iaculis neque</li>
            <li>Purus sodales ultricies</li>
        </ul>
    </li>
    <li>Faucibus porta lacus fringilla vel</li>
    <li>Aenean sit amet erat nunc</li>
</ul>
<p>Some paragraph.</p>
<h2 class="category" id="OrderedLists">Ordered Lists</h2>
<ol>
    <li>Lorem ipsum dolor sit amet</li>
    <li>Consectetur adipiscing elit</li>
    <li>Integer molestie lorem at massa</li>
</ol>
<h2 class="category" id="InlineTextElements">Inline text elements</h2>
<div class="indent-p">
    <p><a href="?somelink=1">Some link. Lorem ipsum dolor sit amet</a></p>
    <p><strong>Bold text.</strong></p>
    <p><em>Italicized (emphasized) text.</em></p>
    <p>Text with <mark>highlighted</mark> text.</p>
    <p><s>This text is no longer correct.</s></p>
    <p><u>Underlined text.</u></p>
    <p><small>Smaller text and other side comments.</small></p>
</div>
<h2 class="category" id="PageBreak">Page break</h2>
<hr />
<h2 class="category" id="Blockquote">Blockquote</h2>
<blockquote>
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
    <footer class="text-right">Someone to be quoted in <cite title="Source Title">Something to be quoted</cite></footer>
</blockquote>
<h2 class="category" id="ResponsiveImages">Responsive images</h2>
<img class="img-responsive" src="Content/Images/image-sample.png" />
<div class="text-center">Image description</div>
<h2 class="category" id="ResponsiveTables">Responsive Tables</h2>
<p>This table automatically displays a horizontal scrollbar if its content is too wide.</p>
<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th scope="col">#</th>
        <th scope="col">First Column</th>
        <th scope="col">Second Column</th>
        <th scope="col">Third Column</th>
        <th scope="col">Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <th scope="row">1</th>
        <td>Text 1</td>
        <td>Text 2</td>
        <td>Text 3</td>
        <td>Description 1</td>
      </tr>
      <tr>
        <th scope="row">2</th>
        <td>Text 2-1</td>
        <td>Text 2-2</td>
        <td>Text 2-3</td>
        <td>Description 2</td>
      </tr>
      <tr>
        <th scope="row">3</th>
        <td>Text 3-1</td>
        <td>Text 3-2</td>
        <td>LongDescriptionWithoutSpaces</td>
        <td>LongDescriptionWithoutSpaces</td>
      </tr>
    </tbody>
  </table>
</div>

<h2 class="category" id="CodeControlAreaBlock">Code/Control Area Block</h2>
<div class="control-area-block">
    <pre>
        &lt;%@ Page Language="C#" AutoEventWireup="true"  MasterPageFile="~/Root.master" CodeBehind="Article.aspx.cs" Inherits="ASPxResponsiveApplicationTemplate.Article" %&gt;
    </pre>
</div>
