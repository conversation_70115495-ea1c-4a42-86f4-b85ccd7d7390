﻿Imports System.Linq.Expressions
Imports HRD.Domain
Public Class PinjamanStatusSpec
    Inherits CompositeSpecification(Of tr_register_pinjaman)

    Private ReadOnly _status As String
    Sub New(sStatus As String)
        _status = sStatus
        Criteria = Function(f) f.Status = _status
    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of tr_register_pinjaman, Boolean))

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of tr_register_pinjaman)) As IQueryable(Of tr_register_pinjaman)
        Return query.Where(Criteria)
    End Function

    'Public Overrides Function IsSatisfiedBy(candidate As tr_register_pinjaman) As Boolean
    '    Return candidate.Status = _status
    'End Function
End Class
