﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucSalaryEntry_list.ascx.vb" Inherits="HRDv23.ucSalaryEntry_list" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="Filter" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem Caption="Perusahaan" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabanglist.PerformCallback();
	grd_salaryentry.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" IsRequired="True" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem Caption="Cabang" ColSpan="1">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabanglist" EnableCallbackMode="True" NullText="ALL" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	grd_salaryentry.Refresh();
}" />
                                <Columns>
                                    <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                    </dx:ListBoxColumn>
                                    <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                    </dx:ListBoxColumn>
                                </Columns>
                                <ClearButton DisplayMode="Always">
                                </ClearButton>
                                <ValidationSettings SetFocusOnError="True">
                                    <RequiredField ErrorText="Required" />
                                </ValidationSettings>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Salary Entry" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_salaryentry">
                                <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_salaryentry.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_salaryentry.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_datadinas.PerformCallback('new'); 
break;
}

}" />
                                <SettingsAdaptivity AdaptivityMode="HideDataCells">
                                </SettingsAdaptivity>
                                <SettingsPager AlwaysShowPager="True">
                                    <AllButton Visible="True">
                                    </AllButton>
                                    <PageSizeItemSettings Visible="True">
                                    </PageSizeItemSettings>
                                </SettingsPager>
                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                <SettingsBehavior AllowFocusedRow="True" />
                                <SettingsPopup>
                                    <FilterControl AutoUpdatePosition="False">
                                    </FilterControl>
                                </SettingsPopup>
                                <SettingsSearchPanel Visible="True" />
                                <SettingsExport EnableClientSideExportAPI="True">
                                </SettingsExport>
                                <Columns>
                                    <dx:GridViewCommandColumn ShowClearFilterButton="True" ShowInCustomizationForm="True" VisibleIndex="0">
                                        <CustomButtons>
                                            <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit Salary">
                                                <Image IconID="dashboards_editnames_svg_16x16">
                                                </Image>
                                            </dx:GridViewCommandColumnCustomButton>
                                        </CustomButtons>
                                    </dx:GridViewCommandColumn>
                                    <dx:GridViewDataDateColumn FieldName="Tanggal" ShowInCustomizationForm="True" VisibleIndex="3">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn Caption="NIK" FieldName="tm_karyawan.NIK" ShowInCustomizationForm="True" VisibleIndex="4">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Department" FieldName="tm_department.NamaDepartemen" ShowInCustomizationForm="True" VisibleIndex="6">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Jabatan" FieldName="tm_jabatan.Jabatan" ShowInCustomizationForm="True" VisibleIndex="7">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataCheckColumn FieldName="Tetap" ShowInCustomizationForm="True" VisibleIndex="8">
                                    </dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataTextColumn Caption="Perusahaan" FieldName="tm_karyawan.tm_area.NamaArea" ShowInCustomizationForm="True" VisibleIndex="1">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Cabang" FieldName="tm_karyawan.tm_cabang.NamaCabang" ShowInCustomizationForm="True" VisibleIndex="2">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Nama" FieldName="tm_karyawan.Nama" ShowInCustomizationForm="True" VisibleIndex="5">
                                    </dx:GridViewDataTextColumn>
                                </Columns>
                                <Toolbars>
                                    <dx:GridViewToolbar>
                                        <Items>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem Command="ShowFilterRow">
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                            </dx:ASPxGridView>
                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" DefaultSorting="Id Desc" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_karyawan_datadinas" />
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

