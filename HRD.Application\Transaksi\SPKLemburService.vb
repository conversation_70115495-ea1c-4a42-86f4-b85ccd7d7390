Imports HRD.Application
Imports HRD.Domain
Public Class SPKLemburService
    Implements ISPKLemburService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "SPK Lembur Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements ISPKLemburService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_spk_lembur)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ISPKLemburService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_spk_lembur, oLines As IList(Of tr_spk_lembur_line)) As Task(Of ResponseModel) Implements ISPKLemburService.UpsertAsync
        Try

            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_spk_lembur)().Update(o)
            Else
                o.NoSpk = CreateNoSPK(o)
                Await _unitOfWork.Repository(Of tr_spk_lembur)().AddAsync(o)

            End If

            UpSertLeburLine(o, oLines)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ISPKLemburService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_spk_lembur_line).Delete(Function(f) f.SPKLembur_id = Id)
                Await _unitOfWork.Repository(Of tr_spk_lembur).DeleteAsync(Id)

                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Function CreateNoSPK(p As tr_spk_lembur) As String
        Dim area = _myFunction.tm_area(p.Area_id)
        Dim sFormat As String = $"SPL-{area.KodeArea}-{Format(p.Tanggal, "yy")}-"
        Dim o = _unitOfWork.Repository(Of tr_spk_lembur).TableNoTracking.Where(Function(f) f.Area_id = p.Area_id And f.NoSpk.StartsWith(sFormat)).OrderByDescending(Function(od) od.NoSpk).Take(1).FirstOrDefault
        Dim i As Integer = 0
        If o IsNot Nothing Then
            i = o.NoSpk.Replace(sFormat, "")
        End If
        i += 1

        Return sFormat & Format(i, "00000")
    End Function
    Private Function UpSertLeburLine(ByRef tEntity As tr_spk_lembur, lines As IList(Of tr_spk_lembur_line)) As Boolean
        Try
            For Each x In lines
                Dim y As New tr_spk_lembur_line With {.Deleting = x.Deleting, .Id = x.Id, .Karyawan_id = x.Karyawan_id, .RowGuid = x.RowGuid, .SPKLembur_id = x.SPKLembur_id}
                If y.Deleting Then
                    If y.Id > 0 Then
                        _unitOfWork.Repository(Of tr_spk_lembur_line).Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tr_spk_lembur = tEntity
                        _unitOfWork.Repository(Of tr_spk_lembur_line).AddAsync(y).GetAwaiter.GetResult()
                    Else
                        _unitOfWork.Repository(Of tr_spk_lembur_line).Update(y)
                    End If
                End If
            Next

            Return True
        Catch ex As Exception
            Return False
        End Try

    End Function
    Public Async Function PostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel) Implements ISPKLemburService.PostingAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur).Get(TEntity.Id)

            o.Posted = True
            o.PostedBy = TEntity.PostedBy
            o.PostedDate = Now

            _unitOfWork.Repository(Of tr_spk_lembur).Update(o)

            _unitOfWork.Save()
            For Each x In TEntity.tr_spk_lembur_line
                Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.karyawan_id = x.Karyawan_id And f.Tanggal = TEntity.Tanggal).FirstOrDefault
                Dim isNewRecord As Boolean = False
                If abs Is Nothing Then                    ' Initialize a new absensi record with all necessary fields
                    abs = New tr_absensi With {
                        .Area_id = TEntity.Area_id,
                        .Cabang_id = TEntity.Cabang_id,
                        .karyawan_id = x.Karyawan_id,
                        .Tanggal = TEntity.Tanggal,
                        .Masuk_b = True,  ' Set as Hadir since there's overtime
                        .CreatedBy = TEntity.PostedBy,
                        .CreatedDate = Now
                    }
                    isNewRecord = True
                End If
                _myFunction.UpdateAbsensiDataLembur(abs, x.tm_karyawan)

                ' Add to repository if it's a new record
                If isNewRecord Then
                    Await _unitOfWork.Repository(Of tr_absensi)().AddAsync(abs)
                Else
                    _unitOfWork.Repository(Of tr_absensi).Update(abs)
                End If
            Next

            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function ProcessBatchPostingAsync(batchIds() As Integer, userName As String) As Task(Of ResponseModel) Implements ISPKLemburService.ProcessBatchPostingAsync
        Try
            Dim successCount As Integer = 0
            Dim failCount As Integer = 0            ' Get all entities in one query for better performance
            Dim entities = _unitOfWork.Repository(Of tr_spk_lembur).TableNoTracking _
                .Where(Function(x) batchIds.Contains(x.Id)) _
                .ToList()

            ' Get all related karyawan_ids and tanggal values for absensi records
            Dim karyawanTanggalPairs = New List(Of Tuple(Of Integer, Date, Integer, Integer))()

            ' First phase: Update all SPK Lembur records in a single transaction
            For Each _Entity In entities
                Try
                    ' Mark as posted
                    _Entity.Posted = True
                    _Entity.PostedBy = userName
                    _Entity.PostedDate = Now
                    ' Update the entity
                    _unitOfWork.Repository(Of tr_spk_lembur).Update(_Entity)                    ' Collect karyawan and tanggal info for absensi processing
                    For Each line In _Entity.tr_spk_lembur_line
                        karyawanTanggalPairs.Add(Tuple.Create(line.Karyawan_id, _Entity.Tanggal, _Entity.Area_id, _Entity.Cabang_id))
                    Next

                    successCount += 1
                Catch ex As Exception
                    failCount += 1
                    Log($"{NameOf(Me.ProcessBatchPostingAsync)}_Phase1", $"Error processing SPK Lembur ID {_Entity.Id}: {ex.Message}")
                End Try
            Next

            ' Save the first phase changes
            _unitOfWork.Save()

            ' Second phase: Process absensi records
            ' Collect all existing absensi records first (to minimize database queries)
            Dim karyawanIds = karyawanTanggalPairs.Select(Function(x) x.Item1).Distinct().ToList()
            Dim tanggalList = karyawanTanggalPairs.Select(Function(x) x.Item2).Distinct().ToList()            ' Get all existing absensi records that match our criteria in one query
            Dim existingAbsensi = _unitOfWork.Repository(Of tr_absensi).TableNoTracking _
                .Where(Function(a) karyawanIds.Contains(a.karyawan_id) AndAlso tanggalList.Contains(a.Tanggal)) _
                .ToDictionary(Function(a) $"{a.karyawan_id}_{a.Tanggal.ToString("yyyyMMdd")}")

            ' Process each karyawan-tanggal pair
            For Each pair In karyawanTanggalPairs
                Dim karyawanId = pair.Item1
                Dim tanggal = pair.Item2
                Dim areaId = pair.Item3
                Dim cabangId = pair.Item4

                ' Create a key to look up in our dictionary
                Dim key = $"{karyawanId}_{tanggal.ToString("yyyyMMdd")}"

                Try
                    Dim absensi As tr_absensi
                    Dim isNewRecord As Boolean = False

                    ' Check if we already have this record
                    If existingAbsensi.ContainsKey(key) Then
                        absensi = existingAbsensi(key)
                    Else
                        ' Create a new absensi record
                        absensi = New tr_absensi With {
                            .Area_id = areaId,
                            .Cabang_id = cabangId,
                            .karyawan_id = karyawanId,
                            .Tanggal = tanggal,
                            .Masuk_b = True,
                            .CreatedBy = userName,
                            .CreatedDate = Now
                        }
                        isNewRecord = True
                    End If                    ' Get the karyawan info
                    Dim karyawan = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking _
                        .FirstOrDefault(Function(k) k.Id = karyawanId)

                    If karyawan IsNot Nothing Then
                        ' Update the absensi data
                        _myFunction.UpdateAbsensiDataLembur(absensi, karyawan)

                        ' Add or update the record
                        If isNewRecord Then
                            Await _unitOfWork.Repository(Of tr_absensi)().AddAsync(absensi)
                        Else
                            _unitOfWork.Repository(Of tr_absensi)().Update(absensi)
                        End If
                    End If
                Catch ex As Exception
                    Log($"{NameOf(Me.ProcessBatchPostingAsync)}_Phase2", $"Error processing absensi for karyawan {karyawanId} on {tanggal}: {ex.Message}")
                    ' We don't increment failCount here as we already counted the success for the SPK Lembur record
                End Try
            Next

            ' Save the second phase changes
            _unitOfWork.Save()

            ' Create a dictionary to return success and fail counts
            Dim counts = New Dictionary(Of String, Integer) From {
                {"success", successCount},
                {"fail", failCount}
            }

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, counts)
        Catch ex As Exception
            Log(NameOf(Me.ProcessBatchPostingAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UnPostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel) Implements ISPKLemburService.UnPostingAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur).Get(TEntity.Id)

            o.Posted = False
            o.ModifiedBy = TEntity.PostedBy
            o.ModifiedDate = Now

            _unitOfWork.Repository(Of tr_spk_lembur).Update(o)

            _unitOfWork.Save()

            For Each x In TEntity.tr_spk_lembur_line
                Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.karyawan_id = x.Karyawan_id And f.Tanggal = TEntity.Tanggal).FirstOrDefault
                If abs IsNot Nothing Then
                    With abs
                        If .Overtime > 0 Then
                            .Lembur_HKerja_1 = 0
                            .Lembur_HKerja_2 = 0
                            .Lembur_HKerja_3 = 0
                            .LemburLine_id = Nothing
                            _unitOfWork.Repository(Of tr_absensi).Update(abs)
                        ElseIf .OverTime_HLibur > 0 Then
                            Await _unitOfWork.Repository(Of tr_absensi).Delete(abs)
                        End If
                    End With
                End If

            Next

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
