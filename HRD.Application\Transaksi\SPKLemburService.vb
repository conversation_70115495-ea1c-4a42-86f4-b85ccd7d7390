﻿Imports HRD.Application
Imports HRD.Domain
Public Class SPKLemburService
    Implements ISPKLemburService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "SPK Lembur Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements ISPKLemburService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_spk_lembur)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ISPKLemburService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_spk_lembur, oLines As IList(Of tr_spk_lembur_line)) As Task(Of ResponseModel) Implements ISPKLemburService.UpsertAsync
        Try

            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_spk_lembur)().Update(o)
            Else
                o.NoSpk = CreateNoSPK(o)
                Await _unitOfWork.Repository(Of tr_spk_lembur)().AddAsync(o)

            End If

            UpSertLeburLine(o, oLines)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ISPKLemburService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_spk_lembur_line).Delete(Function(f) f.SPKLembur_id = Id)
                Await _unitOfWork.Repository(Of tr_spk_lembur).DeleteAsync(Id)

                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    Private Function CreateNoSPK(p As tr_spk_lembur) As String
        Dim area = _myFunction.tm_area(p.Area_id)
        Dim sFormat As String = $"SPL-{area.KodeArea}-{Format(p.Tanggal, "yy")}-"
        Dim o = _unitOfWork.Repository(Of tr_spk_lembur).TableNoTracking.Where(Function(f) f.Area_id = p.Area_id And f.NoSpk.StartsWith(sFormat)).OrderByDescending(Function(od) od.NoSpk).Take(1).FirstOrDefault
        Dim i As Integer = 0
        If o IsNot Nothing Then
            i = o.NoSpk.Replace(sFormat, "")
        End If
        i += 1

        Return sFormat & Format(i, "00000")
    End Function
    Private Function UpSertLeburLine(ByRef tEntity As tr_spk_lembur, lines As IList(Of tr_spk_lembur_line)) As Boolean
        Try
            For Each x In lines
                Dim y As New tr_spk_lembur_line With {.Deleting = x.Deleting, .Id = x.Id, .Karyawan_id = x.Karyawan_id, .RowGuid = x.RowGuid, .SPKLembur_id = x.SPKLembur_id}
                If y.Deleting Then
                    If y.Id > 0 Then
                        _unitOfWork.Repository(Of tr_spk_lembur_line).Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tr_spk_lembur = tEntity
                        _unitOfWork.Repository(Of tr_spk_lembur_line).AddAsync(y).GetAwaiter.GetResult()
                    Else
                        _unitOfWork.Repository(Of tr_spk_lembur_line).Update(y)
                    End If
                End If
            Next

            Return True
        Catch ex As Exception
            Return False
        End Try

    End Function
    Public Async Function PostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel) Implements ISPKLemburService.PostingAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur).Get(TEntity.Id)

            o.Posted = True
            o.PostedBy = TEntity.PostedBy
            o.PostedDate = Now

            _unitOfWork.Repository(Of tr_spk_lembur).Update(o)

            _unitOfWork.Save()

            For Each x In TEntity.tr_spk_lembur_line
                Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.karyawan_id = x.Karyawan_id And f.Tanggal = TEntity.Tanggal).FirstOrDefault
                If abs Is Nothing Then
                    abs = New tr_absensi With {.Area_id = TEntity.Area_id, .Cabang_id = TEntity.Cabang_id, .karyawan_id = x.Karyawan_id, .Tanggal = TEntity.Tanggal}
                End If
                _myFunction.UpdateAbsensiDataLembur(abs, x.tm_karyawan)
            Next

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UnPostingAsync(TEntity As tr_spk_lembur) As Task(Of ResponseModel) Implements ISPKLemburService.UnPostingAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_spk_lembur).Get(TEntity.Id)

            o.Posted = False
            o.ModifiedBy = TEntity.PostedBy
            o.ModifiedDate = Now

            _unitOfWork.Repository(Of tr_spk_lembur).Update(o)

            _unitOfWork.Save()

            For Each x In TEntity.tr_spk_lembur_line
                Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.karyawan_id = x.Karyawan_id And f.Tanggal = TEntity.Tanggal).FirstOrDefault
                If abs IsNot Nothing Then
                    With abs
                        If .Overtime > 0 Then
                            .Lembur_HKerja_1 = 0
                            .Lembur_HKerja_2 = 0
                            .Lembur_HKerja_3 = 0
                            .LemburLine_id = Nothing
                            _unitOfWork.Repository(Of tr_absensi).Update(abs)
                        ElseIf .OverTime_HLibur > 0 Then
                            Await _unitOfWork.Repository(Of tr_absensi).Delete(abs)
                        End If
                    End With
                End If

            Next

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
