Public Class GetAreaHandler
    Implements IRequestHandler(Of GetAreaQuery, IEnumerable(Of AreaDto))

    Private ReadOnly _context As IDbContext

    Public Sub New(context As IDbContext)
        _context = context
    End Sub

    Public Async Function Handle(request As GetAreaQuery, cancellationToken As CancellationToken) As Task(Of IEnumerable(Of AreaDto)) Implements IRequestHandler(Of GetAreaQuery, IEnumerable(Of AreaDto)).Handle
        Dim query = From a In _context.tm_area
                   Where a.Kd_area = request.Kd_area
                   Select New AreaDto With {
                       .Kd_area = a.Kd_area,
                       .Nama_area = a.Nama_area,
                       .Is_aktif = a.Is_aktif
                   }

        Return Await query.ToListAsync()
    End Function
End Class
