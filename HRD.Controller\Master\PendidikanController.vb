﻿Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Public Class PendidikanController
    Inherits Controller(Of tm_pendidikan)
    Implements IPendidikanListController, IPendidikanEditController

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New PendidikanManager(daoFactory)


    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_pendidikan
    End Sub

    Public Overrides Sub SelectItem(id As String)
        mgr.DataAccess.ResetContext()
        SelectedItem = mgr.DataAccess.GetById(id)
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim o = mgr.DataAccess.GetById(id)
            mgr.GetJurusanDao.DeleteRange(o.tm_jurusan.ToList)
            mgr.DataAccess.Delete(o)


            mgr.DataAccess.Save()
            Reset()
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try

    End Sub

    Public Overrides Sub Saving()
        mgr.DataAccess.ResetContext()

        Try
            ExpressMapper.Mapper.Register(Of tm_pendidikan, tm_pendidikan) _
                .Ignore(Function(f) f.tm_jurusan) _
                .Ignore(Function(f) f.tm_karyawan)
            Dim o = ExpressMapper.Mapper.Map(Of tm_pendidikan, tm_pendidikan)(SelectedItem)

            If o.Id <= 0 Then
                o.Createdby = AuthHelper.GetLoggedInUserInfo.UserName
                o.CreatedDate = Now
                mgr.DataAccess.Add(o)
            Else
                o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.ModifiedDate = Now
                mgr.DataAccess.Edit(o)
            End If

            For Each x In SelectedItem.tm_jurusan

                ExpressMapper.Mapper.Register(Of tm_jurusan, tm_jurusan) _
                    .Ignore(Function(f) f.tm_karyawan) _
                    .Ignore(Function(f) f.tm_pendidikan)

                Dim y = ExpressMapper.Mapper.Map(Of tm_jurusan, tm_jurusan)(x)

                If y.Deleting Then
                    If y.id > 0 Then
                        mgr.GetJurusanDao.Delete(y)
                    End If
                Else
                    If y.id <= 0 Then
                        y.tm_pendidikan = o
                        mgr.GetJurusanDao.Add(y)
                    Else
                        mgr.GetJurusanDao.Edit(y)
                    End If
                End If
            Next


            mgr.DataAccess.Save()
            Saved = True
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(o As tm_pendidikan)
        Throw New NotImplementedException()
    End Sub
End Class
Public Interface IPendidikanListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface IPendidikanEditController
    Inherits IControllerMain, IControllerEdit(Of tm_pendidikan)

End Interface