﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web
Imports DevExpress.Data.Linq
Public Class ucThr_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ITHREditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then
                cb_area.Enabled = (oAction = Action.AddNew)
                cb_cabang.Enabled = (oAction = Action.AddNew)
            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_thr_release.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_thr_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_thr.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_thr.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction

        If Controller.Action = Action.Posting Then
            Controller.Saving(oSelectItem)
            Return
        End If

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tr_thr
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tr_thr)
        End Get
        Set(value As tr_thr)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub LinqServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles LinqServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        If oSelectItem Is Nothing Then
            Return
        End If

        e.QueryableSource = Controller.DataListLine(oSelectItem.Id).AsQueryable
    End Sub
End Class