﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Imports HRD.Helpers

Public Class rpt_rekap_absen
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Static i As Integer

        i += 1

        cell_no.Text = $"{i}."
    End Sub

    Private Sub fTerlambat_GetValue(sender As Object, e As GetValueEventArgs) Handles fTerlambat.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        e.Value = o.tr_absensi.Where(Function(f) f.Terlambat > 0 AndAlso f.<PERSON> >= pStartDate.Value And f.Tanggal < tgl2).Count
    End Sub

    Private Sub fAlpha_GetValue(sender As Object, e As GetValueEventArgs) Handles fAlpha.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        e.Value = o.tr_absensi.Where(Function(f) f.Alpha_b = True AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count
    End Sub

    Private Sub fIjin_GetValue(sender As Object, e As GetValueEventArgs) Handles fIjin.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        e.Value = o.tr_absensi.Where(Function(f) If(f.Ijin_id.HasValue AndAlso f.tr_ijin.tm_ijin_master.KodeIjin <> "ID" AndAlso f.tr_ijin.tm_ijin_master.KodeIjin.Substring(0, 1) = "I", True, False) AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count

    End Sub

    Private Sub fSakit_GetValue(sender As Object, e As GetValueEventArgs) Handles fSakit.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        e.Value = o.tr_absensi.Where(Function(f) If(f.Ijin_id.HasValue AndAlso f.tr_ijin.tm_ijin_master.KodeIjin.Substring(0, 1) = "S", True, False) AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count
    End Sub

    Private Sub fIjinID_GetValue(sender As Object, e As GetValueEventArgs) Handles fIjinID.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        e.Value = o.tr_absensi.Where(Function(f) If(f.Ijin_id.HasValue AndAlso f.tr_ijin.tm_ijin_master.KodeIjin = "ID", True, False) AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count
    End Sub

    Private Sub fCuti_GetValue(sender As Object, e As GetValueEventArgs) Handles fCuti.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)
        Dim tgl1 As Date = pStartDate.Value
        If tgl1 < o.tm_area.TglCustOffCuti Then
            tgl1 = o.tm_area.TglCustOffCuti.Value
        End If

        'e.Value = o.tr_absensi.Where(Function(f) f.Cuti_b = True And f.Ijin_b <> True AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count
        e.Value = o.tr_absensi.Where(Function(f) ((f.Ijin_id.HasValue AndAlso f.tr_ijin.tm_ijin_master.PotongCutiTahunan) Or f.CutiBersama) AndAlso f.Tanggal >= tgl1 And f.Tanggal < tgl2).Count
    End Sub

    Private Sub fSisaCuti_GetValue(sender As Object, e As GetValueEventArgs) Handles fSisaCuti.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, pEndDate.Value)

        Dim saCuti = myFunction.GetSaldoCuti(o.Id, pEndDate.Value)
        'Dim jmlCuti = o.tr_absensi.Where(Function(f) f.Cuti_b = True AndAlso f.Tanggal >= pStartDate.Value And f.Tanggal < tgl2).Count

        e.Value = saCuti '- jmlCuti
    End Sub
End Class