﻿Public Class NotSpecification(Of T)
    Inherits CompositeSpecification(Of T)

    Private _innerSpecification As ISpecification(Of T)

    Public Sub New(ByVal innerSpecification As ISpecification(Of T))
        _innerSpecification = innerSpecification
    End Sub

    Public Overrides Function IsSatisfiedBy(ByVal candidate As T) As Boolean
        Return Not _innerSpecification.IsSatisfiedBy(candidate)
    End Function
End Class
