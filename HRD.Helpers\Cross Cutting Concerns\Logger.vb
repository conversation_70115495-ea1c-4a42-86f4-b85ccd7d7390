﻿Imports System.IO
Imports System.Web

Public NotInheritable Class Logger
    Public Shared Sub LogError()
        Dim ex As System.Exception = System.Web.HttpContext.Current.Server.GetLastError()
        LogError(ex)

    End Sub

    Public Shared Sub LogError(ByVal ex As Exception)
        Dim currentContext = HttpContext.Current

        If ex.Message.Equals("File does not exist.") Then
            currentContext.ClearError()
            Return
        End If

        Dim logSummery As String, logDetails As String, filePath As String = "No file path found.", url As String = "No url found to be reported."

        If currentContext IsNot Nothing Then
            filePath = currentContext.Request.FilePath
            url = currentContext.Request.Url.AbsoluteUri
        End If

        logSummery = ex.Message
        logDetails = ex.ToString()

        '-----------------------------------------------------

        Dim path As String = System.Web.HttpContext.Current.Server.MapPath("~/App_Resources/system/log/log.txt")
        Dim fStream As New IO.FileStream(path, FileMode.Append, FileAccess.Write)
        Dim bfs As New BufferedStream(fStream)
        Dim sWriter As New StreamWriter(bfs)

        'insert a separator line
        sWriter.WriteLine("=================================================================================================")

        'create log for header
        sWriter.WriteLine(logSummery)
        sWriter.WriteLine("Log time:" & Date.Now)
        sWriter.WriteLine("URL: " & url)
        sWriter.WriteLine("File Path: " & filePath)

        'create log for body
        sWriter.WriteLine(logDetails)

        'insert a separator line
        sWriter.WriteLine("=================================================================================================")

        sWriter.Close()

    End Sub

    'Public Shared Sub LogPembiayaan(_kantor As String, _no As String, i As String, _kodeanggota As String, _namaanggota As String, _no_rek As String)
    '    Dim currentContext = HttpContext.Current
    '    Dim logDetails As String, filePath As String = "No file path found.", url As String = "No url found to be reported."
    '    If currentContext IsNot Nothing Then
    '        filePath = currentContext.Request.FilePath
    '        url = currentContext.Request.Url.AbsoluteUri
    '    End If
    '    Dim _tgl As String = Format(Now, "dd-MM-yyyy HH:00")
    '    _tgl = _tgl.Replace(":", "_")
    '    Dim _filename As String = String.Format("~/App_Resources/system/log/{0} LogPemb {1}.txt", _tgl, MyFunction.GetKantorLayanan(_kantor).Namakantor)
    '    logDetails = String.Format("{0}. Row {1} : IDAnggota {2} {3} Rek.{4}", _no, i, _kodeanggota, _namaanggota, _no_rek)
    '    'Dim path As String = System.Web.HttpContext.Current.Server.MapPath("~/App_Resources/system/log/log_pembiyaan.txt")
    '    Dim path As String = System.Web.HttpContext.Current.Server.MapPath(_filename)
    '    Dim fStream As New IO.FileStream(path, FileMode.Append, FileAccess.Write)
    '    Dim bfs As New BufferedStream(fStream)
    '    Dim sWriter As New StreamWriter(bfs)
    '    sWriter.WriteLine(logDetails)
    '    sWriter.Close()
    'End Sub

    'Public Shared Sub LogAnggota(_kantor As String, _no As String, i As String, _kodeanggota As String, _namaanggota As String)
    '    Dim currentContext = HttpContext.Current
    '    Dim logDetails As String, filePath As String = "No file path found.", url As String = "No url found to be reported."
    '    If currentContext IsNot Nothing Then
    '        filePath = currentContext.Request.FilePath
    '        url = currentContext.Request.Url.AbsoluteUri
    '    End If
    '    Dim _tgl As String = Format(Now, "dd-MM-yyyy HH:00")
    '    _tgl = _tgl.Replace(":", "_")
    '    Dim _filename As String = String.Format("~/App_Resources/system/log/{0} LogAnggota {1}.txt", _tgl, MyFunction.GetKantorLayanan(_kantor).Namakantor)
    '    logDetails = String.Format("{0}. Row {1} : IDAnggota {2} {3}", _no, i, _kodeanggota, _namaanggota)
    '    'Dim path As String = System.Web.HttpContext.Current.Server.MapPath("~/App_Resources/system/log/log_pembiyaan.txt")
    '    Dim path As String = System.Web.HttpContext.Current.Server.MapPath(_filename)
    '    Dim fStream As New IO.FileStream(path, FileMode.Append, FileAccess.Write)
    '    Dim bfs As New BufferedStream(fStream)
    '    Dim sWriter As New StreamWriter(bfs)
    '    sWriter.WriteLine(logDetails)
    '    sWriter.Close()
    'End Sub

End Class
