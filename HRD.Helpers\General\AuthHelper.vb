﻿Imports HRD.Dao
Imports HRD.Domain
Imports Microsoft.VisualBasic
Imports System.Web
Imports System.Web.Security



Public NotInheritable Class AuthHelper

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly manager As New UserManager(daoFactory)


    Private Sub New()
    End Sub
    Public Shared Function SignIn(ByVal userName As String, ByVal password As String) As Boolean

        Dim myUser = manager.ValidateUser(userName, password)
        If myUser IsNot Nothing Then
            HttpContext.Current.Session("User") = New ApplicationUser With {
            .UserName = myUser.Username,
            .FirstName = myUser.Username,
            .LastName = "",
            .AvatarUrl = "~/Content/Photo/User.png",
            .AreaIdPermisionList = myUser.tm_user_area.Select(Function(s) s.Area_id).ToList
            } 'CreateDefualtUser()  Mock user data

            Return True
        End If

        Return False
    End Function
    Public Shared Sub SignOut()
        HttpContext.Current.Session("User") = Nothing
        FormsAuthentication.SignOut()
        HttpContext.Current.Session.Clear()
        HttpContext.Current.Session.Abandon()
    End Sub
    Public Shared Function IsAuthenticated() As Boolean
        Return GetLoggedInUserInfo() IsNot Nothing
    End Function

    Public Shared Function GetLoggedInUserInfo() As ApplicationUser
        Dim o = manager.GetUserByUserName(HttpContext.Current.User.Identity.Name)
        If o Is Nothing Then
            Return Nothing
        End If
        Dim x = UserToApplicationUser(o)
        Return TryCast(x, ApplicationUser)
    End Function
    Private Shared Function CreateDefualtUser() As ApplicationUser
        Return New ApplicationUser With {
                .UserName = "JBell",
                .FirstName = "Julia",
                .LastName = "Bell",
                .Email = "<EMAIL>",
                .AvatarUrl = "~/Content/Photo/Julia_Bell.jpg"
            }
    End Function
    Private Shared Function UserToApplicationUser(o As tm_user) As ApplicationUser
        Dim x As New ApplicationUser With {
            .Id = o.Id,
            .UserName = o.Username,
            .FirstName = o.Username,
            .LastName = "",
            .Email = o.Email,
            .AvatarUrl = "~/Content/Photo/User.png",
            .PermisionArea = o.PermisionArea,
            .Cabang_id = o.Cabang_id,
            .Area_id = o.Area_id,
            .AreaIdPermisionList = o.tm_user_area.Select(Function(s) s.Area_id).ToList
        }
        Return x
    End Function

End Class

