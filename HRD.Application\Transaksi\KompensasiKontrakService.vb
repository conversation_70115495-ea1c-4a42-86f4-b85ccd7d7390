﻿Imports HRD.Application
Imports HRD.Domain

Public Class KompensasiKontrakService
    Implements IKompensasiKontrakService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _myFunction As IMyFunction

    Public Sub New(unitOfWork As IUnitOfWork, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        _myFunction = myFunction
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements IKompensasiKontrakService.GetQueryableAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_kompensasi_kontrak)().TableNoTracking
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IKompensasiKontrakService.GetByIdAsync
        Try
            Dim result = _unitOfWork.Repository(Of tr_kompensasi_kontrak)().TableNoTracking.Where(Function(x) x.Id = Id).FirstOrDefault
            Return ResponseModel.SuccessResponse("Data berhasil diambil", result)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function UpsertAsync(TEntity As tr_kompensasi_kontrak) As Task(Of ResponseModel) Implements IKompensasiKontrakService.UpsertAsync
        _unitOfWork.BeginTransaction()
        Try
            ' Validate input
            Dim validationResult = ValidateKompensasi(TEntity)
            If Not validationResult.Success Then
                Return validationResult
            End If

            If TEntity.Cabang_id = Nothing Then
                ' Handle multi-branch compensation
                Await HandleMultiBranchKompensasi(TEntity)
            Else
                ' Handle single branch compensation
                Await HandleSingleBranchKompensasi(TEntity)
            End If

            _unitOfWork.Save()
            _unitOfWork.CommitTransaction()

            Return ResponseModel.SuccessResponse("Data berhasil disimpan", Nothing)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            '_errorLog.LogError(NameOf(UpsertAsync), ex.Message)
            Return ResponseModel.FailureResponse(ex.Message)
        End Try

    End Function

    Private Function ValidateKompensasi(entity As tr_kompensasi_kontrak) As ResponseModel
        If entity Is Nothing Then
            Return ResponseModel.FailureResponse("Data kompensasi tidak valid")
        End If

        If entity.Area_id <= 0 Then
            Return ResponseModel.FailureResponse("Area harus dipilih")
        End If

        If entity.Tanggal = DateTime.MinValue Then
            Return ResponseModel.FailureResponse("Tanggal harus diisi")
        End If

        Return ResponseModel.SuccessResponse("Validate Sukses", entity)
    End Function

    Private Async Function HandleMultiBranchKompensasi(entity As tr_kompensasi_kontrak) As Task
        ' Get area with branches
        Dim area = _myFunction.tm_area(entity.Area_id)

        If area Is Nothing Then
            Throw New Exception("Area tidak ditemukan")
        End If

        ' Create list for batch insert
        Dim kompensasiList = New List(Of tr_kompensasi_kontrak)

        For Each cabang In area.tm_cabang
            Dim kompensasi = Await CreateKompensasi(entity, cabang.Id)
            kompensasi.Total = kompensasi.tr_kompensasi_kontrak_line.Sum(Function(f) f.NilaiKompensasi)
            kompensasiList.Add(kompensasi)
        Next

        ' Batch insert for better performance
        If kompensasiList.Any() Then
            Await _unitOfWork.Repository(Of tr_kompensasi_kontrak)().AddRangeAsync(kompensasiList)
        End If
    End Function

    Private Async Function HandleSingleBranchKompensasi(entity As tr_kompensasi_kontrak) As Task
        Dim kompensasi = Await CreateKompensasi(entity, entity.Cabang_id)

        If kompensasi.Id = 0 Then
            Await _unitOfWork.Repository(Of tr_kompensasi_kontrak)().AddAsync(kompensasi)
        Else
            ' Get existing data
            Dim existing = _unitOfWork.Repository(Of tr_kompensasi_kontrak)().TableNoTracking.Where(Function(x) x.Id = kompensasi.Id).FirstOrDefault

            If existing Is Nothing Then
                Throw New Exception("Data tidak ditemukan")
            End If

            ' Update only changed properties
            UpdateKompensasi(existing, kompensasi)
            existing.Total = kompensasi.tr_kompensasi_kontrak_line.Sum(Function(f) f.NilaiKompensasi)
            _unitOfWork.Repository(Of tr_kompensasi_kontrak)().Update(existing)
        End If
    End Function

    Private Async Function CreateKompensasi(source As tr_kompensasi_kontrak, cabangId As Integer) As Task(Of tr_kompensasi_kontrak)
        Try
            Dim kompensasi = tr_kompensasi_kontrak.Create(source)
            kompensasi.Cabang_id = cabangId

            If kompensasi.Id = 0 Then
                ' Set properties for new record
                kompensasi.NoKompensasi = GetNoKompensasi(cabangId)
                kompensasi.Periode = source.PeriodeDate.ToString("yyyy-MM")
                kompensasi.Status = "DRAFT"
            Else
                ' Set properties for existing record
            End If

            Dim karyawanDataDinas = _unitOfWork.Repository(Of tm_karyawan_datadinas)() _
                                        .TableNoTracking _
                                        .Where(Function(x) x.Cabang_id = cabangId AndAlso
                                                           x.TglAkhirKontrak.HasValue AndAlso
                                                           x.TglAkhirKontrak.Value.Month = source.PeriodeDate.Month AndAlso
                                                           x.TglAkhirKontrak.Value.Year = source.PeriodeDate.Year) _
                                        .OrderByDescending(Function(x) x.Id).GroupBy(Function(x) x.Karyawan_id).Select(Function(g) g.FirstOrDefault).ToList()


            '        Dim idKaryawans = _unitOfWork.Repository(Of tm_karyawan_datadinas)() _
            '.TableNoTracking _
            '.Where(Function(x) x.Cabang_id = cabangId AndAlso
            '                   x.TglAkhirKontrak.HasValue AndAlso
            '                   x.TglAkhirKontrak.Value.Month = source.PeriodeDate.Month AndAlso
            '                   x.TglAkhirKontrak.Value.Year = source.PeriodeDate.Year) _
            '.Select(Function(g) g.Karyawan_id) _
            '.AsEnumerable.Distinct.ToList

            '        Dim karyawanHabisKontrak = _unitOfWork.Repository(Of tm_karyawan)() _
            '            .TableNoTracking _
            '            .Where(Function(x) idKaryawans.Contains(x.Id)).ToList

            '        Dim os = karyawanHabisKontrak.Distinct

            For Each x In karyawanDataDinas
                Dim line = Await CreateKompensasiLine(x)
                If line IsNot Nothing Then
                    kompensasi.tr_kompensasi_kontrak_line.Add(line)
                End If
            Next

            Return kompensasi
        Catch ex As Exception
            Throw New Exception($"Error creating kompensasi for cabangId {cabangId}: {ex.Message}", ex)
        End Try
    End Function


    Private Sub UpdateKompensasi(existing As tr_kompensasi_kontrak, updated As tr_kompensasi_kontrak)
        ' Update only if changed
        If existing.Tanggal <> updated.Tanggal Then
            existing.Tanggal = updated.Tanggal
            existing.Periode = updated.Tanggal.ToString("yyyy-MM")
        End If

        existing.Keterangan = updated.Keterangan

    End Sub
    Private Async Function CreateKompensasiLine(dn As tm_karyawan_datadinas) As Task(Of tr_kompensasi_kontrak_line)

        Dim nilaiKompensasi = Await HitungKompensasi(dn)
        Dim o = tr_kompensasi_kontrak_line.Create(0, dn.Karyawan_id, 0, nilaiKompensasi.Output.LamaKerja, nilaiKompensasi.Output.NilaiKompensasi, dn.TglAkhirKontrak, dn.TglAwalKontrak)

        Return o
    End Function
    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IKompensasiKontrakService.DeleteAsync
        Try
            Await _unitOfWork.Repository(Of tr_kompensasi_kontrak).DeleteAsync(Id)
            Await _unitOfWork.Repository(Of tr_kompensasi_kontrak_line).Delete(Function(d) d.Kompensasi_id = Id)
            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse("Data berhasil dihapus", Id)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function

    Public Async Function PostingAsync(TEntity As tr_kompensasi_kontrak) As Task(Of ResponseModel) Implements IKompensasiKontrakService.PostingAsync
        _unitOfWork.BeginTransaction()

        Try
            Dim kompensasi = Await _unitOfWork.Repository(Of tr_kompensasi_kontrak).Get(TEntity.Id)


            If kompensasi Is Nothing Then
                Return ResponseModel.FailureResponse("Data tidak ditemukan")
            End If

            If kompensasi.Status <> "DRAFT" Then
                Return ResponseModel.FailureResponse("Status dokumen harus DRAFT")
            End If

            ' Update status kompensasi
            kompensasi.Status = "POSTED"
            kompensasi.PostedBy = TEntity.PostedBy
            kompensasi.PostedDate = TEntity.PostedDate
            kompensasi.Posted = True
            ' Update status karyawan
            'kompensasi.tm_karyawan.Status = "NONAKTIF"
            'kompensasi.tm_karyawan.TanggalKeluar = kompensasi.TanggalAkhirKontrak

            _unitOfWork.Save()

            _unitOfWork.CommitTransaction()

            Return ResponseModel.SuccessResponse("Posting berhasil", kompensasi)
        Catch ex As Exception
            _unitOfWork.RollbackTransaction()
            Return ResponseModel.FailureResponse(ex.Message)
        End Try

    End Function

    Public Function VoidAsync(Id As Integer, Reason As String) As Task(Of ResponseModel) Implements IKompensasiKontrakService.VoidAsync
        Throw New NotImplementedException()
    End Function

    Public Async Function HitungKompensasi(dn As tm_karyawan_datadinas) As Task(Of ResponseModel) Implements IKompensasiKontrakService.HitungKompensasi
        Try
            ' Get data karyawan
            'Dim karyawan = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking.Where(Function(x) x.Id = KaryawanId).FirstOrDefault

            If dn Is Nothing Then
                Return ResponseModel.FailureResponse("Data Dinas Karyawan tidak ditemukan")
            End If

            ' Hitung lama kerja dalam bulan
            Dim lamaKerja = DateDiff(DateInterval.Month, dn.TglAwalKontrak.Value, dn.TglAkhirKontrak.Value)

            ' Hitung nilai kompensasi
            ' Rumus: (Gaji Pokok + Tunjangan Tetap) x Faktor Pengali
            Dim gajiPokok = dn.Pd_GajiPokok
            Dim tunjanganTetap = dn.Pd_T_Jabatan + dn.Pd_T_Susu + dn.Pd_T_Kontrak
            Dim faktorPengali = GetFaktorPengali(lamaKerja)

            Dim nilaiKompensasi = (gajiPokok + tunjanganTetap) * faktorPengali

            Return ResponseModel.SuccessResponse("", New With {
                .LamaKerja = lamaKerja,
                .NilaiKompensasi = nilaiKompensasi
            })
        Catch ex As Exception
            Return ResponseModel.FailureResponse(ex.Message)
        End Try
    End Function
    Private Function GetFaktorPengali(lamaKerja As Integer) As Decimal
        Return 1
        ' Contoh implementasi faktor pengali
        ' Bisa disesuaikan dengan kebijakan perusahaan
        If lamaKerja <= 12 Then
            Return 1
        ElseIf lamaKerja <= 24 Then
            Return 1.5D
        ElseIf lamaKerja <= 36 Then
            Return 2
        Else
            Return 2.5D
        End If
    End Function


    ' Implementasi method lainnya...


    ' Alternative implementation with custom format
    Private Function GetNoKompensasi(cabang_id As String, Optional format As String = "KK") As String
        Try
            ' Get current date components
            Dim currentDate As DateTime = DateTime.Now
            Dim year As String = currentDate.ToString("yy")
            Dim month As String = currentDate.ToString("MM")

            ' Get company/branch code if needed
            Dim branchCode As String = _myFunction.tm_cabang(cabang_id).KodeCabang

            ' Get last document number for current month and format
            Dim prefix As String = $"{format}/{branchCode}/{year}/{month}/"

            Dim lastDoc = _unitOfWork.Repository(Of tr_kompensasi_kontrak)() _
            .TableNoTracking _
            .Where(Function(x) x.NoKompensasi.StartsWith(prefix)) _
            .OrderByDescending(Function(x) x.NoKompensasi).Select(Function(s) s.NoKompensasi) _
            .FirstOrDefault()

            ' Initialize running number
            Dim runningNumber As Integer = 1

            If lastDoc IsNot Nothing Then
                ' Extract running number from last document
                Dim lastNumber As String = lastDoc.Split("/"c).Last()
                If Integer.TryParse(lastNumber, runningNumber) Then
                    runningNumber += 1
                End If
            End If

            ' Format: KK/BRANCH/YY/MM/0001
            Return $"{prefix}{runningNumber.ToString("0000")}"

        Catch ex As Exception
            ' Log error
            '_errorLog.LogError(NameOf(GetNoKompensasi), ex.Message)

            ' Generate fallback number if needed
            Return $"{format}/{DateTime.Now.ToString("yyMMddHHmmss")}"
        End Try
    End Function

    ' Extension method untuk validasi nomor dokumen
    Public Function IsValidDocumentNumber(docNumber As String) As Boolean
        Try
            ' Basic validation
            If String.IsNullOrEmpty(docNumber) Then Return False

            ' Check format
            Dim parts = docNumber.Split("/"c)
            If parts.Length <> 4 AndAlso parts.Length <> 5 Then Return False

            ' Validate prefix
            If parts(0) <> "KK" Then Return False

            ' Validate year (2 digits)
            Dim year As Integer
            If Not Integer.TryParse(parts(1), year) OrElse year < 0 OrElse year > 99 Then Return False

            ' Validate month (01-12)
            Dim month As Integer
            If Not Integer.TryParse(parts(2), month) OrElse month < 1 OrElse month > 12 Then Return False

            ' Validate running number (4 digits)
            Dim number As Integer
            If Not Integer.TryParse(parts.Last(), number) OrElse number < 1 OrElse number > 9999 Then Return False

            Return True

        Catch ex As Exception
            Return False
        End Try
    End Function
End Class