﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Public Class rpt_slip_gaji
    Inherits DevExpress.XtraReports.UI.XtraReport

    'XtraReport overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Designer
    'It can be modified using the Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(rpt_slip_gaji))
        Me.TopMargin = New DevExpress.XtraReports.UI.TopMarginBand()
        Me.BottomMargin = New DevExpress.XtraReports.UI.BottomMarginBand()
        Me.Detail = New DevExpress.XtraReports.UI.DetailBand()
        Me.DetailReport = New DevExpress.XtraReports.UI.DetailReportBand()
        Me.Detail1 = New DevExpress.XtraReports.UI.DetailBand()
        Me.XrTable4 = New DevExpress.XtraReports.UI.XRTable()
        Me.XrTableRow44 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell88 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow45 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell93 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell96 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow8 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell25 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell26 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow47 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell122 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell126 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow48 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell138 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell142 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow50 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell156 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell157 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow51 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell160 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell161 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow52 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell164 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell165 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow7 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell22 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell23 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow19 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell70 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell71 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow54 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell171 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell172 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow55 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell175 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell176 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow56 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell179 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell180 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow2 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell10 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell11 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTable3 = New DevExpress.XtraReports.UI.XRTable()
        Me.XrTableRow25 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell84 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow26 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell86 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell87 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow27 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell90 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell91 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow28 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell94 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell95 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell97 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow29 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell98 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell99 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell101 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow30 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell102 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell103 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow31 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell105 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell106 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell108 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow32 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell109 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell110 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell112 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow33 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell113 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell114 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell116 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow34 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell117 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell118 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow36 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell124 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell125 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell127 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow37 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell128 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell129 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell131 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow38 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell132 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell133 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell135 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow16 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell19 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell65 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell66 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow17 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell67 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell68 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow4 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell12 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell24 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow18 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell57 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell60 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTable2 = New DevExpress.XtraReports.UI.XRTable()
        Me.XrTableRow6 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell16 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell20 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell21 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell18 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow9 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell27 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell28 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell29 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell30 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow10 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell31 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell32 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell33 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell34 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow14 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell17 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell49 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow11 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.txt_terbilang = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow12 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell36 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell41 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow13 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell35 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell44 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell52 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell45 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell37 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell43 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell38 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell54 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell46 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow15 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell48 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell42 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell51 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell40 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell55 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell50 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell39 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTable1 = New DevExpress.XtraReports.UI.XRTable()
        Me.XrTableRow3 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell7 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell8 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell9 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell56 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell59 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell62 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow1 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell1 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell2 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell3 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell4 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell5 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell6 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableRow5 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell13 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell14 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell15 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell58 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell61 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell64 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.ObjectDataSource1 = New DevExpress.DataAccess.ObjectBinding.ObjectDataSource(Me.components)
        Me.fSisaPinjaman = New DevExpress.XtraReports.UI.CalculatedField()
        Me.XrControlStyle2 = New DevExpress.XtraReports.UI.XRControlStyle()
        Me.fTerbilang = New DevExpress.XtraReports.UI.CalculatedField()
        Me.PageHeader = New DevExpress.XtraReports.UI.PageHeaderBand()
        Me.XrLabel3 = New DevExpress.XtraReports.UI.XRLabel()
        Me.XrLabel2 = New DevExpress.XtraReports.UI.XRLabel()
        Me.XrLabel1 = New DevExpress.XtraReports.UI.XRLabel()
        Me.XrPictureBox2 = New DevExpress.XtraReports.UI.XRPictureBox()
        Me.XrPictureBox1 = New DevExpress.XtraReports.UI.XRPictureBox()
        Me.PageFooter = New DevExpress.XtraReports.UI.PageFooterBand()
        Me.XrRichText1 = New DevExpress.XtraReports.UI.XRRichText()
        Me.XrLine1 = New DevExpress.XtraReports.UI.XRLine()
        Me.XrLabel4 = New DevExpress.XtraReports.UI.XRLabel()
        CType(Me.XrTable4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XrTable3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XrTable2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XrTable1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ObjectDataSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XrRichText1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'TopMargin
        '
        Me.TopMargin.HeightF = 50.0!
        Me.TopMargin.Name = "TopMargin"
        '
        'BottomMargin
        '
        Me.BottomMargin.HeightF = 50.0!
        Me.BottomMargin.Name = "BottomMargin"
        '
        'Detail
        '
        Me.Detail.HeightF = 0!
        Me.Detail.Name = "Detail"
        '
        'DetailReport
        '
        Me.DetailReport.Bands.AddRange(New DevExpress.XtraReports.UI.Band() {Me.Detail1})
        Me.DetailReport.DataMember = "tr_gaji_line"
        Me.DetailReport.DataSource = Me.ObjectDataSource1
        Me.DetailReport.Level = 0
        Me.DetailReport.Name = "DetailReport"
        '
        'Detail1
        '
        Me.Detail1.Controls.AddRange(New DevExpress.XtraReports.UI.XRControl() {Me.XrTable4, Me.XrTable3, Me.XrTable2, Me.XrTable1})
        Me.Detail1.HeightF = 951.1667!
        Me.Detail1.Name = "Detail1"
        Me.Detail1.PageBreak = DevExpress.XtraReports.UI.PageBreak.BeforeBandExceptFirstEntry
        '
        'XrTable4
        '
        Me.XrTable4.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTable4.LocationFloat = New DevExpress.Utils.PointFloat(381.248!, 45.50001!)
        Me.XrTable4.Name = "XrTable4"
        Me.XrTable4.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrTable4.Rows.AddRange(New DevExpress.XtraReports.UI.XRTableRow() {Me.XrTableRow44, Me.XrTableRow45, Me.XrTableRow8, Me.XrTableRow47, Me.XrTableRow48, Me.XrTableRow50, Me.XrTableRow51, Me.XrTableRow52, Me.XrTableRow7, Me.XrTableRow19, Me.XrTableRow54, Me.XrTableRow55, Me.XrTableRow56, Me.XrTableRow2})
        Me.XrTable4.SizeF = New System.Drawing.SizeF(345.752!, 283.0009!)
        Me.XrTable4.StylePriority.UseFont = False
        '
        'XrTableRow44
        '
        Me.XrTableRow44.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell88})
        Me.XrTableRow44.Name = "XrTableRow44"
        Me.XrTableRow44.Weight = 1.0R
        '
        'XrTableCell88
        '
        Me.XrTableCell88.Borders = CType((DevExpress.XtraPrinting.BorderSide.Top Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell88.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell88.Multiline = True
        Me.XrTableCell88.Name = "XrTableCell88"
        Me.XrTableCell88.StylePriority.UseBorders = False
        Me.XrTableCell88.StylePriority.UseFont = False
        Me.XrTableCell88.Text = "POTONGAN"
        Me.XrTableCell88.Weight = 3.0R
        '
        'XrTableRow45
        '
        Me.XrTableRow45.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell93, Me.XrTableCell96})
        Me.XrTableRow45.Name = "XrTableRow45"
        Me.XrTableRow45.Weight = 0.8R
        '
        'XrTableCell93
        '
        Me.XrTableCell93.Multiline = True
        Me.XrTableCell93.Name = "XrTableCell93"
        Me.XrTableCell93.Text = "BPJS KS PERUSAHAAN"
        Me.XrTableCell93.Weight = 1.9486683249183576R
        '
        'XrTableCell96
        '
        Me.XrTableCell96.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JPK]")})
        Me.XrTableCell96.Multiline = True
        Me.XrTableCell96.Name = "XrTableCell96"
        Me.XrTableCell96.StylePriority.UseTextAlignment = False
        Me.XrTableCell96.Text = "XrTableCell23"
        Me.XrTableCell96.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell96.TextFormatString = "{0:n0}"
        Me.XrTableCell96.Weight = 1.0513316750816422R
        '
        'XrTableRow8
        '
        Me.XrTableRow8.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell25, Me.XrTableCell26})
        Me.XrTableRow8.Name = "XrTableRow8"
        Me.XrTableRow8.Weight = 0.79999999999999993R
        '
        'XrTableCell25
        '
        Me.XrTableCell25.Multiline = True
        Me.XrTableCell25.Name = "XrTableCell25"
        Me.XrTableCell25.Text = "BPJS KS KARYAWAN"
        Me.XrTableCell25.Weight = 1.9486683249183576R
        '
        'XrTableCell26
        '
        Me.XrTableCell26.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_K_JPK]")})
        Me.XrTableCell26.Multiline = True
        Me.XrTableCell26.Name = "XrTableCell26"
        Me.XrTableCell26.StylePriority.UseTextAlignment = False
        Me.XrTableCell26.Text = "XrTableCell26"
        Me.XrTableCell26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell26.TextFormatString = "{0:n0}"
        Me.XrTableCell26.Weight = 1.0513316750816422R
        '
        'XrTableRow47
        '
        Me.XrTableRow47.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell122, Me.XrTableCell126})
        Me.XrTableRow47.Name = "XrTableRow47"
        Me.XrTableRow47.Weight = 0.79999999999999993R
        '
        'XrTableCell122
        '
        Me.XrTableCell122.Multiline = True
        Me.XrTableCell122.Name = "XrTableCell122"
        Me.XrTableCell122.Text = "BPJS JP PERUSAHAAN"
        Me.XrTableCell122.Weight = 1.9486683249183576R
        '
        'XrTableCell126
        '
        Me.XrTableCell126.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JP]")})
        Me.XrTableCell126.Multiline = True
        Me.XrTableCell126.Name = "XrTableCell126"
        Me.XrTableCell126.StylePriority.UseTextAlignment = False
        Me.XrTableCell126.Text = "XrTableCell31"
        Me.XrTableCell126.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell126.TextFormatString = "{0:n0}"
        Me.XrTableCell126.Weight = 1.0513316750816422R
        '
        'XrTableRow48
        '
        Me.XrTableRow48.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell138, Me.XrTableCell142})
        Me.XrTableRow48.Name = "XrTableRow48"
        Me.XrTableRow48.Weight = 0.8R
        '
        'XrTableCell138
        '
        Me.XrTableCell138.Multiline = True
        Me.XrTableCell138.Name = "XrTableCell138"
        Me.XrTableCell138.Text = "BPJS JP KARYAWAN"
        Me.XrTableCell138.Weight = 1.9486683249183576R
        '
        'XrTableCell142
        '
        Me.XrTableCell142.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_K_JP]")})
        Me.XrTableCell142.Multiline = True
        Me.XrTableCell142.Name = "XrTableCell142"
        Me.XrTableCell142.StylePriority.UseTextAlignment = False
        Me.XrTableCell142.Text = "XrTableCell35"
        Me.XrTableCell142.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell142.TextFormatString = "{0:n0}"
        Me.XrTableCell142.Weight = 1.0513316750816422R
        '
        'XrTableRow50
        '
        Me.XrTableRow50.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell156, Me.XrTableCell157})
        Me.XrTableRow50.Name = "XrTableRow50"
        Me.XrTableRow50.Weight = 0.8R
        '
        'XrTableCell156
        '
        Me.XrTableCell156.Multiline = True
        Me.XrTableCell156.Name = "XrTableCell156"
        Me.XrTableCell156.Text = "BPJS JKM"
        Me.XrTableCell156.Weight = 1.9486683249183576R
        '
        'XrTableCell157
        '
        Me.XrTableCell157.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JKM]")})
        Me.XrTableCell157.Multiline = True
        Me.XrTableCell157.Name = "XrTableCell157"
        Me.XrTableCell157.StylePriority.UseTextAlignment = False
        Me.XrTableCell157.Text = "XrTableCell43"
        Me.XrTableCell157.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell157.TextFormatString = "{0:n0}"
        Me.XrTableCell157.Weight = 1.0513316750816422R
        '
        'XrTableRow51
        '
        Me.XrTableRow51.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell160, Me.XrTableCell161})
        Me.XrTableRow51.Name = "XrTableRow51"
        Me.XrTableRow51.Weight = 0.80000000000000027R
        '
        'XrTableCell160
        '
        Me.XrTableCell160.Multiline = True
        Me.XrTableCell160.Name = "XrTableCell160"
        Me.XrTableCell160.Text = "BPJS JKK"
        Me.XrTableCell160.Weight = 1.9486683249183576R
        '
        'XrTableCell161
        '
        Me.XrTableCell161.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JKK]")})
        Me.XrTableCell161.Multiline = True
        Me.XrTableCell161.Name = "XrTableCell161"
        Me.XrTableCell161.StylePriority.UseTextAlignment = False
        Me.XrTableCell161.Text = "XrTableCell53"
        Me.XrTableCell161.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell161.TextFormatString = "{0:n0}"
        Me.XrTableCell161.Weight = 1.0513316750816422R
        '
        'XrTableRow52
        '
        Me.XrTableRow52.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell164, Me.XrTableCell165})
        Me.XrTableRow52.Name = "XrTableRow52"
        Me.XrTableRow52.Weight = 0.79999999999999982R
        '
        'XrTableCell164
        '
        Me.XrTableCell164.Multiline = True
        Me.XrTableCell164.Name = "XrTableCell164"
        Me.XrTableCell164.Text = "BPJS JHT PERUSAHAAN"
        Me.XrTableCell164.Weight = 1.9486683249183576R
        '
        'XrTableCell165
        '
        Me.XrTableCell165.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JHT]")})
        Me.XrTableCell165.Multiline = True
        Me.XrTableCell165.Name = "XrTableCell165"
        Me.XrTableCell165.StylePriority.UseTextAlignment = False
        Me.XrTableCell165.Text = "XrTableCell57"
        Me.XrTableCell165.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell165.TextFormatString = "{0:n0}"
        Me.XrTableCell165.Weight = 1.0513316750816422R
        '
        'XrTableRow7
        '
        Me.XrTableRow7.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell22, Me.XrTableCell23})
        Me.XrTableRow7.Name = "XrTableRow7"
        Me.XrTableRow7.Weight = 0.79999999999999982R
        '
        'XrTableCell22
        '
        Me.XrTableCell22.Multiline = True
        Me.XrTableCell22.Name = "XrTableCell22"
        Me.XrTableCell22.Text = "BPJS JHT KARYAWAN"
        Me.XrTableCell22.Weight = 1.9486683249183576R
        '
        'XrTableCell23
        '
        Me.XrTableCell23.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_K_JHT]")})
        Me.XrTableCell23.Multiline = True
        Me.XrTableCell23.Name = "XrTableCell23"
        Me.XrTableCell23.StylePriority.UseTextAlignment = False
        Me.XrTableCell23.Text = "XrTableCell23"
        Me.XrTableCell23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell23.TextFormatString = "{0:n0}"
        Me.XrTableCell23.Weight = 1.0513316750816422R
        '
        'XrTableRow19
        '
        Me.XrTableRow19.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell70, Me.XrTableCell71})
        Me.XrTableRow19.Name = "XrTableRow19"
        Me.XrTableRow19.Weight = 0.79999999999999982R
        '
        'XrTableCell70
        '
        Me.XrTableCell70.Multiline = True
        Me.XrTableCell70.Name = "XrTableCell70"
        Me.XrTableCell70.Text = "POT.ABSEN"
        Me.XrTableCell70.Weight = 1.9486683249183576R
        '
        'XrTableCell71
        '
        Me.XrTableCell71.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pot_Alpha]")})
        Me.XrTableCell71.Multiline = True
        Me.XrTableCell71.Name = "XrTableCell71"
        Me.XrTableCell71.StylePriority.UseTextAlignment = False
        Me.XrTableCell71.Text = "XrTableCell71"
        Me.XrTableCell71.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell71.TextFormatString = "{0:n0}"
        Me.XrTableCell71.Weight = 1.0513316750816422R
        '
        'XrTableRow54
        '
        Me.XrTableRow54.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell171, Me.XrTableCell172})
        Me.XrTableRow54.Name = "XrTableRow54"
        Me.XrTableRow54.Weight = 0.8R
        '
        'XrTableCell171
        '
        Me.XrTableCell171.Multiline = True
        Me.XrTableCell171.Name = "XrTableCell171"
        Me.XrTableCell171.Text = "POT.SP"
        Me.XrTableCell171.Weight = 1.9486683249183576R
        '
        'XrTableCell172
        '
        Me.XrTableCell172.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_SP]")})
        Me.XrTableCell172.Multiline = True
        Me.XrTableCell172.Name = "XrTableCell172"
        Me.XrTableCell172.StylePriority.UseTextAlignment = False
        Me.XrTableCell172.Text = "XrTableCell77"
        Me.XrTableCell172.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell172.TextFormatString = "{0:n0}"
        Me.XrTableCell172.Weight = 1.0513316750816422R
        '
        'XrTableRow55
        '
        Me.XrTableRow55.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell175, Me.XrTableCell176})
        Me.XrTableRow55.Name = "XrTableRow55"
        Me.XrTableRow55.Weight = 0.79999999999999982R
        '
        'XrTableCell175
        '
        Me.XrTableCell175.Multiline = True
        Me.XrTableCell175.Name = "XrTableCell175"
        Me.XrTableCell175.Text = "KASBON"
        Me.XrTableCell175.Weight = 1.9486683249183576R
        '
        'XrTableCell176
        '
        Me.XrTableCell176.Multiline = True
        Me.XrTableCell176.Name = "XrTableCell176"
        Me.XrTableCell176.StylePriority.UseTextAlignment = False
        Me.XrTableCell176.Text = "0"
        Me.XrTableCell176.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell176.TextFormatString = "{0:n0}"
        Me.XrTableCell176.Weight = 1.0513316750816422R
        '
        'XrTableRow56
        '
        Me.XrTableRow56.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell179, Me.XrTableCell180})
        Me.XrTableRow56.Name = "XrTableRow56"
        Me.XrTableRow56.Weight = 0.79999999999999982R
        '
        'XrTableCell179
        '
        Me.XrTableCell179.Multiline = True
        Me.XrTableCell179.Name = "XrTableCell179"
        Me.XrTableCell179.Text = "PINJAMAN"
        Me.XrTableCell179.Weight = 1.9486683249183576R
        '
        'XrTableCell180
        '
        Me.XrTableCell180.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_Kasbon]")})
        Me.XrTableCell180.Multiline = True
        Me.XrTableCell180.Name = "XrTableCell180"
        Me.XrTableCell180.StylePriority.UseTextAlignment = False
        Me.XrTableCell180.Text = "XrTableCell69"
        Me.XrTableCell180.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell180.TextFormatString = "{0:n0}"
        Me.XrTableCell180.Weight = 1.0513316750816422R
        '
        'XrTableRow2
        '
        Me.XrTableRow2.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell10, Me.XrTableCell11})
        Me.XrTableRow2.Name = "XrTableRow2"
        Me.XrTableRow2.Weight = 0.79999999999999982R
        '
        'XrTableCell10
        '
        Me.XrTableCell10.Multiline = True
        Me.XrTableCell10.Name = "XrTableCell10"
        Me.XrTableCell10.Text = "SER. PEKERJA"
        Me.XrTableCell10.Weight = 1.9486683249183576R
        '
        'XrTableCell11
        '
        Me.XrTableCell11.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_SerPekerja]")})
        Me.XrTableCell11.Multiline = True
        Me.XrTableCell11.Name = "XrTableCell11"
        Me.XrTableCell11.StylePriority.UseTextAlignment = False
        Me.XrTableCell11.Text = "XrTableCell11"
        Me.XrTableCell11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell11.TextFormatString = "{0:n0}"
        Me.XrTableCell11.Weight = 1.0513316750816422R
        '
        'XrTable3
        '
        Me.XrTable3.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTable3.LocationFloat = New DevExpress.Utils.PointFloat(0!, 45.50001!)
        Me.XrTable3.Name = "XrTable3"
        Me.XrTable3.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrTable3.Rows.AddRange(New DevExpress.XtraReports.UI.XRTableRow() {Me.XrTableRow25, Me.XrTableRow26, Me.XrTableRow27, Me.XrTableRow28, Me.XrTableRow29, Me.XrTableRow30, Me.XrTableRow31, Me.XrTableRow32, Me.XrTableRow33, Me.XrTableRow34, Me.XrTableRow36, Me.XrTableRow37, Me.XrTableRow38, Me.XrTableRow16, Me.XrTableRow17, Me.XrTableRow4, Me.XrTableRow18})
        Me.XrTable3.SizeF = New System.Drawing.SizeF(380.5!, 342.58!)
        Me.XrTable3.StylePriority.UseFont = False
        '
        'XrTableRow25
        '
        Me.XrTableRow25.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell84})
        Me.XrTableRow25.Name = "XrTableRow25"
        Me.XrTableRow25.Weight = 1.0R
        '
        'XrTableCell84
        '
        Me.XrTableCell84.Borders = CType((DevExpress.XtraPrinting.BorderSide.Top Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell84.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell84.Multiline = True
        Me.XrTableCell84.Name = "XrTableCell84"
        Me.XrTableCell84.StylePriority.UseBorders = False
        Me.XrTableCell84.StylePriority.UseFont = False
        Me.XrTableCell84.Text = "PENGHASILAN"
        Me.XrTableCell84.Weight = 2.5082530109557686R
        '
        'XrTableRow26
        '
        Me.XrTableRow26.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell86, Me.XrTableCell87})
        Me.XrTableRow26.Name = "XrTableRow26"
        Me.XrTableRow26.Weight = 0.8R
        '
        'XrTableCell86
        '
        Me.XrTableCell86.Multiline = True
        Me.XrTableCell86.Name = "XrTableCell86"
        Me.XrTableCell86.Text = "GAJI POKOK"
        Me.XrTableCell86.Weight = 1.5974594863265219R
        '
        'XrTableCell87
        '
        Me.XrTableCell87.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_GajiPokok]")})
        Me.XrTableCell87.Multiline = True
        Me.XrTableCell87.Name = "XrTableCell87"
        Me.XrTableCell87.StylePriority.UseTextAlignment = False
        Me.XrTableCell87.Text = "XrTableCell21"
        Me.XrTableCell87.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell87.TextFormatString = "{0:n0}"
        Me.XrTableCell87.Weight = 0.91079352462924668R
        '
        'XrTableRow27
        '
        Me.XrTableRow27.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell90, Me.XrTableCell91})
        Me.XrTableRow27.Name = "XrTableRow27"
        Me.XrTableRow27.Weight = 0.79999999999999993R
        '
        'XrTableCell90
        '
        Me.XrTableCell90.Multiline = True
        Me.XrTableCell90.Name = "XrTableCell90"
        Me.XrTableCell90.Text = "T. PROB"
        Me.XrTableCell90.Weight = 1.5974594863265217R
        '
        'XrTableCell91
        '
        Me.XrTableCell91.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Probation]")})
        Me.XrTableCell91.Multiline = True
        Me.XrTableCell91.Name = "XrTableCell91"
        Me.XrTableCell91.StylePriority.UseTextAlignment = False
        Me.XrTableCell91.Text = "XrTableCell25"
        Me.XrTableCell91.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell91.TextFormatString = "{0:n0}"
        Me.XrTableCell91.Weight = 0.91079352462924668R
        '
        'XrTableRow28
        '
        Me.XrTableRow28.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell94, Me.XrTableCell95, Me.XrTableCell97})
        Me.XrTableRow28.Name = "XrTableRow28"
        Me.XrTableRow28.Weight = 0.79999999999999993R
        '
        'XrTableCell94
        '
        Me.XrTableCell94.Multiline = True
        Me.XrTableCell94.Name = "XrTableCell94"
        Me.XrTableCell94.Text = "U.LEMBUR"
        Me.XrTableCell94.Weight = 1.5974594863265217R
        '
        'XrTableCell95
        '
        Me.XrTableCell95.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_Lembur]")})
        Me.XrTableCell95.Multiline = True
        Me.XrTableCell95.Name = "XrTableCell95"
        Me.XrTableCell95.StylePriority.UseTextAlignment = False
        Me.XrTableCell95.Text = "XrTableCell29"
        Me.XrTableCell95.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell95.TextFormatString = "{0:n0}"
        Me.XrTableCell95.Weight = 0.9025405136734781R
        '
        'XrTableCell97
        '
        Me.XrTableCell97.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_SP]")})
        Me.XrTableCell97.Multiline = True
        Me.XrTableCell97.Name = "XrTableCell97"
        Me.XrTableCell97.StylePriority.UseTextAlignment = False
        Me.XrTableCell97.Text = "XrTableCell31"
        Me.XrTableCell97.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell97.TextFormatString = "{0:n0}"
        Me.XrTableCell97.Weight = 0.00825301095576858R
        '
        'XrTableRow29
        '
        Me.XrTableRow29.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell98, Me.XrTableCell99, Me.XrTableCell101})
        Me.XrTableRow29.Name = "XrTableRow29"
        Me.XrTableRow29.Weight = 0.8R
        '
        'XrTableCell98
        '
        Me.XrTableCell98.Multiline = True
        Me.XrTableCell98.Name = "XrTableCell98"
        Me.XrTableCell98.Text = "T.JABATAN"
        Me.XrTableCell98.Weight = 1.5974594863265217R
        '
        'XrTableCell99
        '
        Me.XrTableCell99.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Jabatan]")})
        Me.XrTableCell99.Multiline = True
        Me.XrTableCell99.Name = "XrTableCell99"
        Me.XrTableCell99.StylePriority.UseTextAlignment = False
        Me.XrTableCell99.Text = "XrTableCell33"
        Me.XrTableCell99.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell99.TextFormatString = "{0:n0}"
        Me.XrTableCell99.Weight = 0.9025405136734781R
        '
        'XrTableCell101
        '
        Me.XrTableCell101.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_SerPekerja]")})
        Me.XrTableCell101.Multiline = True
        Me.XrTableCell101.Name = "XrTableCell101"
        Me.XrTableCell101.StylePriority.UseTextAlignment = False
        Me.XrTableCell101.Text = "XrTableCell35"
        Me.XrTableCell101.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell101.TextFormatString = "{0:n0}"
        Me.XrTableCell101.Weight = 0.00825301095576858R
        '
        'XrTableRow30
        '
        Me.XrTableRow30.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell102, Me.XrTableCell103})
        Me.XrTableRow30.Name = "XrTableRow30"
        Me.XrTableRow30.Weight = 0.8R
        '
        'XrTableCell102
        '
        Me.XrTableCell102.Multiline = True
        Me.XrTableCell102.Name = "XrTableCell102"
        Me.XrTableCell102.Text = "T.MAKAN & TRANS"
        Me.XrTableCell102.Weight = 1.5974594863265217R
        '
        'XrTableCell103
        '
        Me.XrTableCell103.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Makan]+[Pd_T_Transport]")})
        Me.XrTableCell103.Multiline = True
        Me.XrTableCell103.Name = "XrTableCell103"
        Me.XrTableCell103.StylePriority.UseTextAlignment = False
        Me.XrTableCell103.Text = "XrTableCell37"
        Me.XrTableCell103.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell103.TextFormatString = "{0:n0}"
        Me.XrTableCell103.Weight = 0.91079352462924668R
        '
        'XrTableRow31
        '
        Me.XrTableRow31.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell105, Me.XrTableCell106, Me.XrTableCell108})
        Me.XrTableRow31.Name = "XrTableRow31"
        Me.XrTableRow31.Weight = 0.8R
        '
        'XrTableCell105
        '
        Me.XrTableCell105.Multiline = True
        Me.XrTableCell105.Name = "XrTableCell105"
        Me.XrTableCell105.Text = "T.HADIR"
        Me.XrTableCell105.Weight = 1.5974594863265217R
        '
        'XrTableCell106
        '
        Me.XrTableCell106.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_PremiHadir]")})
        Me.XrTableCell106.Multiline = True
        Me.XrTableCell106.Name = "XrTableCell106"
        Me.XrTableCell106.StylePriority.UseTextAlignment = False
        Me.XrTableCell106.Text = "XrTableCell41"
        Me.XrTableCell106.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell106.TextFormatString = "{0:n0}"
        Me.XrTableCell106.Weight = 0.9025405136734781R
        '
        'XrTableCell108
        '
        Me.XrTableCell108.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JHT]+[Pt_P_JKK]+[Pt_P_JKM]")})
        Me.XrTableCell108.Multiline = True
        Me.XrTableCell108.Name = "XrTableCell108"
        Me.XrTableCell108.StylePriority.UseTextAlignment = False
        Me.XrTableCell108.Text = "XrTableCell43"
        Me.XrTableCell108.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell108.TextFormatString = "{0:n0}"
        Me.XrTableCell108.Weight = 0.00825301095576858R
        '
        'XrTableRow32
        '
        Me.XrTableRow32.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell109, Me.XrTableCell110, Me.XrTableCell112})
        Me.XrTableRow32.Name = "XrTableRow32"
        Me.XrTableRow32.Weight = 0.80000000000000027R
        '
        'XrTableCell109
        '
        Me.XrTableCell109.Multiline = True
        Me.XrTableCell109.Name = "XrTableCell109"
        Me.XrTableCell109.Text = "T.SUSU"
        Me.XrTableCell109.Weight = 1.5974594863265217R
        '
        'XrTableCell110
        '
        Me.XrTableCell110.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Susu]")})
        Me.XrTableCell110.Multiline = True
        Me.XrTableCell110.Name = "XrTableCell110"
        Me.XrTableCell110.StylePriority.UseTextAlignment = False
        Me.XrTableCell110.Text = "XrTableCell51"
        Me.XrTableCell110.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell110.TextFormatString = "{0:n0}"
        Me.XrTableCell110.Weight = 0.9025405136734781R
        '
        'XrTableCell112
        '
        Me.XrTableCell112.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JPK]")})
        Me.XrTableCell112.Multiline = True
        Me.XrTableCell112.Name = "XrTableCell112"
        Me.XrTableCell112.StylePriority.UseTextAlignment = False
        Me.XrTableCell112.Text = "XrTableCell53"
        Me.XrTableCell112.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell112.TextFormatString = "{0:n0}"
        Me.XrTableCell112.Weight = 0.00825301095576858R
        '
        'XrTableRow33
        '
        Me.XrTableRow33.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell113, Me.XrTableCell114, Me.XrTableCell116})
        Me.XrTableRow33.Name = "XrTableRow33"
        Me.XrTableRow33.Weight = 0.79999999999999982R
        '
        'XrTableCell113
        '
        Me.XrTableCell113.Multiline = True
        Me.XrTableCell113.Name = "XrTableCell113"
        Me.XrTableCell113.Text = "T. KONTRAK"
        Me.XrTableCell113.Weight = 1.5974594863265217R
        '
        'XrTableCell114
        '
        Me.XrTableCell114.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Kontrak]")})
        Me.XrTableCell114.Multiline = True
        Me.XrTableCell114.Name = "XrTableCell114"
        Me.XrTableCell114.StylePriority.UseTextAlignment = False
        Me.XrTableCell114.Text = "XrTableCell55"
        Me.XrTableCell114.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell114.TextFormatString = "{0:n0}"
        Me.XrTableCell114.Weight = 0.9025405136734781R
        '
        'XrTableCell116
        '
        Me.XrTableCell116.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_P_JP]")})
        Me.XrTableCell116.Multiline = True
        Me.XrTableCell116.Name = "XrTableCell116"
        Me.XrTableCell116.StylePriority.UseTextAlignment = False
        Me.XrTableCell116.Text = "XrTableCell57"
        Me.XrTableCell116.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell116.TextFormatString = "{0:n0}"
        Me.XrTableCell116.Weight = 0.00825301095576858R
        '
        'XrTableRow34
        '
        Me.XrTableRow34.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell117, Me.XrTableCell118})
        Me.XrTableRow34.Name = "XrTableRow34"
        Me.XrTableRow34.Weight = 0.8R
        '
        'XrTableCell117
        '
        Me.XrTableCell117.Multiline = True
        Me.XrTableCell117.Name = "XrTableCell117"
        Me.XrTableCell117.Text = "T. PREMI ASSURANSI"
        Me.XrTableCell117.Weight = 1.5974594863265217R
        '
        'XrTableCell118
        '
        Me.XrTableCell118.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_PremiAss]")})
        Me.XrTableCell118.Multiline = True
        Me.XrTableCell118.Name = "XrTableCell118"
        Me.XrTableCell118.StylePriority.UseTextAlignment = False
        Me.XrTableCell118.Text = "XrTableCell59"
        Me.XrTableCell118.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell118.TextFormatString = "{0:n0}"
        Me.XrTableCell118.Weight = 0.91079352462924668R
        '
        'XrTableRow36
        '
        Me.XrTableRow36.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell124, Me.XrTableCell125, Me.XrTableCell127})
        Me.XrTableRow36.Name = "XrTableRow36"
        Me.XrTableRow36.Weight = 0.79999999999999982R
        '
        'XrTableCell124
        '
        Me.XrTableCell124.Multiline = True
        Me.XrTableCell124.Name = "XrTableCell124"
        Me.XrTableCell124.Text = "T.BPJS KS PERUSAHAAN"
        Me.XrTableCell124.Weight = 1.5974594863265217R
        '
        'XrTableCell125
        '
        Me.XrTableCell125.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_JPK]")})
        Me.XrTableCell125.Multiline = True
        Me.XrTableCell125.Name = "XrTableCell125"
        Me.XrTableCell125.StylePriority.UseTextAlignment = False
        Me.XrTableCell125.Text = "XrTableCell63"
        Me.XrTableCell125.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell125.TextFormatString = "{0:n0}"
        Me.XrTableCell125.Weight = 0.9025405136734781R
        '
        'XrTableCell127
        '
        Me.XrTableCell127.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_K_JPK]")})
        Me.XrTableCell127.Multiline = True
        Me.XrTableCell127.Name = "XrTableCell127"
        Me.XrTableCell127.StylePriority.UseTextAlignment = False
        Me.XrTableCell127.Text = "XrTableCell65"
        Me.XrTableCell127.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell127.TextFormatString = "{0:n0}"
        Me.XrTableCell127.Weight = 0.00825301095576858R
        '
        'XrTableRow37
        '
        Me.XrTableRow37.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell128, Me.XrTableCell129, Me.XrTableCell131})
        Me.XrTableRow37.Name = "XrTableRow37"
        Me.XrTableRow37.Weight = 0.79999999999999982R
        '
        'XrTableCell128
        '
        Me.XrTableCell128.Multiline = True
        Me.XrTableCell128.Name = "XrTableCell128"
        Me.XrTableCell128.Text = "T.BPJS JP PERUSAHAAN"
        Me.XrTableCell128.Weight = 1.5974594863265217R
        '
        'XrTableCell129
        '
        Me.XrTableCell129.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_JP]")})
        Me.XrTableCell129.Multiline = True
        Me.XrTableCell129.Name = "XrTableCell129"
        Me.XrTableCell129.StylePriority.UseTextAlignment = False
        Me.XrTableCell129.Text = "XrTableCell67"
        Me.XrTableCell129.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell129.TextFormatString = "{0:n0}"
        Me.XrTableCell129.Weight = 0.9025405136734781R
        '
        'XrTableCell131
        '
        Me.XrTableCell131.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_K_JP]")})
        Me.XrTableCell131.Multiline = True
        Me.XrTableCell131.Name = "XrTableCell131"
        Me.XrTableCell131.StylePriority.UseTextAlignment = False
        Me.XrTableCell131.Text = "XrTableCell69"
        Me.XrTableCell131.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell131.TextFormatString = "{0:n0}"
        Me.XrTableCell131.Weight = 0.00825301095576858R
        '
        'XrTableRow38
        '
        Me.XrTableRow38.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell132, Me.XrTableCell133, Me.XrTableCell135})
        Me.XrTableRow38.Name = "XrTableRow38"
        Me.XrTableRow38.Weight = 0.80000000000000027R
        '
        'XrTableCell132
        '
        Me.XrTableCell132.Multiline = True
        Me.XrTableCell132.Name = "XrTableCell132"
        Me.XrTableCell132.Text = "T.BPJS JKM"
        Me.XrTableCell132.Weight = 1.5974594863265217R
        '
        'XrTableCell133
        '
        Me.XrTableCell133.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_JKM]")})
        Me.XrTableCell133.Multiline = True
        Me.XrTableCell133.Name = "XrTableCell133"
        Me.XrTableCell133.StylePriority.UseTextAlignment = False
        Me.XrTableCell133.Text = "XrTableCell71"
        Me.XrTableCell133.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell133.TextFormatString = "{0:n0}"
        Me.XrTableCell133.Weight = 0.9025405136734781R
        '
        'XrTableCell135
        '
        Me.XrTableCell135.Multiline = True
        Me.XrTableCell135.Name = "XrTableCell135"
        Me.XrTableCell135.StylePriority.UseTextAlignment = False
        Me.XrTableCell135.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell135.Weight = 0.00825301095576858R
        '
        'XrTableRow16
        '
        Me.XrTableRow16.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell19, Me.XrTableCell65, Me.XrTableCell66})
        Me.XrTableRow16.Name = "XrTableRow16"
        Me.XrTableRow16.Weight = 0.80000000000000027R
        '
        'XrTableCell19
        '
        Me.XrTableCell19.Multiline = True
        Me.XrTableCell19.Name = "XrTableCell19"
        Me.XrTableCell19.Text = "T.BPJS JKK"
        Me.XrTableCell19.Weight = 1.5974594863265217R
        '
        'XrTableCell65
        '
        Me.XrTableCell65.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_JKK]")})
        Me.XrTableCell65.Multiline = True
        Me.XrTableCell65.Name = "XrTableCell65"
        Me.XrTableCell65.StylePriority.UseTextAlignment = False
        Me.XrTableCell65.Text = "XrTableCell65"
        Me.XrTableCell65.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell65.TextFormatString = "{0:n0}"
        Me.XrTableCell65.Weight = 0.9025405136734781R
        '
        'XrTableCell66
        '
        Me.XrTableCell66.Multiline = True
        Me.XrTableCell66.Name = "XrTableCell66"
        Me.XrTableCell66.StylePriority.UseTextAlignment = False
        Me.XrTableCell66.Text = "XrTableCell66"
        Me.XrTableCell66.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell66.Weight = 0.00825301095576858R
        '
        'XrTableRow17
        '
        Me.XrTableRow17.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell67, Me.XrTableCell68})
        Me.XrTableRow17.Name = "XrTableRow17"
        Me.XrTableRow17.Weight = 0.80000000000000027R
        '
        'XrTableCell67
        '
        Me.XrTableCell67.Multiline = True
        Me.XrTableCell67.Name = "XrTableCell67"
        Me.XrTableCell67.Text = "T.BPJS JHT PERUSAHAAN"
        Me.XrTableCell67.Weight = 1.5974594863265217R
        '
        'XrTableCell68
        '
        Me.XrTableCell68.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_JHT]")})
        Me.XrTableCell68.Multiline = True
        Me.XrTableCell68.Name = "XrTableCell68"
        Me.XrTableCell68.StylePriority.UseTextAlignment = False
        Me.XrTableCell68.Text = "XrTableCell68"
        Me.XrTableCell68.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell68.TextFormatString = "{0:n0}"
        Me.XrTableCell68.Weight = 0.91079352462924668R
        '
        'XrTableRow4
        '
        Me.XrTableRow4.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell12, Me.XrTableCell24})
        Me.XrTableRow4.Name = "XrTableRow4"
        Me.XrTableRow4.Weight = 0.80000000000000027R
        '
        'XrTableCell12
        '
        Me.XrTableCell12.Multiline = True
        Me.XrTableCell12.Name = "XrTableCell12"
        Me.XrTableCell12.Text = "T. INSENTIF"
        Me.XrTableCell12.Weight = 1.5974594863265217R
        '
        'XrTableCell24
        '
        Me.XrTableCell24.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_Insentif]")})
        Me.XrTableCell24.Multiline = True
        Me.XrTableCell24.Name = "XrTableCell24"
        Me.XrTableCell24.StylePriority.UseTextAlignment = False
        Me.XrTableCell24.Text = "XrTableCell24"
        Me.XrTableCell24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell24.TextFormatString = "{0:N0}"
        Me.XrTableCell24.Weight = 0.91079352462924668R
        '
        'XrTableRow18
        '
        Me.XrTableRow18.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell57, Me.XrTableCell60})
        Me.XrTableRow18.Name = "XrTableRow18"
        Me.XrTableRow18.Weight = 0.80000000000000027R
        '
        'XrTableCell57
        '
        Me.XrTableCell57.Multiline = True
        Me.XrTableCell57.Name = "XrTableCell57"
        Me.XrTableCell57.Text = "T. LAIN"
        Me.XrTableCell57.Weight = 1.5974594863265217R
        '
        'XrTableCell60
        '
        Me.XrTableCell60.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Lain]")})
        Me.XrTableCell60.Multiline = True
        Me.XrTableCell60.Name = "XrTableCell60"
        Me.XrTableCell60.StylePriority.UseTextAlignment = False
        Me.XrTableCell60.Text = "XrTableCell60"
        Me.XrTableCell60.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell60.TextFormatString = "{0:N0}"
        Me.XrTableCell60.Weight = 0.91079352462924668R
        '
        'XrTable2
        '
        Me.XrTable2.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTable2.LocationFloat = New DevExpress.Utils.PointFloat(0!, 392.1667!)
        Me.XrTable2.Name = "XrTable2"
        Me.XrTable2.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrTable2.Rows.AddRange(New DevExpress.XtraReports.UI.XRTableRow() {Me.XrTableRow6, Me.XrTableRow9, Me.XrTableRow10, Me.XrTableRow14, Me.XrTableRow11, Me.XrTableRow12, Me.XrTableRow13, Me.XrTableRow15})
        Me.XrTable2.SizeF = New System.Drawing.SizeF(727.0!, 148.3333!)
        Me.XrTable2.StylePriority.UseFont = False
        '
        'XrTableRow6
        '
        Me.XrTableRow6.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell16, Me.XrTableCell20, Me.XrTableCell21, Me.XrTableCell18})
        Me.XrTableRow6.Name = "XrTableRow6"
        Me.XrTableRow6.Weight = 1.0R
        '
        'XrTableCell16
        '
        Me.XrTableCell16.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell16.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell16.Multiline = True
        Me.XrTableCell16.Name = "XrTableCell16"
        Me.XrTableCell16.StylePriority.UseBorders = False
        Me.XrTableCell16.StylePriority.UseFont = False
        Me.XrTableCell16.StylePriority.UseTextAlignment = False
        Me.XrTableCell16.Text = "SUB TOTAL PENGHASILAN"
        Me.XrTableCell16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell16.Weight = 1.0000000209887057R
        '
        'XrTableCell20
        '
        Me.XrTableCell20.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell20.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[TotalPendapatan]")})
        Me.XrTableCell20.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell20.Multiline = True
        Me.XrTableCell20.Name = "XrTableCell20"
        Me.XrTableCell20.StylePriority.UseBorders = False
        Me.XrTableCell20.StylePriority.UseFont = False
        Me.XrTableCell20.StylePriority.UseTextAlignment = False
        Me.XrTableCell20.Text = "XrTableCell20"
        Me.XrTableCell20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell20.TextFormatString = "{0:n0}"
        Me.XrTableCell20.Weight = 0.57323785333226762R
        '
        'XrTableCell21
        '
        Me.XrTableCell21.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell21.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell21.Multiline = True
        Me.XrTableCell21.Name = "XrTableCell21"
        Me.XrTableCell21.StylePriority.UseBorders = False
        Me.XrTableCell21.StylePriority.UseFont = False
        Me.XrTableCell21.StylePriority.UseTextAlignment = False
        Me.XrTableCell21.Text = "SUB TOTAL POTONGAN"
        Me.XrTableCell21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell21.Weight = 0.92676199974679219R
        '
        'XrTableCell18
        '
        Me.XrTableCell18.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell18.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[TotalPotongan]")})
        Me.XrTableCell18.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell18.Multiline = True
        Me.XrTableCell18.Name = "XrTableCell18"
        Me.XrTableCell18.StylePriority.UseBorders = False
        Me.XrTableCell18.StylePriority.UseFont = False
        Me.XrTableCell18.StylePriority.UseTextAlignment = False
        Me.XrTableCell18.Text = "XrTableCell18"
        Me.XrTableCell18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell18.TextFormatString = "{0:n0}"
        Me.XrTableCell18.Weight = 0.5000001259322342R
        '
        'XrTableRow9
        '
        Me.XrTableRow9.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell27, Me.XrTableCell28, Me.XrTableCell29, Me.XrTableCell30})
        Me.XrTableRow9.Name = "XrTableRow9"
        Me.XrTableRow9.Weight = 1.0R
        '
        'XrTableCell27
        '
        Me.XrTableCell27.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell27.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell27.Multiline = True
        Me.XrTableCell27.Name = "XrTableCell27"
        Me.XrTableCell27.StylePriority.UseBorders = False
        Me.XrTableCell27.StylePriority.UseFont = False
        Me.XrTableCell27.StylePriority.UseTextAlignment = False
        Me.XrTableCell27.Text = "T. PAJAK PENGHASILAN"
        Me.XrTableCell27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell27.Weight = 1.0000000209887057R
        '
        'XrTableCell28
        '
        Me.XrTableCell28.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell28.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pd_T_Pajak]")})
        Me.XrTableCell28.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell28.Multiline = True
        Me.XrTableCell28.Name = "XrTableCell28"
        Me.XrTableCell28.StylePriority.UseBorders = False
        Me.XrTableCell28.StylePriority.UseFont = False
        Me.XrTableCell28.StylePriority.UseTextAlignment = False
        Me.XrTableCell28.Text = "XrTableCell28"
        Me.XrTableCell28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell28.TextFormatString = "{0:n0}"
        Me.XrTableCell28.Weight = 0.57323785333226762R
        '
        'XrTableCell29
        '
        Me.XrTableCell29.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell29.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell29.Multiline = True
        Me.XrTableCell29.Name = "XrTableCell29"
        Me.XrTableCell29.StylePriority.UseBorders = False
        Me.XrTableCell29.StylePriority.UseFont = False
        Me.XrTableCell29.StylePriority.UseTextAlignment = False
        Me.XrTableCell29.Text = "PAJAK PENGHASILAN"
        Me.XrTableCell29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell29.Weight = 0.92676199974679219R
        '
        'XrTableCell30
        '
        Me.XrTableCell30.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell30.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Pt_PPH21]")})
        Me.XrTableCell30.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell30.Multiline = True
        Me.XrTableCell30.Name = "XrTableCell30"
        Me.XrTableCell30.StylePriority.UseBorders = False
        Me.XrTableCell30.StylePriority.UseFont = False
        Me.XrTableCell30.StylePriority.UseTextAlignment = False
        Me.XrTableCell30.Text = "XrTableCell30"
        Me.XrTableCell30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell30.TextFormatString = "{0:n0}"
        Me.XrTableCell30.Weight = 0.5000001259322342R
        '
        'XrTableRow10
        '
        Me.XrTableRow10.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell31, Me.XrTableCell32, Me.XrTableCell33, Me.XrTableCell34})
        Me.XrTableRow10.Name = "XrTableRow10"
        Me.XrTableRow10.Weight = 1.0R
        '
        'XrTableCell31
        '
        Me.XrTableCell31.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell31.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell31.Multiline = True
        Me.XrTableCell31.Name = "XrTableCell31"
        Me.XrTableCell31.StylePriority.UseBorders = False
        Me.XrTableCell31.StylePriority.UseFont = False
        Me.XrTableCell31.StylePriority.UseTextAlignment = False
        Me.XrTableCell31.Text = "TOTAL PENGHASILAN"
        Me.XrTableCell31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell31.Weight = 1.0000000209887057R
        '
        'XrTableCell32
        '
        Me.XrTableCell32.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell32.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[TotalPendapatan]+[Pd_T_Pajak]")})
        Me.XrTableCell32.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell32.Multiline = True
        Me.XrTableCell32.Name = "XrTableCell32"
        Me.XrTableCell32.StylePriority.UseBorders = False
        Me.XrTableCell32.StylePriority.UseFont = False
        Me.XrTableCell32.StylePriority.UseTextAlignment = False
        Me.XrTableCell32.Text = "XrTableCell32"
        Me.XrTableCell32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell32.TextFormatString = "{0:n0}"
        Me.XrTableCell32.Weight = 0.57323785333226762R
        '
        'XrTableCell33
        '
        Me.XrTableCell33.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell33.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell33.Multiline = True
        Me.XrTableCell33.Name = "XrTableCell33"
        Me.XrTableCell33.StylePriority.UseBorders = False
        Me.XrTableCell33.StylePriority.UseFont = False
        Me.XrTableCell33.StylePriority.UseTextAlignment = False
        Me.XrTableCell33.Text = "TOTAL POTONGAN"
        Me.XrTableCell33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell33.Weight = 0.92676199974679219R
        '
        'XrTableCell34
        '
        Me.XrTableCell34.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell34.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[TotalPotongan]+[Pt_PPH21]")})
        Me.XrTableCell34.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell34.Multiline = True
        Me.XrTableCell34.Name = "XrTableCell34"
        Me.XrTableCell34.StylePriority.UseBorders = False
        Me.XrTableCell34.StylePriority.UseFont = False
        Me.XrTableCell34.StylePriority.UseTextAlignment = False
        Me.XrTableCell34.Text = "XrTableCell34"
        Me.XrTableCell34.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell34.TextFormatString = "{0:n0}"
        Me.XrTableCell34.Weight = 0.5000001259322342R
        '
        'XrTableRow14
        '
        Me.XrTableRow14.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell17, Me.XrTableCell49})
        Me.XrTableRow14.Name = "XrTableRow14"
        Me.XrTableRow14.Weight = 1.0R
        '
        'XrTableCell17
        '
        Me.XrTableCell17.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell17.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell17.Multiline = True
        Me.XrTableCell17.Name = "XrTableCell17"
        Me.XrTableCell17.StylePriority.UseBorders = False
        Me.XrTableCell17.StylePriority.UseFont = False
        Me.XrTableCell17.StylePriority.UseTextAlignment = False
        Me.XrTableCell17.Text = "PENGHASILAN BERSIH"
        Me.XrTableCell17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell17.Weight = 1.5732382416233235R
        '
        'XrTableCell49
        '
        Me.XrTableCell49.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTableCell49.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[THP]")})
        Me.XrTableCell49.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTableCell49.Multiline = True
        Me.XrTableCell49.Name = "XrTableCell49"
        Me.XrTableCell49.StylePriority.UseBorders = False
        Me.XrTableCell49.StylePriority.UseFont = False
        Me.XrTableCell49.StylePriority.UseTextAlignment = False
        Me.XrTableCell49.Text = "XrTableCell49"
        Me.XrTableCell49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell49.TextFormatString = "{0:n0}"
        Me.XrTableCell49.Weight = 1.4267617583766763R
        '
        'XrTableRow11
        '
        Me.XrTableRow11.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.txt_terbilang})
        Me.XrTableRow11.Name = "XrTableRow11"
        Me.XrTableRow11.Weight = 1.0R
        '
        'txt_terbilang
        '
        Me.txt_terbilang.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.txt_terbilang.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.txt_terbilang.Multiline = True
        Me.txt_terbilang.Name = "txt_terbilang"
        Me.txt_terbilang.StylePriority.UseBorders = False
        Me.txt_terbilang.StylePriority.UseFont = False
        Me.txt_terbilang.StylePriority.UseTextAlignment = False
        Me.txt_terbilang.Text = "TERBILANG :"
        Me.txt_terbilang.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.txt_terbilang.TextFormatString = "TERBILANG :{0}"
        Me.txt_terbilang.Weight = 3.0R
        '
        'XrTableRow12
        '
        Me.XrTableRow12.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell36, Me.XrTableCell41})
        Me.XrTableRow12.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableRow12.Name = "XrTableRow12"
        Me.XrTableRow12.StylePriority.UseFont = False
        Me.XrTableRow12.Weight = 1.0R
        '
        'XrTableCell36
        '
        Me.XrTableCell36.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell36.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell36.Multiline = True
        Me.XrTableCell36.Name = "XrTableCell36"
        Me.XrTableCell36.StylePriority.UseBorders = False
        Me.XrTableCell36.StylePriority.UseFont = False
        Me.XrTableCell36.StylePriority.UseTextAlignment = False
        Me.XrTableCell36.Text = "Absensi"
        Me.XrTableCell36.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell36.Weight = 0.33097954793350243R
        '
        'XrTableCell41
        '
        Me.XrTableCell41.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell41.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell41.Multiline = True
        Me.XrTableCell41.Name = "XrTableCell41"
        Me.XrTableCell41.StylePriority.UseBorders = False
        Me.XrTableCell41.StylePriority.UseFont = False
        Me.XrTableCell41.StylePriority.UseTextAlignment = False
        Me.XrTableCell41.Text = ":"
        Me.XrTableCell41.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell41.Weight = 2.6690204520664973R
        '
        'XrTableRow13
        '
        Me.XrTableRow13.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell35, Me.XrTableCell44, Me.XrTableCell52, Me.XrTableCell45, Me.XrTableCell37, Me.XrTableCell43, Me.XrTableCell38, Me.XrTableCell54, Me.XrTableCell46})
        Me.XrTableRow13.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableRow13.Name = "XrTableRow13"
        Me.XrTableRow13.StylePriority.UseFont = False
        Me.XrTableRow13.Weight = 1.0R
        '
        'XrTableCell35
        '
        Me.XrTableCell35.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell35.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell35.Multiline = True
        Me.XrTableCell35.Name = "XrTableCell35"
        Me.XrTableCell35.StylePriority.UseBorders = False
        Me.XrTableCell35.StylePriority.UseFont = False
        Me.XrTableCell35.StylePriority.UseTextAlignment = False
        Me.XrTableCell35.Text = "H.Kerja"
        Me.XrTableCell35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell35.Weight = 0.33097954793350243R
        '
        'XrTableCell44
        '
        Me.XrTableCell44.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell44.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell44.Multiline = True
        Me.XrTableCell44.Name = "XrTableCell44"
        Me.XrTableCell44.StylePriority.UseBorders = False
        Me.XrTableCell44.StylePriority.UseFont = False
        Me.XrTableCell44.StylePriority.UseTextAlignment = False
        Me.XrTableCell44.Text = ":"
        Me.XrTableCell44.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell44.Weight = 0.050506203505819136R
        '
        'XrTableCell52
        '
        Me.XrTableCell52.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell52.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[HariKerja]")})
        Me.XrTableCell52.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell52.Multiline = True
        Me.XrTableCell52.Name = "XrTableCell52"
        Me.XrTableCell52.StylePriority.UseBorders = False
        Me.XrTableCell52.StylePriority.UseFont = False
        Me.XrTableCell52.StylePriority.UseTextAlignment = False
        Me.XrTableCell52.Text = "XrTableCell52"
        Me.XrTableCell52.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell52.TextFormatString = "{0} Hari"
        Me.XrTableCell52.Weight = 0.61851429053808993R
        '
        'XrTableCell45
        '
        Me.XrTableCell45.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell45.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell45.Multiline = True
        Me.XrTableCell45.Name = "XrTableCell45"
        Me.XrTableCell45.StylePriority.UseBorders = False
        Me.XrTableCell45.StylePriority.UseFont = False
        Me.XrTableCell45.StylePriority.UseTextAlignment = False
        Me.XrTableCell45.Text = "Izin"
        Me.XrTableCell45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell45.Weight = 0.286619099822956R
        '
        'XrTableCell37
        '
        Me.XrTableCell37.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell37.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell37.Multiline = True
        Me.XrTableCell37.Name = "XrTableCell37"
        Me.XrTableCell37.StylePriority.UseBorders = False
        Me.XrTableCell37.StylePriority.UseFont = False
        Me.XrTableCell37.StylePriority.UseTextAlignment = False
        Me.XrTableCell37.Text = ":"
        Me.XrTableCell37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell37.Weight = 0.041265479800133731R
        '
        'XrTableCell43
        '
        Me.XrTableCell43.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell43.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[JumlahIjin]+[JumlahIjinDispensasi]")})
        Me.XrTableCell43.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell43.Multiline = True
        Me.XrTableCell43.Name = "XrTableCell43"
        Me.XrTableCell43.StylePriority.UseBorders = False
        Me.XrTableCell43.StylePriority.UseFont = False
        Me.XrTableCell43.StylePriority.UseTextAlignment = False
        Me.XrTableCell43.Text = "XrTableCell43"
        Me.XrTableCell43.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell43.TextFormatString = "{0} Hari"
        Me.XrTableCell43.Weight = 0.58063575818089364R
        '
        'XrTableCell38
        '
        Me.XrTableCell38.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell38.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell38.Multiline = True
        Me.XrTableCell38.Name = "XrTableCell38"
        Me.XrTableCell38.StylePriority.UseBorders = False
        Me.XrTableCell38.StylePriority.UseFont = False
        Me.XrTableCell38.StylePriority.UseTextAlignment = False
        Me.XrTableCell38.Text = "Absen"
        Me.XrTableCell38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell38.Weight = 0.31370550205487824R
        '
        'XrTableCell54
        '
        Me.XrTableCell54.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell54.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell54.Multiline = True
        Me.XrTableCell54.Name = "XrTableCell54"
        Me.XrTableCell54.StylePriority.UseBorders = False
        Me.XrTableCell54.StylePriority.UseFont = False
        Me.XrTableCell54.StylePriority.UseTextAlignment = False
        Me.XrTableCell54.Text = ":"
        Me.XrTableCell54.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter
        Me.XrTableCell54.Weight = 0.058551179657612112R
        '
        'XrTableCell46
        '
        Me.XrTableCell46.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell46.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[JumlahAlpha]")})
        Me.XrTableCell46.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell46.Multiline = True
        Me.XrTableCell46.Name = "XrTableCell46"
        Me.XrTableCell46.StylePriority.UseBorders = False
        Me.XrTableCell46.StylePriority.UseFont = False
        Me.XrTableCell46.StylePriority.UseTextAlignment = False
        Me.XrTableCell46.Text = "XrTableCell46"
        Me.XrTableCell46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell46.TextFormatString = "{0} Hari"
        Me.XrTableCell46.Weight = 0.71922293850611452R
        '
        'XrTableRow15
        '
        Me.XrTableRow15.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell48, Me.XrTableCell42, Me.XrTableCell51, Me.XrTableCell40, Me.XrTableCell55, Me.XrTableCell50, Me.XrTableCell39})
        Me.XrTableRow15.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableRow15.Name = "XrTableRow15"
        Me.XrTableRow15.StylePriority.UseFont = False
        Me.XrTableRow15.Weight = 1.0R
        '
        'XrTableCell48
        '
        Me.XrTableCell48.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell48.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell48.Multiline = True
        Me.XrTableCell48.Name = "XrTableCell48"
        Me.XrTableCell48.StylePriority.UseBorders = False
        Me.XrTableCell48.StylePriority.UseFont = False
        Me.XrTableCell48.StylePriority.UseTextAlignment = False
        Me.XrTableCell48.Text = "Cuti"
        Me.XrTableCell48.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell48.Weight = 0.33097954793350254R
        '
        'XrTableCell42
        '
        Me.XrTableCell42.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell42.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell42.Multiline = True
        Me.XrTableCell42.Name = "XrTableCell42"
        Me.XrTableCell42.StylePriority.UseBorders = False
        Me.XrTableCell42.StylePriority.UseFont = False
        Me.XrTableCell42.StylePriority.UseTextAlignment = False
        Me.XrTableCell42.Text = ":"
        Me.XrTableCell42.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell42.Weight = 0.050506198258642637R
        '
        'XrTableCell51
        '
        Me.XrTableCell51.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell51.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[JumlahCuti]")})
        Me.XrTableCell51.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell51.Multiline = True
        Me.XrTableCell51.Name = "XrTableCell51"
        Me.XrTableCell51.StylePriority.UseBorders = False
        Me.XrTableCell51.StylePriority.UseFont = False
        Me.XrTableCell51.StylePriority.UseTextAlignment = False
        Me.XrTableCell51.Text = "XrTableCell51"
        Me.XrTableCell51.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell51.TextFormatString = "{0} Hari"
        Me.XrTableCell51.Weight = 0.61851428529091335R
        '
        'XrTableCell40
        '
        Me.XrTableCell40.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell40.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell40.Multiline = True
        Me.XrTableCell40.Name = "XrTableCell40"
        Me.XrTableCell40.StylePriority.UseBorders = False
        Me.XrTableCell40.StylePriority.UseFont = False
        Me.XrTableCell40.StylePriority.UseTextAlignment = False
        Me.XrTableCell40.Text = "Lembur"
        Me.XrTableCell40.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell40.Weight = 0.28661911556448527R
        '
        'XrTableCell55
        '
        Me.XrTableCell55.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell55.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell55.Multiline = True
        Me.XrTableCell55.Name = "XrTableCell55"
        Me.XrTableCell55.StylePriority.UseBorders = False
        Me.XrTableCell55.StylePriority.UseFont = False
        Me.XrTableCell55.StylePriority.UseTextAlignment = False
        Me.XrTableCell55.Text = ":"
        Me.XrTableCell55.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter
        Me.XrTableCell55.Weight = 0.041265469305780927R
        '
        'XrTableCell50
        '
        Me.XrTableCell50.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell50.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[JumlahLembur]")})
        Me.XrTableCell50.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell50.Multiline = True
        Me.XrTableCell50.Name = "XrTableCell50"
        Me.XrTableCell50.StylePriority.UseBorders = False
        Me.XrTableCell50.StylePriority.UseFont = False
        Me.XrTableCell50.StylePriority.UseTextAlignment = False
        Me.XrTableCell50.Text = "XrTableCell50"
        Me.XrTableCell50.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.XrTableCell50.TextFormatString = "{0} Hari"
        Me.XrTableCell50.Weight = 0.58063576342807011R
        '
        'XrTableCell39
        '
        Me.XrTableCell39.Borders = DevExpress.XtraPrinting.BorderSide.None
        Me.XrTableCell39.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTableCell39.Multiline = True
        Me.XrTableCell39.Name = "XrTableCell39"
        Me.XrTableCell39.StylePriority.UseBorders = False
        Me.XrTableCell39.StylePriority.UseFont = False
        Me.XrTableCell39.StylePriority.UseTextAlignment = False
        Me.XrTableCell39.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        Me.XrTableCell39.Weight = 1.0914796202186048R
        '
        'XrTable1
        '
        Me.XrTable1.Font = New DevExpress.Drawing.DXFont("Segoe UI", 8.0!)
        Me.XrTable1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 3.00001!)
        Me.XrTable1.Name = "XrTable1"
        Me.XrTable1.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrTable1.Rows.AddRange(New DevExpress.XtraReports.UI.XRTableRow() {Me.XrTableRow3, Me.XrTableRow1, Me.XrTableRow5})
        Me.XrTable1.SizeF = New System.Drawing.SizeF(727.0!, 37.5!)
        Me.XrTable1.StylePriority.UseFont = False
        '
        'XrTableRow3
        '
        Me.XrTableRow3.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell7, Me.XrTableCell8, Me.XrTableCell9, Me.XrTableCell56, Me.XrTableCell59, Me.XrTableCell62})
        Me.XrTableRow3.Name = "XrTableRow3"
        Me.XrTableRow3.Weight = 1.0R
        '
        'XrTableCell7
        '
        Me.XrTableCell7.Multiline = True
        Me.XrTableCell7.Name = "XrTableCell7"
        Me.XrTableCell7.Text = "NAMA"
        Me.XrTableCell7.Weight = 0.54264094839397781R
        '
        'XrTableCell8
        '
        Me.XrTableCell8.Multiline = True
        Me.XrTableCell8.Name = "XrTableCell8"
        Me.XrTableCell8.Text = ":"
        Me.XrTableCell8.Weight = 0.047455505622958194R
        '
        'XrTableCell9
        '
        Me.XrTableCell9.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([tm_karyawan].[Nama])")})
        Me.XrTableCell9.Multiline = True
        Me.XrTableCell9.Name = "XrTableCell9"
        Me.XrTableCell9.Text = "XrTableCell9"
        Me.XrTableCell9.Weight = 0.98314216540309063R
        '
        'XrTableCell56
        '
        Me.XrTableCell56.Multiline = True
        Me.XrTableCell56.Name = "XrTableCell56"
        Me.XrTableCell56.Text = "PAYROLL NUMBER"
        Me.XrTableCell56.Weight = 0.52854566823337557R
        '
        'XrTableCell59
        '
        Me.XrTableCell59.Multiline = True
        Me.XrTableCell59.Name = "XrTableCell59"
        Me.XrTableCell59.Text = ":"
        Me.XrTableCell59.Weight = 0.055795798111486605R
        '
        'XrTableCell62
        '
        Me.XrTableCell62.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([Keterangan])")})
        Me.XrTableCell62.Multiline = True
        Me.XrTableCell62.Name = "XrTableCell62"
        Me.XrTableCell62.Text = "XrTableCell62"
        Me.XrTableCell62.Weight = 0.84241991423511109R
        '
        'XrTableRow1
        '
        Me.XrTableRow1.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell1, Me.XrTableCell2, Me.XrTableCell3, Me.XrTableCell4, Me.XrTableCell5, Me.XrTableCell6})
        Me.XrTableRow1.Name = "XrTableRow1"
        Me.XrTableRow1.Weight = 1.0R
        '
        'XrTableCell1
        '
        Me.XrTableCell1.Multiline = True
        Me.XrTableCell1.Name = "XrTableCell1"
        Me.XrTableCell1.Text = "NIK"
        Me.XrTableCell1.Weight = 0.54264094839397781R
        '
        'XrTableCell2
        '
        Me.XrTableCell2.Multiline = True
        Me.XrTableCell2.Name = "XrTableCell2"
        Me.XrTableCell2.Text = ":"
        Me.XrTableCell2.Weight = 0.047455505622958194R
        '
        'XrTableCell3
        '
        Me.XrTableCell3.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([tm_karyawan].[NIK])")})
        Me.XrTableCell3.Multiline = True
        Me.XrTableCell3.Name = "XrTableCell3"
        Me.XrTableCell3.Text = "XrTableCell3"
        Me.XrTableCell3.Weight = 0.98314216540309063R
        '
        'XrTableCell4
        '
        Me.XrTableCell4.Multiline = True
        Me.XrTableCell4.Name = "XrTableCell4"
        Me.XrTableCell4.Text = "TAX STATUS"
        Me.XrTableCell4.Weight = 0.52854566823337557R
        '
        'XrTableCell5
        '
        Me.XrTableCell5.Multiline = True
        Me.XrTableCell5.Name = "XrTableCell5"
        Me.XrTableCell5.Text = ":"
        Me.XrTableCell5.Weight = 0.055795798111486605R
        '
        'XrTableCell6
        '
        Me.XrTableCell6.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([tm_karyawan].[tm_status_PTKP].[KodeStatus])")})
        Me.XrTableCell6.Multiline = True
        Me.XrTableCell6.Name = "XrTableCell6"
        Me.XrTableCell6.Text = "XrTableCell6"
        Me.XrTableCell6.Weight = 0.84241991423511109R
        '
        'XrTableRow5
        '
        Me.XrTableRow5.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell13, Me.XrTableCell14, Me.XrTableCell15, Me.XrTableCell58, Me.XrTableCell61, Me.XrTableCell64})
        Me.XrTableRow5.Name = "XrTableRow5"
        Me.XrTableRow5.Weight = 1.0R
        '
        'XrTableCell13
        '
        Me.XrTableCell13.Multiline = True
        Me.XrTableCell13.Name = "XrTableCell13"
        Me.XrTableCell13.Text = "DEPARTMENT"
        Me.XrTableCell13.Weight = 0.54264094839397781R
        '
        'XrTableCell14
        '
        Me.XrTableCell14.Multiline = True
        Me.XrTableCell14.Name = "XrTableCell14"
        Me.XrTableCell14.Text = ":"
        Me.XrTableCell14.Weight = 0.047455505622958194R
        '
        'XrTableCell15
        '
        Me.XrTableCell15.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([tm_karyawan].[tm_karyawan_datadinas].[tm_department].[NamaDepartemen])")})
        Me.XrTableCell15.Multiline = True
        Me.XrTableCell15.Name = "XrTableCell15"
        Me.XrTableCell15.Text = "XrTableCell15"
        Me.XrTableCell15.Weight = 0.98314216540309063R
        '
        'XrTableCell58
        '
        Me.XrTableCell58.Multiline = True
        Me.XrTableCell58.Name = "XrTableCell58"
        Me.XrTableCell58.Text = "LOKASI KERJA"
        Me.XrTableCell58.Weight = 0.52854566823337557R
        '
        'XrTableCell61
        '
        Me.XrTableCell61.Multiline = True
        Me.XrTableCell61.Name = "XrTableCell61"
        Me.XrTableCell61.Text = ":"
        Me.XrTableCell61.Weight = 0.055795798111486605R
        '
        'XrTableCell64
        '
        Me.XrTableCell64.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Upper([tm_cabang].[NamaCabang])")})
        Me.XrTableCell64.Multiline = True
        Me.XrTableCell64.Name = "XrTableCell64"
        Me.XrTableCell64.Text = "XrTableCell64"
        Me.XrTableCell64.Weight = 0.84241991423511109R
        '
        'ObjectDataSource1
        '
        Me.ObjectDataSource1.DataSource = GetType(HRD.Domain.tr_gaji)
        Me.ObjectDataSource1.Name = "ObjectDataSource1"
        '
        'fSisaPinjaman
        '
        Me.fSisaPinjaman.DataMember = "GajiLines"
        Me.fSisaPinjaman.FieldType = DevExpress.XtraReports.UI.FieldType.[Decimal]
        Me.fSisaPinjaman.Name = "fSisaPinjaman"
        '
        'XrControlStyle2
        '
        Me.XrControlStyle2.Name = "XrControlStyle2"
        '
        'fTerbilang
        '
        Me.fTerbilang.FieldType = DevExpress.XtraReports.UI.FieldType.[String]
        Me.fTerbilang.Name = "fTerbilang"
        '
        'PageHeader
        '
        Me.PageHeader.Controls.AddRange(New DevExpress.XtraReports.UI.XRControl() {Me.XrLabel4, Me.XrLine1, Me.XrLabel3, Me.XrLabel2, Me.XrLabel1, Me.XrPictureBox2, Me.XrPictureBox1})
        Me.PageHeader.HeightF = 134.1667!
        Me.PageHeader.Name = "PageHeader"
        '
        'XrLabel3
        '
        Me.XrLabel3.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[tm_area].[NamaArea]")})
        Me.XrLabel3.Font = New DevExpress.Drawing.DXFont("Segoe UI", 12.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrLabel3.LocationFloat = New DevExpress.Utils.PointFloat(425.0!, 20.0!)
        Me.XrLabel3.Multiline = True
        Me.XrLabel3.Name = "XrLabel3"
        Me.XrLabel3.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100.0!)
        Me.XrLabel3.SizeF = New System.Drawing.SizeF(292.0!, 23.0!)
        Me.XrLabel3.StylePriority.UseFont = False
        Me.XrLabel3.StylePriority.UseTextAlignment = False
        Me.XrLabel3.Text = "XrLabel3"
        Me.XrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        '
        'XrLabel2
        '
        Me.XrLabel2.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Tanggal]")})
        Me.XrLabel2.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrLabel2.LocationFloat = New DevExpress.Utils.PointFloat(216.0!, 114.0!)
        Me.XrLabel2.Multiline = True
        Me.XrLabel2.Name = "XrLabel2"
        Me.XrLabel2.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100.0!)
        Me.XrLabel2.SizeF = New System.Drawing.SizeF(295.0!, 15.0!)
        Me.XrLabel2.StylePriority.UseFont = False
        Me.XrLabel2.StylePriority.UseTextAlignment = False
        Me.XrLabel2.Text = "SLIP GAJI"
        Me.XrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter
        Me.XrLabel2.TextFormatString = "BULAN {0:MMM yyyy}"
        '
        'XrLabel1
        '
        Me.XrLabel1.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrLabel1.LocationFloat = New DevExpress.Utils.PointFloat(216.0!, 99.0!)
        Me.XrLabel1.Multiline = True
        Me.XrLabel1.Name = "XrLabel1"
        Me.XrLabel1.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100.0!)
        Me.XrLabel1.SizeF = New System.Drawing.SizeF(295.0!, 15.0!)
        Me.XrLabel1.StylePriority.UseFont = False
        Me.XrLabel1.StylePriority.UseTextAlignment = False
        Me.XrLabel1.Text = "SLIP GAJI"
        Me.XrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter
        '
        'XrPictureBox2
        '
        Me.XrPictureBox2.ImageSource = New DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("XrPictureBox2.ImageSource"))
        Me.XrPictureBox2.LocationFloat = New DevExpress.Utils.PointFloat(80.20738!, 20.0!)
        Me.XrPictureBox2.Name = "XrPictureBox2"
        Me.XrPictureBox2.SizeF = New System.Drawing.SizeF(205.0!, 35.0!)
        '
        'XrPictureBox1
        '
        Me.XrPictureBox1.ImageSource = New DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("XrPictureBox1.ImageSource"))
        Me.XrPictureBox1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 0!)
        Me.XrPictureBox1.Name = "XrPictureBox1"
        Me.XrPictureBox1.SizeF = New System.Drawing.SizeF(80.0!, 65.0!)
        '
        'PageFooter
        '
        Me.PageFooter.Controls.AddRange(New DevExpress.XtraReports.UI.XRControl() {Me.XrRichText1})
        Me.PageFooter.HeightF = 87.83325!
        Me.PageFooter.Name = "PageFooter"
        '
        'XrRichText1
        '
        Me.XrRichText1.Borders = DevExpress.XtraPrinting.BorderSide.Top
        Me.XrRichText1.BorderWidth = 3.0!
        Me.XrRichText1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 9.166667!)
        Me.XrRichText1.Name = "XrRichText1"
        Me.XrRichText1.Padding = New DevExpress.XtraPrinting.PaddingInfo(40, 0, 0, 0, 100.0!)
        Me.XrRichText1.SerializableRtfString = resources.GetString("XrRichText1.SerializableRtfString")
        Me.XrRichText1.SizeF = New System.Drawing.SizeF(717.0!, 64.16667!)
        Me.XrRichText1.StylePriority.UseBorders = False
        Me.XrRichText1.StylePriority.UseBorderWidth = False
        Me.XrRichText1.StylePriority.UsePadding = False
        '
        'XrLine1
        '
        Me.XrLine1.BorderWidth = 2.0!
        Me.XrLine1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 66.0!)
        Me.XrLine1.Name = "XrLine1"
        Me.XrLine1.SizeF = New System.Drawing.SizeF(727.0!, 5.000004!)
        Me.XrLine1.StylePriority.UseBorderWidth = False
        '
        'XrLabel4
        '
        Me.XrLabel4.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrLabel4.LocationFloat = New DevExpress.Utils.PointFloat(580.0!, 71.00001!)
        Me.XrLabel4.Multiline = True
        Me.XrLabel4.Name = "XrLabel4"
        Me.XrLabel4.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrLabel4.SizeF = New System.Drawing.SizeF(137.0!, 23.0!)
        Me.XrLabel4.StylePriority.UseFont = False
        Me.XrLabel4.StylePriority.UseTextAlignment = False
        Me.XrLabel4.Text = "FO-ACC-16"
        Me.XrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight
        '
        'rpt_slip_gaji
        '
        Me.Bands.AddRange(New DevExpress.XtraReports.UI.Band() {Me.TopMargin, Me.BottomMargin, Me.Detail, Me.DetailReport, Me.PageHeader, Me.PageFooter})
        Me.CalculatedFields.AddRange(New DevExpress.XtraReports.UI.CalculatedField() {Me.fSisaPinjaman, Me.fTerbilang})
        Me.ComponentStorage.AddRange(New System.ComponentModel.IComponent() {Me.ObjectDataSource1})
        Me.DataSource = Me.ObjectDataSource1
        Me.DrawGrid = False
        Me.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!)
        Me.Margins = New DevExpress.Drawing.DXMargins(50.0!, 50.0!, 50.0!, 50.0!)
        Me.PageHeight = 1169
        Me.PageWidth = 827
        Me.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.A4
        Me.SnapGridSize = 3.0!
        Me.StyleSheet.AddRange(New DevExpress.XtraReports.UI.XRControlStyle() {Me.XrControlStyle2})
        Me.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft
        Me.Version = "23.1"
        CType(Me.XrTable4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XrTable3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XrTable2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XrTable1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ObjectDataSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XrRichText1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Friend WithEvents TopMargin As DevExpress.XtraReports.UI.TopMarginBand
    Friend WithEvents BottomMargin As DevExpress.XtraReports.UI.BottomMarginBand
    Friend WithEvents Detail As DevExpress.XtraReports.UI.DetailBand
    Friend WithEvents DetailReport As DevExpress.XtraReports.UI.DetailReportBand
    Friend WithEvents Detail1 As DevExpress.XtraReports.UI.DetailBand
    Friend WithEvents XrTable2 As DevExpress.XtraReports.UI.XRTable
    Friend WithEvents XrTableRow14 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell17 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell49 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTable1 As DevExpress.XtraReports.UI.XRTable
    Friend WithEvents XrTableRow3 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell7 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell8 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell9 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow5 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell13 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell14 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell15 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents ObjectDataSource1 As DevExpress.DataAccess.ObjectBinding.ObjectDataSource
    Friend WithEvents fSisaPinjaman As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents XrControlStyle2 As DevExpress.XtraReports.UI.XRControlStyle
    Friend WithEvents XrTable4 As DevExpress.XtraReports.UI.XRTable
    Friend WithEvents XrTableRow44 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell88 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow45 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell93 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell96 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow47 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell122 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell126 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow48 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell138 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell142 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow50 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell156 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell157 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow51 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell160 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell161 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow52 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell164 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell165 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow54 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell171 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell172 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow55 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell175 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell176 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow56 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell179 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell180 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTable3 As DevExpress.XtraReports.UI.XRTable
    Friend WithEvents XrTableRow25 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell84 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow26 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell86 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell87 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow27 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell90 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell91 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow28 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell94 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell95 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell97 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow29 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell98 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell99 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell101 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow30 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell102 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell103 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow31 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell105 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell106 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell108 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow32 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell109 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell110 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell112 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow33 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell113 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell114 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell116 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow34 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell117 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell118 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow36 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell124 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell125 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell127 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow37 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell128 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell129 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell131 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow38 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell132 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell133 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell135 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow6 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell16 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell20 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell21 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell18 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow8 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell25 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell26 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow9 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell27 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell28 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell29 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell30 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow10 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell31 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell32 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell33 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell34 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow11 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents txt_terbilang As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow12 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell36 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell41 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow13 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell35 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell44 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell45 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell38 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell46 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow15 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell48 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell40 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell50 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell52 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell37 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell43 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell54 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell51 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell55 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell56 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell59 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell62 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell58 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell61 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell64 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents fTerbilang As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents PageHeader As DevExpress.XtraReports.UI.PageHeaderBand
    Friend WithEvents XrPictureBox2 As DevExpress.XtraReports.UI.XRPictureBox
    Friend WithEvents XrPictureBox1 As DevExpress.XtraReports.UI.XRPictureBox
    Friend WithEvents XrTableRow16 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell19 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell65 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell66 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow17 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell67 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell68 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow7 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell22 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell23 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow19 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell70 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell71 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrLabel1 As DevExpress.XtraReports.UI.XRLabel
    Friend WithEvents XrLabel2 As DevExpress.XtraReports.UI.XRLabel
    Friend WithEvents XrTableRow1 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell1 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell2 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell3 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell4 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell5 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell6 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow2 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell10 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell11 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents PageFooter As DevExpress.XtraReports.UI.PageFooterBand
    Friend WithEvents XrRichText1 As DevExpress.XtraReports.UI.XRRichText
    Friend WithEvents XrTableRow4 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell12 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell24 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableRow18 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell57 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell60 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrLabel3 As DevExpress.XtraReports.UI.XRLabel
    Friend WithEvents XrTableCell42 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell39 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrLabel4 As DevExpress.XtraReports.UI.XRLabel
    Friend WithEvents XrLine1 As DevExpress.XtraReports.UI.XRLine
End Class
