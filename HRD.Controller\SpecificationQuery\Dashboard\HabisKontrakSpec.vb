﻿Imports System.Linq.Expressions
Imports HRD.Domain
Imports HRD.Helpers
Public Class HabisKontrakSpec
    Inherits CompositeSpecification(Of vw_KaryawanAkanHabisKontrak)

    Private ReadOnly _loggedInUser As ApplicationUser

    Public Sub New()
        _loggedInUser = AuthHelper.GetLoggedInUserInfo

    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of vw_KaryawanAkanHabisKontrak, Boolean))
        Get
            Select Case _loggedInUser.PermisionArea
                Case 0
                    Return Function(f) f.Cabang_id = _loggedInUser.Cabang_id
                Case 1
                    Return Function(f) _loggedInUser.AreaIdPermisionList.Contains(f.Area_id)
                Case Else

            End Select
        End Get
    End Property

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of vw_KaryawanAkanHabisKontrak)) As IQueryable(Of vw_KaryawanAkanHabisKontrak)
        If Criteria Is Nothing Then
            Return query
        End If
        Return query.Where(Criteria)
    End Function
End Class
