﻿Imports System.Data.Entity
Imports System.Data.Entity.Infrastructure
Imports System.Data.Entity.Migrations
Imports System.Linq.Expressions
Imports HRD.Application
Imports HRD.Domain
Public Class EfRepository(Of T As Class)
    Implements IRepository(Of T)

#Region "Properties"
    Private ReadOnly _context As HRDEntities

    Protected _entities As DbSet(Of T)
#End Region
#Region "Ctor"

    'INSTANT VB WARNING: The following constructor is declared outside of its associated class:
    'ORIGINAL LINE: public EfRepository(ApplicationDBContext context)
    Public Sub New(ByVal context As HRDEntities)
        _context = context
    End Sub

#End Region
#Region "Utility"
    Protected Function GetFullErrorTextAndRollbackEntityChanges(ByVal exception As DbUpdateException) As String
        'rollback entity changes
        If TypeOf _context Is DbContext Then
            Dim dbContext As DbContext = CType(_context, DbContext)
            Dim entries = dbContext.ChangeTracker.Entries().Where(Function(e) e.State = EntityState.Added OrElse e.State = EntityState.Modified).ToList()

            entries.ForEach(Sub(entry)
                                Try
                                    entry.State = EntityState.Unchanged
                                Catch e1 As InvalidOperationException
                                    ' ignored
                                End Try
                            End Sub)
        End If

        Try
            _context.SaveChanges()
            Return exception.ToString()
        Catch ex As Exception
            'if after the rollback of changes the context is still not saving,
            'return the full text of the exception that occurred when saving
            Return ex.ToString()
        End Try
    End Function
#End Region

    Protected Overridable ReadOnly Property Entities() As DbSet(Of T)
        Get
            If _entities Is Nothing Then
                _entities = _context.Set(Of T)()
            End If

            Return _entities
        End Get
    End Property


    Public ReadOnly Property Table As IQueryable(Of T) Implements IRepository(Of T).Table
        Get
            Return Entities

        End Get
    End Property

    Public ReadOnly Property TableNoTracking As IQueryable(Of T) Implements IRepository(Of T).TableNoTracking
        Get
            Return Entities.AsNoTracking()
        End Get
    End Property

    Public Sub Update(entity As T) Implements IRepository(Of T).Update
        _context.Entry(entity).State = EntityState.Modified

    End Sub

    Public Async Function AddAsync(entity As T) As Task(Of T) Implements IRepository(Of T).AddAsync
        _context.Entry(entity).State = EntityState.Added

        Return entity

    End Function

    Public Function UpdateAsync(Id As Object, entity As T) As Task Implements IRepository(Of T).UpdateAsync
        Dim exist As T = _context.Set(Of T)().Find(Id)
        _context.Entry(exist).CurrentValues.SetValues(entity)
        Return Task.CompletedTask

    End Function

    Public Async Function DeleteAsync(id As Integer) As Task(Of Boolean) Implements IRepository(Of T).DeleteAsync
        Dim entity = Entities.Find(id)
        If entity Is Nothing Then
            Return False
        End If

        _context.Entry(entity).State = EntityState.Deleted
        Return True

    End Function

    Public Async Function Delete(entity As T) As Task(Of Boolean) Implements IRepository(Of T).Delete
        _context.Entry(entity).State = EntityState.Deleted
        Return True

    End Function

    Public Async Function Delete(where As Expression(Of Func(Of T, Boolean))) As Task(Of Boolean) Implements IRepository(Of T).Delete
        Dim entities = Me.Entities.Where(where)
        Me.Entities.RemoveRange(entities)
        Return True

    End Function

    Public Async Function [Get](id As Integer) As Task(Of T) Implements IRepository(Of T).Get
        Dim o = Entities.Find(id)
        Return o
    End Function

    Public Async Function [Get](where As Expression(Of Func(Of T, Boolean))) As Task(Of T) Implements IRepository(Of T).Get
        Return Await Entities.FirstOrDefaultAsync(where)
    End Function

    Public Function GetMany(where As Expression(Of Func(Of T, Boolean))) As IEnumerable(Of T) Implements IRepository(Of T).GetMany
        Return Entities.Where(where)
    End Function

    Public Function GetAll() As IEnumerable(Of T) Implements IRepository(Of T).GetAll
        Return Entities
    End Function
    Public Overridable Sub Save()
        _context.SaveChanges()
    End Sub
    Public Overridable Sub SaveAsync()
        _context.SaveChangesAsync()
    End Sub

    Public Async Function Count(where As Expression(Of Func(Of T, Boolean))) As Task(Of Integer) Implements IRepository(Of T).Count
        Return Await Entities.CountAsync(where)
    End Function

    Public Async Function Count() As Task(Of Integer) Implements IRepository(Of T).Count
        Return Await Entities.CountAsync()
    End Function

    Public Function [Set](Of TEntity As Class)() As Data.Entity.DbSet(Of TEntity) Implements IRepository(Of T).Set
        Return _context.Set(Of TEntity)()
    End Function

    Public Async Function AddRangeAsync(entity As List(Of T)) As Task(Of Boolean) Implements IRepository(Of T).AddRangeAsync
        Try
            If entity.Count > 0 Then
                Entities.AddRange(entity)

            End If
            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function

    Public Function AddRange(entity As List(Of T)) As Boolean Implements IRepository(Of T).AddRange
        Try
            If entity.Count > 0 Then
                Entities.AddRange(entity)
            End If
            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function

    Public Function RemoveRange(entitiesCol As ICollection(Of T)) As Boolean Implements IRepository(Of T).RemoveRange
        Try
            If entitiesCol.Count > 0 Then
                Entities.RemoveRange(entitiesCol)
            End If
            Return True
        Catch ex As Exception
            Return False
        End Try

    End Function
End Class
