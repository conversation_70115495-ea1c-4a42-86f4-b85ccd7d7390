﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C2185DB9-AECC-41B7-AF08-FC7BDD4091B2}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>HRD.Dao</RootNamespace>
    <AssemblyName>HRD.Dao</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>HRD.Dao.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>HRD.Dao.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BmiHrd.Encryption">
      <HintPath>..\..\BmiHrd\BmiHrd.Encryption\bin\Release\BmiHrd.Encryption.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="Z.EntityFramework.Extensions, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Extensions.7.100.0.4\lib\net45\Z.EntityFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Z.EntityFramework.Plus.EF6, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Plus.EF6.7.100.0.4\lib\net45\Z.EntityFramework.Plus.EF6.dll</HintPath>
    </Reference>
    <Reference Include="Z.Expressions.Eval, Version=5.0.11.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.Expressions.Eval.5.0.11\lib\net45\Z.Expressions.Eval.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ContextFactory.vb" />
    <Compile Include="Contracts\IAbsensiDao.vb" />
    <Compile Include="Contracts\IAgamaDao.vb" />
    <Compile Include="Contracts\IAreaDao.vb" />
    <Compile Include="Contracts\ICabangDao.vb" />
    <Compile Include="Contracts\IDataDinasDao.vb" />
    <Compile Include="Contracts\IDepartmentDao.vb" />
    <Compile Include="Contracts\IGajiLineDao.vb" />
    <Compile Include="Contracts\IIjinMasterDao.vb" />
    <Compile Include="Contracts\IJabatanDao.vb" />
    <Compile Include="Contracts\IJenisKelaminDao.vb" />
    <Compile Include="Contracts\IJurusanDao.vb" />
    <Compile Include="Contracts\IKaryawanDao.vb" />
    <Compile Include="Contracts\ILiburNasionalDao.vb" />
    <Compile Include="Contracts\IMenuDao.vb" />
    <Compile Include="Contracts\IMenuRoleDao.vb" />
    <Compile Include="Contracts\INamaBankDao.vb" />
    <Compile Include="Contracts\IOffKaryawanLineDao.vb" />
    <Compile Include="Contracts\IPendidikanDao.vb" />
    <Compile Include="Contracts\IRegisterPinjamanDao.vb" />
    <Compile Include="Contracts\IRoleDao.vb" />
    <Compile Include="Contracts\ISettingAbsenDao.vb" />
    <Compile Include="Contracts\IShiftAbsensiDao.vb" />
    <Compile Include="Contracts\ISPKLemburDao.vb" />
    <Compile Include="Contracts\ISPKLemburLineDao.vb" />
    <Compile Include="Contracts\IStatusPerkawinanDao.vb" />
    <Compile Include="Contracts\IStatusPTKPDao.vb" />
    <Compile Include="Contracts\ITerPajakDao.vb" />
    <Compile Include="Contracts\IUserDao.vb" />
    <Compile Include="Contracts\IUserRoleDao.vb" />
    <Compile Include="DaoBase.vb" />
    <Compile Include="DaoFactory.vb" />
    <Compile Include="IDao.vb" />
    <Compile Include="IDaoFactory.vb" />
    <Compile Include="Implementations\AbsensiDao.vb" />
    <Compile Include="Implementations\AgamaDao.vb" />
    <Compile Include="Implementations\AreaDao.vb" />
    <Compile Include="Implementations\CabangDao.vb" />
    <Compile Include="Implementations\DataDinasDao.vb" />
    <Compile Include="Implementations\DepartmentDao.vb" />
    <Compile Include="Implementations\GajiLineDao.vb" />
    <Compile Include="Implementations\IjinMasterDao.vb" />
    <Compile Include="Implementations\LiburNasionalDao.vb" />
    <Compile Include="Implementations\OffKaryawanLineDao.vb" />
    <Compile Include="Implementations\RegisterPinjamanDao.vb" />
    <Compile Include="Implementations\JabatanDao.vb" />
    <Compile Include="Implementations\JenisKelaminDao.vb" />
    <Compile Include="Implementations\JurusanDao.vb" />
    <Compile Include="Implementations\KaryawanDao.vb" />
    <Compile Include="Implementations\MenuDao.vb" />
    <Compile Include="Implementations\MenuRoleDao.vb" />
    <Compile Include="Implementations\NamaBankDao.vb" />
    <Compile Include="Implementations\PendidikanDao.vb" />
    <Compile Include="Implementations\RoleDao.vb" />
    <Compile Include="Implementations\SettingAbsenDao.vb" />
    <Compile Include="Implementations\ShiftAbsensiDao.vb" />
    <Compile Include="Implementations\SPKLemburDao.vb" />
    <Compile Include="Implementations\SPKLemburLineDao.vb" />
    <Compile Include="Implementations\StatusPerkawinanDao.vb" />
    <Compile Include="Implementations\StatusPTKPDao.vb" />
    <Compile Include="Implementations\TerPajakDao.vb" />
    <Compile Include="Implementations\UserAreaDao.vb" />
    <Compile Include="Implementations\UserDao.vb" />
    <Compile Include="Implementations\UserRoleDao.vb" />
    <Compile Include="Manager\AreaManager.vb" />
    <Compile Include="Manager\CabangManager.vb" />
    <Compile Include="Contracts\IUserAreaDao.vb" />
    <Compile Include="Manager\MenuManager.vb" />
    <Compile Include="Manager\PendidikanManager.vb" />
    <Compile Include="Manager\RoleManager.vb" />
    <Compile Include="Manager\UserManager.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="ObjectContextManager.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HRD.Domain\HRD.Domain.vbproj">
      <Project>{93a2fb6b-4503-4da1-80f4-ba3ca6dbb7d8}</Project>
      <Name>HRD.Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>