﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Public Class ucArea_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IAreaEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            Session(ViewState("_PageID").ToString()) = Controller.SelectedItem
            Session(ViewState("_PageID").ToString() & "_Action") = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = Session(ViewState("_PageID").ToString() & "_Action")

        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_area)




        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelect)


        Controller.SelectedItem = oSelect
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()
        End If
    End Sub

End Class