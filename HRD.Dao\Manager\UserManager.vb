﻿Imports HRD.Domain
Imports HRD.Dao
Imports BmiHrd.Encryption
Imports Z.EntityFramework.Plus

Public Class UserManager
    Private ReadOnly daoDactory As IDaoFactory
    Public Sub New(daoDactory As IDaoFactory)
        Me.daoDactory = daoDactory

    End Sub
    Public Function ValidateUser(username As String, password As String) As tm_user
        Return Me.daoDactory.GetUserDao.ValidateUser(username, EncryptPassword(password))
    End Function
    ReadOnly Property DataAccess As IUserDao
        Get
            Return daoDactory.GetUserDao
        End Get
    End Property
    ReadOnly Property GetMenuDao As IMenuDao
        Get
            Return daoDactory.GetMenuDao
        End Get
    End Property

    ReadOnly Property GetUserRoleDao As IUserRoleDao
        Get
            Return daoDactory.GetUserRoleDao
        End Get
    End Property

    ReadOnly Property GetUserAreaDao As IUserAreaDao
        Get
            Return daoDactory.GetUserAreaDao
        End Get
    End Property
    Public Function GetUserByUserName(userName As String) As tm_user
        Return DataAccess.FindBy(Function(f) f.Username = userName).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(5)}, userName).FirstOrDefault
    End Function
    Public Function EncryptPassword(ByVal password As String) As String
        Dim s As String = password & "|user"
        Return Crypto.ActionEncrypt(s)
    End Function
    Function DecryptPassword(ByVal sid As String) As Integer
        Dim s As String = Crypto.ActionDecrypt(sid)
        s = s.Substring(0, s.IndexOf("|"))
        Return Integer.Parse(s)
    End Function


End Class
