﻿Imports Microsoft.VisualBasic
'Imports System
Imports System.Web.UI
Imports System.Web.UI.HtmlControls
Imports HRDv23.Model
Imports DevExpress.Web
Imports DevExpress.Web.ASPxSiteMapControl
Imports HRD.Dao
Imports HRD.Domain
Imports Z.EntityFramework.Plus
Imports HRD.Helpers

Partial Public Class Root
    Inherits MasterPage
    Private privateEnableBackButton As Boolean
    Public Property EnableBackButton() As Boolean
        Get
            Return privateEnableBackButton
        End Get
        Set(ByVal value As Boolean)
            privateEnableBackButton = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs)
        If (Not String.IsNullOrEmpty(Page.Header.Title)) Then
            Page.Header.Title &= " - "
        End If
        Page.Header.Title = Page.Header.Title & "HRD And Payroll MNF"

        Page.Header.DataBind()
        UpdateUserMenuItemsVisible()
        HideUnusedContent()
        UpdateUserInfo()
    End Sub

    Protected Sub HideUnusedContent()
        LeftAreaMenu.Items(1).Visible = EnableBackButton

        Dim hasLeftPanelContent As Boolean = HasContent(LeftPanelContent)
        LeftAreaMenu.Items.FindByName("ToggleLeftPanel").Visible = hasLeftPanelContent
        LeftPanel.Visible = hasLeftPanelContent

        Dim hasRightPanelContent As Boolean = HasContent(RightPanelContent)
        RightAreaMenu.Items.FindByName("ToggleRightPanel").Visible = hasRightPanelContent
        RightPanel.Visible = hasRightPanelContent

        Dim hasPageToolbar As Boolean = HasContent(PageToolbar)
        PageToolbarPanel.Visible = hasPageToolbar
    End Sub

    Protected Function HasContent(ByVal ContentPlaceHolder As Control) As Boolean
        If ContentPlaceHolder Is Nothing Then
            Return False
        End If

        Dim childControls As ControlCollection = ContentPlaceHolder.Controls
        If childControls.Count = 0 Then
            Return False
        End If

        Return True
    End Function

    ' SignIn/Register

    Protected Sub UpdateUserMenuItemsVisible()
        Dim isAuthenticated = AuthHelper.IsAuthenticated()
        RightAreaMenu.Items.FindByName("SignInItem").Visible = Not isAuthenticated
        RightAreaMenu.Items.FindByName("RegisterItem").Visible = Not isAuthenticated
        RightAreaMenu.Items.FindByName("MyAccountItem").Visible = isAuthenticated
        RightAreaMenu.Items.FindByName("SignOutItem").Visible = isAuthenticated
    End Sub

    Protected Sub UpdateUserInfo()
        If AuthHelper.IsAuthenticated() Then
            Dim user = AuthHelper.GetLoggedInUserInfo()
            Dim myAccountItem = RightAreaMenu.Items.FindByName("MyAccountItem")
            Dim userName = CType(myAccountItem.FindControl("UserNameLabel"), ASPxLabel)
            Dim email = CType(myAccountItem.FindControl("EmailLabel"), ASPxLabel)
            Dim accountImage = CType(RightAreaMenu.Items(0).FindControl("AccountImage"), HtmlGenericControl)
            userName.Text = String.Format("{0} ({1} {2})", user.UserName, user.FirstName, user.LastName)
            email.Text = user.Email
            accountImage.Attributes("class") = "account-image"

            If String.IsNullOrEmpty(user.AvatarUrl) Then
                accountImage.InnerHtml = String.Format("{0}{1}", user.FirstName(0), user.LastName(0)).ToUpper()
            Else
                Dim avatarUrl = CType(myAccountItem.FindControl("AvatarUrl"), HtmlImage)
                avatarUrl.Attributes("src") = ResolveUrl(user.AvatarUrl)
                accountImage.Style("background-image") = ResolveUrl(user.AvatarUrl)
            End If
        End If
    End Sub

    Protected Sub RightAreaMenu_ItemClick(ByVal source As Object, ByVal e As DevExpress.Web.MenuItemEventArgs)
        If e.Item.Name = "SignOutItem" Then
            AuthHelper.SignOut() ' DXCOMMENT: Your Signing out logic
            Response.Redirect("~/")
        End If
    End Sub

    Protected Sub ApplicationMenu_ItemDataBound(ByVal source As Object, ByVal e As MenuItemEventArgs)
        e.Item.Image.Url = String.Format("Content/Images/{0}.svg", e.Item.Text)
        e.Item.Image.UrlSelected = String.Format("Content/Images/{0}-white.svg", e.Item.Text)
    End Sub

#Region "Create Menu"
    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()

    Protected Sub BuildMenu(mnu As ASPxMenu, EnableRoles As Boolean)
        Dim menuDao = daoFactory.GetMenuDao

        Dim os As List(Of tm_menu)
        If Session("menuData") IsNot Nothing Then
            os = CType(Session("menuData"), List(Of tm_menu))
        Else
            os = menuDao.GetAll.OrderBy(Function(f) f.Parent).OrderBy(Function(f) f.Id).ToList()
            Session("menuData") = os
        End If

        Dim menuItems As Dictionary(Of String, MenuItem) = New Dictionary(Of String, MenuItem)

        Dim roleNamesByMenu As Dictionary(Of String, List(Of String))
        If Session("roleNamesByMenu") IsNot Nothing Then
            roleNamesByMenu = CType(Session("roleNamesByMenu"), Dictionary(Of String, List(Of String)))
        Else
            roleNamesByMenu = New Dictionary(Of String, List(Of String))
        End If

        For Each x In os
            Dim s As List(Of String)
            If EnableRoles Then
                If roleNamesByMenu.ContainsKey(x.Id.ToString()) Then
                    s = roleNamesByMenu(x.Id.ToString())
                Else
                    s = x.tm_menu_role.Select(Function(f) f.tm_role.RoleName).ToList()
                    roleNamesByMenu(x.Id.ToString()) = s
                End If
            End If

            If Not EnableRoles Or HttpContext.Current.User.IsInRole("Administrator") Or CekRoleMenu(s) Then
                Dim item As MenuItem = CreateMenuItem(x)
                Dim itemID As String = x.Id.ToString
                Dim parentID As String = If(x.Parent Is Nothing, "", x.Parent.ToString)

                If menuItems.ContainsKey(parentID) Then
                    menuItems(parentID).Items.Add(item)
                Else
                    If parentID = "" Then
                        mnu.Items.Add(item)
                    End If
                End If
                menuItems.Add(itemID, item)
            End If
        Next

        Session("roleNamesByMenu") = roleNamesByMenu
    End Sub

    Private Function CreateMenuItem(x As tm_menu) As MenuItem
        Dim ret As MenuItem = New MenuItem
        ret.Text = x.Title.ToString
        If x.Url IsNot Nothing Then
            ret.NavigateUrl = x.Url
        End If
        'If x.Image_url IsNot Nothing Then
        '    ret.Image.Url = x.Image_url
        'End If
        ret.BeginGroup = x.BeginGroup
        Return ret
    End Function
    Private Function CekRoleMenu(_s As List(Of String)) As Boolean
        'For Each x In _s
        '    If HttpContext.Current.User.IsInRole(x) Then
        '        Return True
        '    End If

        'Next
        'Return False

        If _s IsNot Nothing AndAlso _s.Count > 0 Then
            Return _s.Any(Function(x) HttpContext.Current.User.IsInRole(x))
        End If
        Return False
    End Function

    Private Sub Root_Init(sender As Object, e As EventArgs) Handles Me.Init
        If Me.IsPostBack = False Then
            BuildMenu(Me.ApplicationMenu, True)
        End If
    End Sub
#End Region
End Class