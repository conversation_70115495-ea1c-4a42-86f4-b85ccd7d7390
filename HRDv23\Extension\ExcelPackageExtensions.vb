﻿Imports OfficeOpenXml

Module ExcelPackageExtensions

    <System.Runtime.CompilerServices.Extension>
    Public Function ToDataTable(ByVal package As ExcelPackage) As DataTable
        Dim workSheet As ExcelWorksheet = package.Workbook.Worksheets.First()
        Dim table As New DataTable()
        Dim i As Integer = 0
        Dim iEndColumn As Integer = 10 'workSheet.Dimension.End.Column
        For Each firstRowCell In workSheet.Cells(1, 1, 1, iEndColumn)
            i += 1
            table.Columns.Add(firstRowCell.Text.Trim.ToLower)
        Next firstRowCell
        For rowNumber = 2 To workSheet.Dimension.End.Row
            Dim row = workSheet.Cells(rowNumber, 1, rowNumber, iEndColumn)
            Dim newRow = table.NewRow()
            For Each cell In row
                newRow(cell.Start.Column - 1) = cell.Text
            Next cell
            table.Rows.Add(newRow)
        Next rowNumber
        Return table
    End Function
End Module
