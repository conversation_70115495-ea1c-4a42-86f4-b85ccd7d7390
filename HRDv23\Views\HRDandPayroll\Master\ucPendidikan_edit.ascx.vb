﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data

Public Class ucPendidikan_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IPendidikanEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_pendidikan.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_pendidikan.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction


        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)


        Controller.SelectedItem = oSelectItem
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

            Me.ASPxGridView1.DataSourceID = Nothing
        Else
            Me.ASPxGridView1.DataSourceID = "EntityServerModeDataSource1"
        End If
    End Sub

    Protected Sub EntityServerModeDataSource1_Selecting(sender As Object, e As DevExpress.Data.Linq.LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting

        If oSelectItem Is Nothing Then
            Return
        End If
        e.QueryableSource = oSelectItem.tm_jurusan.Where(Function(f) f.Deleting <> True).AsQueryable
    End Sub

    Private Sub ASPxGridView1_RowInserting(sender As Object, e As ASPxDataInsertingEventArgs) Handles ASPxGridView1.RowInserting
        Dim o As New tm_jurusan
        With o
            .KodeJurusan = e.NewValues("KodeJurusan")
            .NamaJurusan = e.NewValues("NamaJurusan")

            oSelectItem.tm_jurusan.Add(o)
        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView1.RowUpdating
        Dim o = CType(oSelectItem.tm_jurusan.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_jurusan)
        With o
            .KodeJurusan = e.NewValues("KodeJurusan")
            .NamaJurusan = e.NewValues("NamaJurusan")


        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowDeleting(sender As Object, e As ASPxDataDeletingEventArgs) Handles ASPxGridView1.RowDeleting
        Dim o = CType(oSelectItem.tm_jurusan.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_jurusan)
        With o
            .Deleting = True
        End With
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowValidating(sender As Object, e As ASPxDataValidationEventArgs) Handles ASPxGridView1.RowValidating
        Dim os = oSelectItem.tm_jurusan.Where(Function(f) f.RowGuid <> e.Keys("RowGuid"))
        os = os.Where(Function(f) f.KodeJurusan = e.NewValues("KodeJurusan"))

        If os.Count > 0 Then
            e.RowError = "Kode Jurusan ini sudah ada di list!"
        End If
    End Sub

    Property oSelectItem As tm_pendidikan
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tm_pendidikan)
        End Get
        Set(value As tm_pendidikan)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property


End Class