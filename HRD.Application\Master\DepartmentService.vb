﻿Imports System.Data.Entity
Imports HRD.Application
Imports HRD.Domain

Public Class DepartmentService
    Implements IDepartmentService

#Region "Properties"
    Private ReadOnly _unitOfWork As IUnitOfWork
    'Private ReadOnly logger As ILogger(Of DepartmentService)
    'Private ReadOnly mapper As IMapper
    Private ReadOnly errorMessageLog As IErrorMessageLog
#End Region


#Region "Ctor"
    Public Sub New(ByVal unitOfWork As IUnitOfWork, ByVal errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        'Me.logger = logger
        'Me.mapper = mapper
        Me.errorMessageLog = errorMessageLog
    End Sub
#End Region

    Public Async Function GetDepartmentsAsync() As Task(Of ResponseModel) Implements IDepartmentService.GetDepartmentsAsync
        Try
            Dim departs = _unitOfWork.Repository(Of tm_department)().TableNoTracking.OrderBy(Function(t) t.Id) '.ToListAsync()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, departs)
        Catch ex As Exception
            Log(NameOf(Me.GetDepartmentsAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function GetDepartmentByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IDepartmentService.GetDepartmentByIdAsync
        Try
            Dim depart = Await _unitOfWork.Repository(Of tm_department)().Get(Id)
            If depart IsNot Nothing Then
                'Dim appSettingVm = mapper.Map(Of AppSettingVm)(appSetting)
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, depart)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetDepartmentByIdAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Public Async Function UpsertAsync(o As tm_department) As Task(Of ResponseModel) Implements IDepartmentService.UpsertAsync

        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tm_department)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tm_department)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try


    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IDepartmentService.DeleteAsync
        Try
            Dim depart = Await _unitOfWork.Repository(Of tm_department)().Get(Id)
            If depart IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_department).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)
            'If logger IsNot Nothing Then
            '    logger.LogError(ex.ToString())
            'End If
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function


#Region "Error"
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Department Service", method, msg)
    End Sub
#End Region

End Class
