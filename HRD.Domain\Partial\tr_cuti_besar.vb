Partial Public Class tr_cuti_besar
    Public Shared Function Create(
        Optional id As Integer = 0,
        Optional area_id As Integer = 0,
        Optional cabang_id As Integer = 0,
        Optional noCutiBesar As String = "",
        Optional tanggal As Date = Nothing,
        Optional tahun As String = "",
        Optional status As String = "DRAFT",
        Optional keterangan As String = "",
        Optional total As Decimal = 0,
        Optional createdBy As String = "",
        Optional createdDate As Date? = Nothing,
        Optional modifiedBy As String = "",
        Optional modifiedDate As Date? = Nothing,
        Optional postedBy As String = "",
        Optional postedDate As Date? = Nothing,
        Optional posted As Boolean = False) As tr_cuti_besar

        Return New tr_cuti_besar With {
            .Id = id,
            .Area_id = area_id,
            .Cabang_id = cabang_id,
            .NoCutiBesar = noCutiBesar,
            .Tanggal = tanggal,
            .Tahun = tahun,
            .Status = status,
            .Keterangan = keterangan,
            .Total = total,
            .CreatedBy = createdBy,
            .CreatedDate = createdDate,
            .ModifiedBy = modifiedBy,
            .ModifiedDate = modifiedDate,
            .PostedBy = postedBy,
            .PostedDate = postedDate,
            .Posted = posted
        }
    End Function

    ' Create method from existing object
    Public Shared Function Create(source As tr_cuti_besar) As tr_cuti_besar
        If source Is Nothing Then
            Return Nothing
        End If

        Return New tr_cuti_besar With {
            .Id = 0, ' Always create new record
            .Area_id = source.Area_id,
            .Cabang_id = source.Cabang_id,
            .NoCutiBesar = source.NoCutiBesar,
            .Tanggal = source.Tanggal,
            .Tahun = source.Tahun,
            .Status = source.Status,
            .Keterangan = source.Keterangan,
            .Total = source.Total,
            .CreatedBy = source.CreatedBy,
            .CreatedDate = source.CreatedDate,
            .ModifiedBy = source.ModifiedBy,
            .ModifiedDate = source.ModifiedDate,
            .PostedBy = source.PostedBy,
            .PostedDate = source.PostedDate,
            .Posted = source.Posted
        }
    End Function
End Class
