﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports MediatR
Imports StructureMap

Public Class GajiController
    Inherits Controller(Of tr_gaji)
    Implements IGajiListController, IGajiEditController

    Public ReadOnly Property DataList(area_id As String, bPosted As Boolean) As IQueryable(Of tr_gaji) Implements IGajiListController.DataList
        Get

            Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
            Dim r = Serv.GetGajisAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_gaji) = r.Output
                'Dim _user = AuthHelper.GetLoggedInUserInfo
                If area_id = "ALL" Then
                    os = os.Where(Function(f) f.Posted = bPosted)
                Else
                    os = os.Where(Function(f) f.Posted = bPosted And f.Area_id = area_id)
                End If

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property DataListGajiLine(gaji_id As String) As IQueryable(Of tr_gaji_line) Implements IGajiEditController.DataListGajiLine
        Get
            Dim Serv = ObjectFactory.GetInstance(Of GajiLineService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_gaji_line) = r.Output
                os = os.Where(Function(f) f.Gaji_id = gaji_id)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_gaji With {.Tanggal = Now, .Area_id = _user.Area_id}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
        Dim r = Serv.GetGajiByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_gaji)

        Dim absService = ObjectFactory.GetInstance(Of AbsensiService)()
        Dim tgl0 As Date = DateAdd(DateInterval.Month, -1, TEntity.Tanggal)
        Dim tgl1 As Date = Format(tgl0, "yyyy-MM-16")
        Dim tgl2 As Date = Format(TEntity.Tanggal, "yyyy-MM-20")


        Dim queryAllCabang As New GetAllTmCabangQuery
        Dim _cabangCommandHandler = ObjectFactory.GetInstance(Of TmCabangQueryHandler)

        Dim cabangs = _cabangCommandHandler.Handle(queryAllCabang).GetAwaiter.GetResult
        cabangs = cabangs.Where(Function(f) f.Area_id = TEntity.Area_id)
        If TEntity.Cabang_id <> Nothing Then
            cabangs = cabangs.Where(Function(f) f.Id = TEntity.Cabang_id)
        End If

        For Each x In cabangs
            absService.GenerateAbsensi(x.Id, tgl1, tgl2).GetAwaiter.GetResult()
        Next



        If Action = Action.AddNew Then
            TEntity.Tahun = Format(TEntity.Tanggal, "yyyy")
            TEntity.Bulan = Format(TEntity.Tanggal, "MM")
        End If

        Dim o As New tr_gaji With {.Area_id = TEntity.Area_id, .Cabang_id = TEntity.Cabang_id, .Bayar = TEntity.Bayar, .Bulan = TEntity.Bulan, .CreatedBy = TEntity.CreatedBy _
            , .CreatedDate = TEntity.CreatedDate, .Id = TEntity.Id, .Keterangan = TEntity.Keterangan, .KurangBayar = TEntity.KurangBayar, .ModifiedBy = TEntity.ModifiedBy _
            , .ModifiedDate = TEntity.ModifiedDate, .NoVoucher = TEntity.NoVoucher, .Posted = TEntity.Posted, .PostedBy = TEntity.PostedBy, .PostedDate = TEntity.PostedDate _
            , .Tahun = TEntity.Tahun, .Tanggal = TEntity.Tanggal, .TotalNet = TEntity.TotalNet, .TotalPendapatan = TEntity.TotalPendapatan, .TotalPotongan = TEntity.TotalPotongan}

        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If



        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Public Sub Posting(TEntity As tr_gaji) Implements IGajiEditController.Posting

        Dim o As New tr_gaji With {.Area_id = TEntity.Area_id, .Cabang_id = TEntity.Cabang_id, .Bayar = TEntity.Bayar, .Bulan = TEntity.Bulan, .CreatedBy = TEntity.CreatedBy _
            , .CreatedDate = TEntity.CreatedDate, .Id = TEntity.Id, .Keterangan = TEntity.Keterangan, .KurangBayar = TEntity.KurangBayar, .ModifiedBy = TEntity.ModifiedBy _
            , .ModifiedDate = TEntity.ModifiedDate, .NoVoucher = TEntity.NoVoucher, .Posted = TEntity.Posted, .PostedBy = TEntity.PostedBy, .PostedDate = TEntity.PostedDate _
            , .Tahun = TEntity.Tahun, .Tanggal = TEntity.Tanggal, .TotalNet = TEntity.TotalNet, .TotalPendapatan = TEntity.TotalPendapatan, .TotalPotongan = TEntity.TotalPotongan}

        Dim Serv = ObjectFactory.GetInstance(Of GajiService)()
        o.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        o.PostedDate = Now
        o.Posted = True

        Dim r = Serv.Posting(o, TEntity.tr_gaji_line.ToList).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IGajiListController
    Inherits IControllerMain, IControllerList


    ReadOnly Property DataList(area_id As String, bPosted As Boolean) As IQueryable(Of tr_gaji)


End Interface
Public Interface IGajiEditController
    Inherits IControllerMain, IControllerEdit(Of tr_gaji)

    ReadOnly Property DataListGajiLine(gaji_id As String) As IQueryable(Of tr_gaji_line)
    Sub Posting(TEntity As tr_gaji)
End Interface