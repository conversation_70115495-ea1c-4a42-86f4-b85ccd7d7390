﻿Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports DevExpress.Web.Data
Imports HRD.Controller
Imports HRD.Helpers
Imports ILS.MVVM
Imports HRD.Domain
Public Class ucBayarPinjaman_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IBayarPinjamanEditController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then
            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action

            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()
            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetEnableForm)
            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_bayar_pinjaman_release.PerformCallback('reset');}"
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_bayar_pinjaman_release.PerformCallback('save');s.SetEnabled(false);};}"
            Else
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_bayar_pinjaman.PerformCallback('reset');}"
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_bayar_pinjaman.PerformCallback('save');s.SetEnabled(false);};}"
            End If

            cb_area.ReadOnly = Not (oAction = Action.AddNew)
            cb_cabang.ReadOnly = Not (oAction = Action.AddNew)
            cb_karyawan.ReadOnly = Not (oAction = Action.AddNew)

            If Controller.Action = Action.View Then
                btn_save.Visible = False

            Else

                btn_save.Visible = True
                If Controller.Action = Action.Posting Then
                    btn_save.Text = "Release..."
                Else
                    btn_save.Text = "Save"
                End If
            End If
        Else
            Visible = False
        End If



    End Sub

    Sub SavingPrepare()
        Controller.Action = oAction

        If oAction = Action.Posting Then
            Controller.Posting(oSelectItem)
            Return
        End If

        With oSelectItem
            .Tanggal = Me.ASPxFormLayout1.GetNestedControlValueByFieldName("Tanggal")
            .Area_id = Me.ASPxFormLayout1.GetNestedControlValueByFieldName("Area_id")
            .Cabang_id = Me.ASPxFormLayout1.GetNestedControlValueByFieldName("Cabang_id")
            .Karyawan_id = Me.ASPxFormLayout1.GetNestedControlValueByFieldName("Karyawan_id")
            .Amount = If(Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True).Sum(Function(s) CType(s.Amount, Decimal?)), 0)

            .Memo = Me.ASPxFormLayout1.GetNestedControlValueByFieldName("Memo")

        End With

        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tr_pinjaman_bayar
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tr_pinjaman_bayar)
        End Get
        Set(value As tr_pinjaman_bayar)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()
        End If
    End Sub
    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub


    Protected Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        If Controller.SelectedItem Is Nothing Then
            Return
        End If
        Dim listId = Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True).Select(Function(s) s.Pinjaman_id)
        e.QueryableSource = Controller.PinjamanList(cb_karyawan.Value).Where(Function(f) Not listId.Contains(f.Id)).AsQueryable
    End Sub

    Private Sub ASPxGridView1_CustomUnboundColumnData(sender As Object, e As ASPxGridViewColumnDataEventArgs) Handles ASPxGridView1.CustomUnboundColumnData
        Select Case e.Column.FieldName
            Case "xBayar"
                If Controller Is Nothing Then
                    Return
                End If
                If Controller.SelectedItem Is Nothing Then
                    Return
                End If
                If e.IsGetData Then
                    e.Value = Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True And f.Bayar_id = Controller.SelectedItem.Id And f.Pinjaman_id = e.GetListSourceFieldValue("Id")).Sum(Function(s) s.Amount)
                End If
        End Select
    End Sub

    Private Sub ASPxGridView1_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView1.RowUpdating
        Dim sId As String = e.Keys("Id")
        Dim reg = Controller.PinjamanList(cb_karyawan.Value).Where(Function(f) f.Id = sId).FirstOrDefault
        Dim o = CType(Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Bayar_id = Controller.SelectedItem.Id And f.Pinjaman_id = reg.Id).FirstOrDefault, tr_pinjaman_bayar_line)
        Dim bAddNew As Boolean = False
        If o Is Nothing Then
            o = New tr_pinjaman_bayar_line
            bAddNew = True
        End If
        With o
            .Pinjaman_id = reg.Id
            .Amount = e.NewValues("xBayar")
            If .Amount <= 0 Then
                .Deleting = True
                If .Id <= 0 And bAddNew = False Then
                    Controller.SelectedItem.tr_pinjaman_bayar_line.Remove(o)
                End If
            Else
                If bAddNew Then
                    o.tr_register_pinjaman = reg
                    Controller.SelectedItem.tr_pinjaman_bayar_line.Add(o)
                End If
            End If
        End With
        Me.ASPxGridView1.JSProperties("cpUpdate") = True
        e.Cancel = True
        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowValidating(sender As Object, e As ASPxDataValidationEventArgs) Handles ASPxGridView1.RowValidating
        If e.NewValues("xBayar") > e.NewValues("Saldo") Then
            e.RowError = "Pembayaran Melebihi Sisa Pinjaman!"
        End If
    End Sub

    Private Sub ASPxGridView1_CommandButtonInitialize(sender As Object, e As ASPxGridViewCommandButtonEventArgs) Handles ASPxGridView1.CommandButtonInitialize
        If Controller Is Nothing Then
            Return
        End If
        Select Case e.ButtonType
            Case ColumnCommandButtonType.New, ColumnCommandButtonType.Edit, ColumnCommandButtonType.Delete
                e.Enabled = (oAction = Action.AddNew Or oAction = Action.Edit)
        End Select
    End Sub

    Protected Sub txt_totalbayar_Load(sender As Object, e As EventArgs)
        Dim txt = CType(sender, ASPxSpinEdit)
        If Controller Is Nothing Then
            txt.Value = 0
            Return
        End If
        If Controller.SelectedItem Is Nothing Then
            txt.Value = 0
            Return
        End If
        txt.Value = If(Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True).Sum(Function(s) CType(s.Amount, Decimal?)), 0)
    End Sub

    Protected Sub EntityServerModeDataSource2_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource2.Selecting
        If Controller Is Nothing Then

            Return
        End If
        If Controller.SelectedItem Is Nothing Then

            Return
        End If

        e.QueryableSource = Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True).AsQueryable
    End Sub

    Private Sub ASPxGridView2_CommandButtonInitialize(sender As Object, e As ASPxGridViewCommandButtonEventArgs) Handles ASPxGridView2.CommandButtonInitialize
        If Controller Is Nothing Then
            Return
        End If
        Select Case e.ButtonType
            Case ColumnCommandButtonType.New, ColumnCommandButtonType.Edit, ColumnCommandButtonType.Delete
                e.Enabled = (oAction = Action.AddNew Or oAction = Action.Edit)
        End Select
    End Sub

    Protected Sub txt_kurangbayar_Load(sender As Object, e As EventArgs)
        Dim txt = CType(sender, ASPxSpinEdit)
        If Controller Is Nothing Then
            txt.Value = 0
            Return
        End If
        If Controller.SelectedItem Is Nothing Then
            txt.Value = 0
            Return
        End If
        Dim listId = Controller.SelectedItem.tr_pinjaman_bayar_line.Where(Function(f) f.Deleting <> True).Select(Function(s) s.Pinjaman_id)
        Dim os = Controller.PinjamanList(cb_karyawan.Value).Where(Function(f) Not listId.Contains(f.Id)).AsQueryable

        txt.Value = If(os.Sum(Function(s) CType(s.Saldo, Decimal?)), 0)
    End Sub

    Private Sub ASPxGridView2_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView2.RowUpdating
        Dim o = CType(oSelectItem.tr_pinjaman_bayar_line.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tr_pinjaman_bayar_line)


        With o

            .Amount = e.NewValues("Amount")

        End With
        'Me.ASPxGridView2.JSProperties("cpUpdate") = True
        e.Cancel = True
        Me.ASPxGridView2.CancelEdit()
        Me.ASPxGridView2.DataBind()
    End Sub

    Private Sub ASPxGridView2_RowDeleting(sender As Object, e As ASPxDataDeletingEventArgs) Handles ASPxGridView2.RowDeleting
        Dim o = CType(oSelectItem.tr_pinjaman_bayar_line.Where(Function(f) f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tr_pinjaman_bayar_line)


        With o

            .Deleting = True
        End With
        Me.ASPxGridView2.JSProperties("cpDeleted") = True
        e.Cancel = True
        Me.ASPxGridView2.CancelEdit()
        Me.ASPxGridView2.DataBind()
    End Sub
End Class