﻿Public Module NullAndEmptyValueExtension
    'DateTime

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Date) As Boolean
        Return value = Date.MinValue
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Date) As Boolean
        If value IsNot Nothing Then
            Return CDate(value).IsEmpty()
        End If

        Return False
    End Function

    'string 

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As String) As Boolean
        Return value = String.Empty
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsNull(ByVal value As String) As Boolean
        Return value Is Nothing
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsInvalidKey(ByVal value As String) As Boolean
        Return (value.IsEmpty() OrElse String.IsNullOrWhiteSpace(value))
    End Function

    'byte array

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value() As Byte) As Boolean
        If value IsNot Nothing Then
            Return value.Length = 0
        End If

        Return False
    End Function

    'int

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Integer) As Boolean
        Return value = 0
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Integer) As Boolean
        If value IsNot Nothing Then
            Return CInt(value).IsEmpty()
        End If

        Return False
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsInvalidKey(ByVal value As Integer) As Boolean
        Return value <= 0
    End Function

    'big int

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Int64) As Boolean
        Return value = 0
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Int64) As Boolean
        If value IsNot Nothing Then
            Return CLng(value).IsEmpty()
        End If

        Return False
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsInvalidKey(ByVal value As Int64) As Boolean
        Return value <= 0
    End Function

    'float

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Single) As Boolean
        Return value = 0
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Single) As Boolean
        If value IsNot Nothing Then
            Return CSng(value).IsEmpty()
        End If

        Return False
    End Function

    'double

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Double) As Boolean
        Return value = 0
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Double) As Boolean
        If value IsNot Nothing Then
            Return CDbl(value).IsEmpty()
        End If

        Return False
    End Function

    'guid

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value As Guid) As Boolean
        Return value = Guid.Empty
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsEmpty(ByVal value? As Guid) As Boolean
        If value IsNot Nothing Then
            Return CType(value, Guid).IsEmpty()
        End If

        Return False
    End Function

    <System.Runtime.CompilerServices.Extension>
    Public Function IsInvalidKey(ByVal value As Guid) As Boolean
        Return value.IsEmpty()
    End Function

End Module
