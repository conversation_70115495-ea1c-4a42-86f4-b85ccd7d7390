﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="devExpress">
      <section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
    </sectionGroup>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <connectionStrings>
    <add name="HRDEntities" connectionString="metadata=res://*/ModelHrd.csdl|res://*/ModelHrd.ssdl|res://*/ModelHrd.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;initial catalog=HRD;persist security info=True;user id=sa;password=@masterKey123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/>
    <!--<add name="ApplicationServices" connectionString="data source=(localdb)\mssqllocaldb;integrated security=SSPI;attachdbfilename=|DataDirectory|\aspnetdb.mdf" providerName="System.Data.SqlClient" />-->
  </connectionStrings>
  <system.web>
    <compilation debug="true" targetFramework="4.7.2">
      <assemblies>
        <add assembly="DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxThemes.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.RichEdit.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.RichEdit.v23.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Printing.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraScheduler.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraScheduler.v23.1.Core.Desktop, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxScheduler.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraReports.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraReports.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraReports.v23.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Drawing.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Pdf.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.Resources.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Charts.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.CodeParser.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.DataAccess.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Office.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.PivotGrid.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Sparkline.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Xpo.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraCharts.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraGauges.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraReports.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraReports.v23.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.Resources.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.DataAccess.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="System.Web.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="DevExpress.XtraTreeList.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Utils.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.XtraEditors.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="DevExpress.XtraLayout.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Web.ASPxTreeList.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="System.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
      </assemblies>
    </compilation>
    <authentication mode="Forms">
      <forms loginUrl="~/Account/Login.aspx" timeout="2880"/>
    </authentication>
    <profile>
      <providers>
        <clear/>
        <add name="AspNetSqlProfileProvider" type="System.Web.Profile.SqlProfileProvider" connectionStringName="ApplicationServices" applicationName="/"/>
      </providers>
    </profile>
    <roleManager enabled="false">
      <providers>
        <clear/>
        <add name="AspNetSqlRoleProvider" type="System.Web.Security.SqlRoleProvider" connectionStringName="ApplicationServices" applicationName="/"/>
        <add name="AspNetWindowsTokenRoleProvider" type="System.Web.Security.WindowsTokenRoleProvider" applicationName="/"/>
      </providers>
    </roleManager>
    <httpHandlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET" path="DX.ashx" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" validate="false"/>
    </httpHandlers>
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
      <add name="PageControllerModule" type="ILS.MVVM.PageControllerModule, ILS.MVVM"/>
    </httpModules>
    <httpRuntime maxRequestLength="4096" requestValidationMode="4.0" executionTimeout="110" targetFramework="4.7.2"/>
    <pages validateRequest="true" clientIDMode="Predictable">
      <controls>
        <add tagPrefix="dx" namespace="DevExpress.Web" assembly="DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
      </controls>
    </pages>
    <globalization culture="" uiCulture=""/>
  </system.web>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true">
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
      <add name="PageControllerModule" type="ILS.MVVM.PageControllerModule, ILS.MVVM"/>
    </modules>
    <handlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" name="ASPxUploadProgressHandler" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" name="ASPxReportDesignerHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" name="ASPxWebDocumentViewerHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" name="ASPxQueryBuilderDesignerHandlerModule" preCondition="integratedMode"/>
    </handlers>
    <validation validateIntegratedModeConfiguration="false"/>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="30000000"/>
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IO.RecyclableMemoryStream" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <devExpress>
    <resources>
      <add type="ThirdParty"/>
      <add type="DevExtreme"/>
    </resources>
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="MaterialCompact" customThemeAssemblies="" baseColor="" font=""/>
    <compression enableHtmlCompression="true" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true"/>
    <settings accessibilityCompliant="false" doctypeMode="Html5" rightToLeft="false" checkReferencesToExternalScripts="true" protectControlState="true" ieCompatibilityVersion="edge"/>
    <errors callbackErrorRedirectUrl=""/>
  </devExpress>
  <appSettings>
    <add key="vs:EnableBrowserLink" value="false"/>
  </appSettings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
</configuration>