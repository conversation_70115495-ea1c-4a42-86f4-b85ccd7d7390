﻿Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Public Class AreaController
    Inherits Controller(Of tm_area)
    Implements IAreaListController, IAreaEditController

    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()
    Private Shared ReadOnly mgr As New AreaManager(daoFactory)

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_area
    End Sub

    Public Overrides Sub SelectItem(id As String)
        mgr.DataAccess.ResetContext()
        SelectedItem = mgr.DataAccess.GetById(id)
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim o = mgr.DataAccess.GetById(id)
            mgr.DataAccess.Delete(o)
            mgr.DataAccess.Save()
            Reset()
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try

    End Sub

    Public Overrides Sub Saving()
        Try
            Dim o = mgr.DataAccess.FindBy(Function(f) f.Id = SelectedItem.Id).FirstOrDefault
            If o Is Nothing Then
                o = New tm_area
            End If
            With o
                .KodeArea = SelectedItem.KodeArea
                .NamaArea = SelectedItem.NamaArea
                .CreatedBy = SelectedItem.CreatedBy
                .CreatedDate = SelectedItem.CreatedDate
                .ModifiedBy = SelectedItem.ModifiedBy
                .ModifiedDate = SelectedItem.ModifiedDate
                .KodeNIK_Tetap = SelectedItem.KodeNIK_Tetap
                .KodeNIK_Kontrak = SelectedItem.KodeNIK_Kontrak
                .NoRekeningBank = SelectedItem.NoRekeningBank
                .TglCustOffCuti = SelectedItem.TglCustOffCuti
                .FlatRateBPJS = SelectedItem.FlatRateBPJS

            End With
            If o.Id <= 0 Then
                o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.CreatedDate = Now
                mgr.DataAccess.Add(o)
            Else
                o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                o.ModifiedDate = Now
                mgr.DataAccess.Edit(o)
            End If

            mgr.DataAccess.Save()
            Saved = True
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving(o As tm_area)
        Throw New NotImplementedException()
    End Sub
End Class
Public Interface IAreaListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface IAreaEditController
    Inherits IControllerMain, IControllerEdit(Of tm_area)

End Interface