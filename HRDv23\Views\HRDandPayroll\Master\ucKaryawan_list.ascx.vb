﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Public Class ucKaryawan_list
    Inherits System.Web.UI.UserControl


    <Inject>
    Property Controller As IKaryawanListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            Dim _user = AuthHelper.GetLoggedInUserInfo
            cb_area.Value = _user.Area_id

        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        ' Ambil data karyawan dengan departemen
        Dim karyawanList = Controller.DataList(chk_berhenti.Checked, cb_area.Value, cb_cabang.Value)

        ' Tambahkan properti departemen untuk grouping
        Dim result = karyawanList.Select(Function(k) New With {
            .Id = k.Id,
            .Area_id = k.Area_id,
            .Cabang_id = k.Cabang_id,
            .NIK = k.NIK,
            .Nama = k.Nama,
            .Tempat = k.Tempat,
            .TglLahir = k.TglLahir,
            .Umur = k.Umur,
            .JenisKelamin_id = k.JenisKelamin_id,
            .Agama_id = k.Agama_id,
            .Alamat = k.Alamat,
            .Kota = k.Kota,
            .Telp = k.Telp,
            .StatusPerkawinan_id = k.StatusPerkawinan_id,
            .Pendidikan_id = k.Pendidikan_id,
            .Jurusan_id = k.Jurusan_id,
            .Alumni = k.Alumni,
            .TahunLulus = k.TahunLulus,
            .TglMasuk = k.TglMasuk,
            .Tgl_Berhenti = k.Tgl_Berhenti,
            .Berhenti_b = k.Berhenti_b,
            .AlasanBerhenti = k.AlasanBerhenti,
            .NoRekening = k.NoRekening,
            .NamaBank_id = k.NamaBank_id,
            .NamaAccount = k.NamaAccount,
            .Ship = k.Ship,
            .Version = k.Version,
            .NPWP = k.NPWP,
            .IsBPJS_Kes = k.IsBPJS_Kes,
            .IsBPJS_TK = k.IsBPJS_TK,
            .StatusPerkawinan_NonPTKP_id = k.StatusPerkawinan_NonPTKP_id,
            .KaryawanTetap = k.KaryawanTetap,
            .TipeAbsensiOFF = k.TipeAbsensiOFF,
            .Email = k.Email,
            .TglReloadCuti = k.TglReloadCuti,
            .NoKTP = k.NoKTP,
            .tm_agama = k.tm_agama,
            .tm_cabang = k.tm_cabang,
            .tm_jenis_kelamin = k.tm_jenis_kelamin,
            .tm_jurusan = k.tm_jurusan,
            .tm_namabank = k.tm_namabank,
            .tm_pendidikan = k.tm_pendidikan,
            .tm_status_perkawinan = k.tm_status_perkawinan,
            .tm_status_PTKP = k.tm_status_PTKP,
            .tm_area = k.tm_cabang.tm_area,
            .DepartemenName = If(k.tm_karyawan_datadinas.Any() AndAlso k.tm_karyawan_datadinas.OrderByDescending(Function(d) d.Id).FirstOrDefault().tm_department IsNot Nothing,
                                k.tm_karyawan_datadinas.OrderByDescending(Function(d) d.Id).FirstOrDefault().tm_department.NamaDepartemen,
                                "Tidak Ada Departemen")
        })

        e.QueryableSource = result.AsQueryable
    End Sub
    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim b As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Berhenti_b")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(b, 1, 0)
            Case "btn_view"
                e.Visible = If(b, 0, 1)


        End Select
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub
End Class