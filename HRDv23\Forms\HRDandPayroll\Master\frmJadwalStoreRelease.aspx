﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmJadwalStoreRelease.aspx.vb" Inherits="HRDv23.frmJadwalStoreRelease" %>

<%@ Register Src="~/Views/HRDandPayroll/Master/ucJadwalStore_release.ascx" TagPrefix="uc1" TagName="ucJadwalStore_release" %>
<%@ Register Src="~/Views/HRDandPayroll/Master/ucJadwalStore_edit.ascx" TagPrefix="uc1" TagName="ucJadwalStore_edit" %>


<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_jadwal_store_release" Width="100%">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Caption="List" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucJadwalStore_release runat="server" id="ucJadwalStore_release" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Edit" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucJadwalStore_edit runat="server" id="ucJadwalStore_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
