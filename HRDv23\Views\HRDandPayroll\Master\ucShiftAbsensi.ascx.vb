﻿Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Controller
Imports HRD.Helpers
Imports ILS.MVVM

Public Class ucShiftAbsensi
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IKaryawanListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()
        Me.ASPxGridView2.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = AuthHelper.GetLoggedInUserInfo.Cabang_id
        End If
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        e.QueryableSource = Controller.KaryawanNonShift(cb_cabang.Value).AsQueryable
    End Sub

    Friend Sub ClearSelectedItemsShift()
        Me.ASPxGridView2.Selection.UnselectAll()
        Me.ASPxGridView2.DataBind()
    End Sub

    Friend Function GetListIDShift() As IList(Of Integer)
        Dim listT As New List(Of Integer)
        For Each x In Me.ASPxGridView2.GetSelectedFieldValues("Id")
            listT.Add(x)
        Next
        Return listT
    End Function

    Friend Sub ClearSelectedItemsStandBy()
        Me.ASPxGridView1.Selection.UnselectAll()
        Me.ASPxGridView1.DataBind()
    End Sub

    Friend Function GetShift_id() As Integer
        Return cb_shift.Value
    End Function

    Friend Function GetListIDStandBy() As IList(Of Integer)
        Dim listT As New List(Of Integer)
        For Each x In Me.ASPxGridView1.GetSelectedFieldValues("Id")
            listT.Add(x)
        Next
        Return listT
    End Function

    Protected Sub EntityServerModeDataSource2_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource2.Selecting
        If Controller Is Nothing Then
            Return
        End If
        e.QueryableSource = Controller.KaryawanByShip(cb_cabang.Value, cb_shift.Value).AsQueryable
    End Sub
End Class