﻿Imports DevExpress.Web
Imports HRD.Domain
Imports HRD.Helpers

Public Class ctrl_karyawan_datadinas
    Inherits System.Web.UI.UserControl

    Private Property mData As tm_karyawan_datadinas
        Get
            Return Session(ViewState("_PageID") & "_data")
        End Get
        Set(value As tm_karyawan_datadinas)
            Session(ViewState("_PageID") & "_data") = value
        End Set
    End Property
    Property Data As tm_karyawan_datadinas
        Get
            If VisibleGrupPendapatan Then
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout2, TipeEnumFormLayout.SetValueToDomain, mData)
            Else
                With mData
                    .Area_id = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Area_id")
                    .Cabang_id = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Cabang_id")
                    .Karyawan_id = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Karyawan_id")
                    .Tanggal = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Tanggal")
                    .Department_id = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Department_id")
                    .Jabatan_id = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Jabatan_id")
                    .Tetap = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("Tetap")
                    .TglTetap = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("TglTetap")
                    .TglAwalKontrak = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("TglAwalKontrak")
                    .TglAkhirKontrak = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("TglAkhirKontrak")
                    .NoKontrakKerja = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("NoKontrakKerja")
                    .TglReloadCuti = Me.ASPxFormLayout2.GetNestedControlValueByFieldName("TglReloadCuti")

                End With
            End If


            Return mData
        End Get
        Set(value As tm_karyawan_datadinas)
            If value IsNot Nothing AndAlso value.tm_karyawan IsNot Nothing Then
                value.TglReloadCuti = value.tm_karyawan.TglReloadCuti
            End If

            Me.ASPxFormLayout2.DataSource = value
            mData = value

            If value IsNot Nothing AndAlso value.TglAwalKontrak.HasValue AndAlso value.TglAkhirKontrak.HasValue Then
                txt_durasi_kontrak.Value = DateDiff(DateInterval.Month, value.TglAwalKontrak.Value, value.TglAkhirKontrak.Value)
            Else
                txt_durasi_kontrak.Value = 0
            End If

            StatusTetapChanged()
        End Set
    End Property
    WriteOnly Property Enabled As Boolean
        Set(value As Boolean)
            If value Then
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout2, TipeEnumFormLayout.SetEnableForm)
            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout2, TipeEnumFormLayout.SetDisableForm)
            End If

        End Set
    End Property
    Private Sub StatusTetapChanged()
        If mData IsNot Nothing Then
            txt_tgl_tetap.ClientEnabled = mData.Tetap
            txt_tgl_awal_kontrak.ClientEnabled = Not mData.Tetap
            txt_tgl_akhir_kontrak.ClientEnabled = Not mData.Tetap
        End If
    End Sub
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
        StatusTetapChanged()
    End Sub
    Public Property VisibleGrupPendapatan As Boolean
        Get
            Return Me.ASPxFormLayout2.FindItemOrGroupByName("lg_pendapatan").Visible
        End Get
        Set(value As Boolean)
            Me.ASPxFormLayout2.FindItemOrGroupByName("lg_pendapatan").Visible = value
        End Set
    End Property
    Public WriteOnly Property EnableDataDinasInfo As Boolean
        Set(value As Boolean)
            cb_area.ReadOnly = Not value
            cb_cabang.ReadOnly = Not value
            cb_karyawan.ReadOnly = Not value
            txt_tanggal.ReadOnly = Not value
            cb_department.ReadOnly = Not value
            cb_jabatan.ReadOnly = Not value
            chk_tetap.ReadOnly = Not value
            txt_tgl_tetap.ReadOnly = Not value

            txt_tgl_awal_kontrak.ReadOnly = Not value
            txt_tgl_akhir_kontrak.ReadOnly = Not value
            txt_nokontrak.ReadOnly = Not value
        End Set
    End Property
    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub

    Protected Sub cb_department_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_department.ItemRequestedByValue
        MyMethod.Department_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_department_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_department.ItemsRequestedByFilterCondition
        MyMethod.Department_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_jabatan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_jabatan.ItemRequestedByValue
        MyMethod.Jabatan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_jabatan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_jabatan.ItemsRequestedByFilterCondition
        MyMethod.Jabatan_ItemsRequestedByFilterCondition(source, e)
    End Sub
End Class