﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Public Class LiburNasionalController
    Inherits Controller(Of tm_libur_nasional)
    Implements ILiburNasionalListController, ILiburNasionalEditController

    Public ReadOnly Property DataList(area_id As String) As IQueryable(Of tm_libur_nasional) Implements ILiburNasionalListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of LiburNasionalService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tm_libur_nasional) = r.Output
                os = os.Where(Function(f) f.Area_id = area_id)

                Return os.AsQueryable
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tm_libur_nasional With {.StartDate = Now, .EndDate = Now, .Area_id = _user.Area_id}

    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of LiburNasionalService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of LiburNasionalService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_libur_nasional)
        If TEntity.Id <= 0 Then
            TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        Else
            TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.ModifiedDate = Now
        End If

        ExpressMapper.Mapper.Register(Of tm_libur_nasional, tm_libur_nasional) _
            .Ignore(Function(dest) dest.tm_area) _
            .Ignore(Function(dest) dest.tr_absensi)


        Dim o = ExpressMapper.Mapper.Map(Of tm_libur_nasional, tm_libur_nasional)(TEntity)

        Dim Serv = ObjectFactory.GetInstance(Of LiburNasionalService)()

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface ILiburNasionalListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(area_id As String) As IQueryable(Of tm_libur_nasional)
End Interface
Public Interface ILiburNasionalEditController
    Inherits IControllerMain, IControllerEdit(Of tm_libur_nasional)

End Interface