﻿Partial Friend Class BusinessLayerHelper
    Public Shared Sub ThrowErrorForInvalidDataKey(ByVal dataFieldName As String)
        Throw New InvalidDataKeyException("Data field is not a valid data key. Data field name: " & dataFieldName)
    End Sub

    Public Shared Sub ThrowErrorForEmptyValue(ByVal dataFieldName As String)
        Throw New EmptyValueNotAllowedException("Data field is not allowed to be empty. Data field name: " & dataFieldName)
    End Sub

    Public Shared Sub ThrowErrorForNullValue(ByVal dataFieldName As String)
        Throw New NullValueNotAllowedException("Data field is not allowed to be null. Data field name: " & dataFieldName)
    End Sub
End Class
