﻿Imports System.Data.Entity
Imports System.Linq.Expressions
Imports HRD.Domain

Public Interface IRepository(Of T As Class)
    Function AddAsync(ByVal entity As T) As Task(Of T)
    Function AddRangeAsync(ByVal entity As List(Of T)) As Task(Of Boolean)
    Function AddRange(ByVal entity As List(Of T)) As Boolean
    Sub Update(ByVal entity As T)
    Function UpdateAsync(ByVal Id As Object, ByVal entity As T) As Task
    Function DeleteAsync(ByVal id As Integer) As Task(Of Boolean)
    Function Delete(ByVal entity As T) As Task(Of Boolean)
    Function Delete(ByVal where As Expression(Of Func(Of T, Boolean))) As Task(Of Boolean)
    Function [Get](ByVal id As Integer) As Task(Of T)
    Function [Get](ByVal where As Expression(Of Func(Of T, Boolean))) As Task(Of T)
    Function GetMany(ByVal where As Expression(Of Func(Of T, Boolean))) As IEnumerable(Of T)
    Function GetAll() As IEnumerable(Of T)
    Function Count(ByVal where As Expression(Of Func(Of T, Boolean))) As Task(Of Integer)
    Function Count() As Task(Of Integer)
#Region "Properties"

    ''' <summary>
    ''' Gets a table
    ''' </summary>
    ReadOnly Property Table() As IQueryable(Of T)

    ''' <summary>
    ''' Gets a table with "no tracking" enabled (EF feature) Use it only when you load record(s) only for read-only operations
    ''' </summary>
    ReadOnly Property TableNoTracking() As IQueryable(Of T)

    Function [Set](Of TEntity As Class)() As DbSet(Of TEntity)
    Function RemoveRange(entities As ICollection(Of T)) As Boolean

#End Region
End Interface
