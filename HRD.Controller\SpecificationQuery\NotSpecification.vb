﻿Imports System.Linq.Expressions

Public Class NotSpecification(Of T)
    Inherits CompositeSpecification(Of T)


    Private _innerSpecification As ISpecification(Of T)

    Public Sub New(ByVal innerSpecification As ISpecification(Of T))
        _innerSpecification = innerSpecification
    End Sub

    Public Overrides ReadOnly Property Criteria As Expression(Of Func(Of T, Boolean))

    Public Overrides Function SatisfyingEntitiesInQuery(query As IQueryable(Of T)) As IQueryable(Of T)
        Return _innerSpecification.SatisfyingEntitiesInQuery(query)
    End Function


    'Public Overrides Function IsSatisfiedBy(candidate As T) As Boolean
    '    Return Not _innerSpecification.IsSatisfiedBy(candidate)

    'End Function
End Class
