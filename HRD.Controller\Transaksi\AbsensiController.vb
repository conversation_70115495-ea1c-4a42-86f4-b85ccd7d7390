Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Application
Imports StructureMap
Imports HRD.Controller

Public Class AbsensiController
    Inherits Controller(Of tr_absensi)
    Implements IAbsensiListController, IAbsensiEditController

    Private ReadOnly _myFunction As IMyFunction

    Public Sub New()
        ' Constructor tanpa parameter untuk PageControllerBuilder
        _myFunction = StructureMap.ObjectFactory.GetInstance(Of IMyFunction)()
    End Sub

    Public Sub New(myFunction As IMyFunction)
        _myFunction = myFunction
    End Sub

    Public ReadOnly Property DataList(startDate As Date, endDate As Date, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_absensi) Implements IAbsensiListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()
            Dim r = Serv.GetAbsensisAsync.GetAwaiter.GetResult
            If r.Success Then

                Dim os As IQueryable(Of tr_absensi) = r.Output
                Dim tgl1 As Date = startDate.Date
                Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, endDate.Date)
                os = os.Where(Function(f) f.Tanggal >= tgl1 And f.Tanggal < tgl2)

                Dim spec = New AbsensiPermisionSpec
                os = spec.SatisfyingEntitiesInQuery(os)

                If area_id <> Nothing Then
                    os = os.Where(Function(f) f.Area_id = area_id)
                End If
                If cabang_id <> Nothing Then
                    os = os.Where(Function(f) f.Cabang_id = cabang_id)
                End If
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew

        Dim j_masuk As DateTime = Nothing
        Dim j_keluar As DateTime = Nothing
        Dim _user = AuthHelper.GetLoggedInUserInfo
        Dim ServSetting = ObjectFactory.GetInstance(Of SettingAbsenService)()
        Dim r = ServSetting.GetSettingAbsensAsync.GetAwaiter.GetResult
        If r.Success Then
            Dim os As IQueryable(Of tm_absensi_setting) = r.Output
            Dim o = os.Where(Function(f) f.Cabang_id = _user.Cabang_id).FirstOrDefault
            If o IsNot Nothing Then
                j_masuk = o.JamMasuk
                j_keluar = o.JamKeluar
            End If
        End If
        SelectedItem = New tr_absensi With {.Tanggal = Now, .Area_id = _user.Area_id, .Cabang_id = _user.Cabang_id, .Masuk_b = True, .MsJamMasuk = j_masuk, .MsJamKeluar = j_keluar}

    End Sub

    Public Sub UpdateJamMasukSelectedItem(getSelectedItem As List(Of Object), jamMasuk As DateTime)
        Dim serv = ObjectFactory.GetInstance(Of AbsensiService)()
        Dim r = serv.UpdateJamMasukSelectedItem(getSelectedItem, jamMasuk)
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()
        Dim r = Serv.GetAbsensiByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub
    Public Overrides Sub Saving(TEntity As tr_absensi)
        Try
            Dim oKar = GetKaryawan(TEntity.karyawan_id)
            If oKar Is Nothing Then
                SetError("Karyawan tidak ditemukan!")
                Return
            End If

            ' Get attendance settings based on date
            Dim shift As Integer = If(oKar.Ship.HasValue, oKar.Ship.Value, 0)
            Dim oSetting = GetAbsensiSettingByDate(TEntity.Cabang_id, TEntity.Tanggal, shift)
            If oSetting Is Nothing Then
                SetError("Setting Jam Absensi untuk cabang ini belum di setting!")
                Return
            End If

            If TEntity.Alpha_b Then
                TEntity.Keterangan = "Alpha"
            End If
            HandleAbsensiTypeSettings(TEntity, oSetting, oKar)

            If Not ValidateAndSetTimes(TEntity, oSetting, oKar) Then Return

            TEntity.Tanggal = TEntity.Tanggal.Date
            TEntity.FromAbsensi = True
            If TEntity.Id <= 0 Then
                TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.CreatedDate = Now
            Else
                TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                TEntity.ModifiedDate = Now
            End If

            Dim mappedEntity = MapEntity(TEntity)

            Dim result = SaveEntity(mappedEntity)
            If Not result.Success Then
                SetError(result.ErrorMessage)
            End If
        Catch ex As Exception
            ' Log the exception
            'Log(NameOf(Me.Saving), ex.Message)
            SetError("An error occurred while saving the entity.")
        End Try
    End Sub

    'Public Overrides Sub Saving(TEntity As tr_absensi)
    '    Dim oKar = GetKaryawan(TEntity.karyawan_id)
    '    If oKar Is Nothing Then
    '        SetError("Karyawan tidak ditemukan!")
    '        Return
    '    End If

    '    Dim oSetting = GetAbsensiSetting(TEntity.Cabang_id)
    '    If oKar.Ship.HasValue Then
    '        oSetting = GetAbsensiSetting(TEntity.Cabang_id, oKar.Ship.Value)
    '    End If
    '    If oSetting Is Nothing Then
    '        SetError("Setting Jam Absensi untuk cabang ini belum di setting!")
    '        Return
    '    End If


    '    If TEntity.Alpha_b Then
    '        TEntity.Keterangan = "Alpha"
    '    End If
    '    HandleAbsensiTypeSettings(TEntity, oSetting, oKar)

    '    If Not ValidateAndSetTimes(TEntity, oSetting, oKar) Then Return

    '    TEntity.Tanggal = TEntity.Tanggal.Date
    '    TEntity.FromAbsensi = True
    '    If TEntity.Id <= 0 Then
    '        TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
    '        TEntity.CreatedDate = Now
    '    Else
    '        TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
    '        TEntity.ModifiedDate = Now
    '    End If

    '    Dim mappedEntity = MapEntity(TEntity)

    '    If Not SaveEntity(mappedEntity) Then
    '        SetError("Failed to save entity.")
    '    End If
    'End Sub

    Private Function GetAbsensiSetting(cabangId As String) As tm_absensi_setting
        Return _myFunction.tm_absensi_setting(cabangId)
    End Function
    Private Function GetAbsensiSetting(cabangId As String, shift As String) As tm_absensi_setting
        Return _myFunction.tm_absensi_setting(cabangId, shift)
    End Function
    Private Function GetAbsensiSettingByDate(cabangId As String, tanggal As Date, Optional shift As Integer = 0) As tm_absensi_setting
        Return _myFunction.tm_absensi_setting_by_date(cabangId, tanggal, shift)
    End Function

    Private Function GetKaryawan(karyawanId As Integer) As tm_karyawan
        Return _myFunction.tm_karyawan(karyawanId)
    End Function

    Private Sub HandleAbsensiTypeSettings(TEntity As tr_absensi, oSetting As tm_absensi_setting, oKar As tm_karyawan)
        If oKar.TipeAbsensiOFF = 0 Then
            TEntity.MsJamMasuk = FormatDateTimeForAbsensi(oSetting.JamMasuk, TEntity.Tanggal)
            If oSetting.JamMasukJumat.HasValue Then
                If _myFunction.CekHariJumat(TEntity.Tanggal) Then
                    TEntity.MsJamMasuk = FormatDateTimeForAbsensi(oSetting.JamMasukJumat, TEntity.Tanggal)
                End If
            End If
            TEntity.MsJamKeluar = FormatDateTimeForAbsensi(oSetting.JamKeluar, TEntity.Tanggal)
            If oSetting.JamKeluarJumat.HasValue Then
                If _myFunction.CekHariJumat(TEntity.Tanggal) Then
                    TEntity.MsJamKeluar = FormatDateTimeForAbsensi(oSetting.JamKeluarJumat, TEntity.Tanggal)
                End If
            End If
            If oSetting.JamKeluarSabtu.HasValue Then
                If _myFunction.CekHariSabtu(TEntity.Tanggal) Then
                    TEntity.MsJamKeluar = FormatDateTimeForAbsensi(oSetting.JamKeluarSabtu, TEntity.Tanggal)
                End If
            End If
            If oSetting.JamKeluarMinggu.HasValue Then
                If _myFunction.CekHariMinggu(TEntity.Tanggal) Then
                    TEntity.MsJamKeluar = FormatDateTimeForAbsensi(oSetting.JamKeluarMinggu, TEntity.Tanggal)
                End If
            End If

        ElseIf oKar.TipeAbsensiOFF = 1 Then
            Dim set_abs_off = _myFunction.GetSettingAbsensi_OFF(TEntity.karyawan_id, TEntity.Tanggal)
            If set_abs_off.sKeterangan IsNot Nothing Then
                SetError(set_abs_off.sKeterangan)
                Return
            End If
            If set_abs_off.bOff Then
                TEntity.Off_b = True
            Else
                TEntity.MsJamMasuk = set_abs_off.dMSmasuk
                TEntity.MsJamKeluar = set_abs_off.dMSkeluar
            End If
        End If
    End Sub

    Private Function ValidateAndSetTimes(TEntity As tr_absensi, oSetting As tm_absensi_setting, oKar As tm_karyawan) As Boolean
        If TEntity.Masuk_b AndAlso Not TEntity.JamMasuk.HasValue Then
            SetError("Jam Masuk Wajib Isi!")
            Return False
        End If
        If TEntity.Masuk_b AndAlso Not TEntity.JamKeluar.HasValue Then
            SetError("Jam Keluar Wajib Isi!")
            Return False
        End If

        CalculateLateAndEarlyTimes(TEntity, oSetting, oKar)

        Return True
    End Function

    Private Function MapEntity(TEntity As tr_absensi) As tr_absensi
        ExpressMapper.Mapper.Register(Of tr_absensi, tr_absensi)() _
        .Ignore(Function(i) i.tm_area) _
        .Ignore(Function(i) i.tm_cabang) _
        .Ignore(Function(i) i.tm_karyawan) _
        .Ignore(Function(i) i.tr_ijin) _
        .Ignore(Function(i) i.tr_spk_lembur_line)


        Return ExpressMapper.Mapper.Map(Of tr_absensi, tr_absensi)(TEntity)
    End Function
    Private Function SaveEntity(entity As tr_absensi) As (Success As Boolean, ErrorMessage As String)
        Try
            Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()
            Dim r = Serv.UpsertAsync(entity).GetAwaiter.GetResult
            If r.Success Then
                Saved = True
                Return (True, String.Empty)
            Else
                Return (False, r.Message)
            End If
        Catch ex As Exception
            ' Log the exception
            'Log(NameOf(Me.SaveEntity), ex.Message)
            Return (False, "An error occurred while saving the entity.")
        End Try
    End Function

    'Private Function SaveEntity(entity As tr_absensi) As Boolean
    '    Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()
    '    Dim r = Serv.UpsertAsync(entity).GetAwaiter.GetResult
    '    If r.Success Then
    '        Saved = True
    '        Return True
    '    Else
    '        Return False
    '    End If
    'End Function

    Private Sub SetError(message As String)
        sMsg = MessageFormatter.GetFormattedErrorMessage(message)
        Saved = False
    End Sub

    Private Function FormatDateTimeForAbsensi(time As DateTime, _date As DateTime) As DateTime
        Return DateTime.ParseExact($"{_date:yyyy-MM-dd} {time:HH:mm}", "yyyy-MM-dd HH:mm", Nothing)
    End Function

    Private Sub CalculateLateAndEarlyTimes(TEntity As tr_absensi, oSetting As tm_absensi_setting, oKar As tm_karyawan)
        ' Implementation for calculating late and early times
        If TEntity.Masuk_b = False Then
            TEntity.Terlambat = 0
            TEntity.PulangCepat = 0
            TEntity.JamMasuk = Nothing
            TEntity.JamKeluar = Nothing
            TEntity.Lembur_HKerja_1 = 0
            TEntity.Lembur_HKerja_2 = 0
            TEntity.Lembur_HKerja_3 = 0
            TEntity.Lembur_HLibur_1_7 = 0
            TEntity.Lembur_HLibur_8 = 0
            TEntity.Lembur_HLibur_9_10 = 0
            TEntity.Overtime = 0
            TEntity.OverTime_HLibur = 0
            Return
        End If
        If oKar.TipeAbsensiOFF = 2 Then
            TEntity.JamMasuk = Format(TEntity.Tanggal, $"yyyy-MM-dd {Format(TEntity.JamMasuk, "HH:mm")}")
            TEntity.JamKeluar = Format(TEntity.Tanggal, $"yyyy-MM-dd {Format(TEntity.JamKeluar, "HH:mm")}")
            TEntity.Terlambat = DateDiff(DateInterval.Minute, TEntity.JamMasuk.Value, TEntity.JamKeluar.Value)

            TEntity.Terlambat = (8 * 60) - (TEntity.Terlambat)
            If TEntity.Terlambat <= oSetting.ToleransiJamMasuk Then
                TEntity.Terlambat = 0
            End If
        Else
            TEntity.JamMasuk = Format(TEntity.Tanggal, $"yyyy-MM-dd {Format(TEntity.JamMasuk, "HH:mm")}")
            TEntity.Terlambat = DateDiff(DateInterval.Minute, TEntity.MsJamMasuk.Value, TEntity.JamMasuk.Value)
            If TEntity.Terlambat <= oSetting.ToleransiJamMasuk Then
                TEntity.Terlambat = 0
            End If

            TEntity.JamKeluar = Format(TEntity.Tanggal, $"yyyy-MM-dd {Format(TEntity.JamKeluar, "HH:mm")}")
            Dim ovt As Integer = DateDiff(DateInterval.Minute, TEntity.MsJamKeluar.Value, TEntity.JamKeluar.Value)
            If ovt < 0 Then
                TEntity.PulangCepat = ovt * (-1)
            Else
                TEntity.PulangCepat = 0
            End If

            If _myFunction.UpdateAbsensiDataLembur(TEntity, oKar) Then


            End If
        End If
    End Sub

    Public Function GetSettingAbsen(cabang_id As String) As tm_absensi_setting Implements IAbsensiEditController.GetSettingAbsen
        ' Use the new function that considers special day settings
        Return _myFunction.tm_absensi_setting_by_date(cabang_id, DateTime.Now.Date)
    End Function
    Sub SaveImport(dt As DataTable)

        Dim Serv = ObjectFactory.GetInstance(Of AbsensiService)()

        Dim r = Serv.SaveImport(dt).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If


    End Sub

    Sub DeleteSelectedItem(items As IList(Of Object))
        Dim serv = ObjectFactory.GetInstance(Of AbsensiService)()
        Dim r = serv.DeleteSelectedItem(items)
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub
End Class
Public Interface IAbsensiListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(startDate As Date, endDate As Date, Optional area_id As String = Nothing, Optional cabang_id As String = Nothing) As IQueryable(Of tr_absensi)
End Interface
Public Interface IAbsensiEditController
    Inherits IControllerMain, IControllerEdit(Of tr_absensi)

    Function GetSettingAbsen(cabang_id As String) As tm_absensi_setting
End Interface