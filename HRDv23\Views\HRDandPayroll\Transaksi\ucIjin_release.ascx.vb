﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Public Class ucIjin_release
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IIjinListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.DefaultSorting = "Id Desc"
        e.QueryableSource = Controller.DataList(False).AsQueryable
    End Sub
End Class