﻿Imports HRD.Domain
Public Interface IIjinService
    Function GetIjinsAsync() As Task(Of ResponseModel)
    Function GetIjinByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_ijin) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function PostingAsync(ByVal o As tr_ijin) As Task(Of ResponseModel)
    Function UnPostingAsync(o As tr_ijin) As Task(Of ResponseModel)
End Interface
