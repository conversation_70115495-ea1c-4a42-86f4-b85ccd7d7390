﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web.Data
Imports DevExpress.Web

Public Class ucIjin_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IIjinEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            oSelectItem = Controller.SelectedItem
            oAction = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            ASPxFormLayout1.DataBind()

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                ElseIf Controller.Action = Action.UnPosting Then
                    btn_save.Text = "Un-Release..."
                End If
            End If


        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = oAction

        If Controller.Action = Action.Posting Then
            Controller.Saving(oSelectItem)
            Return
        End If

        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelectItem)

        If oSelectItem.Photo IsNot Nothing Then
            oSelectItem.Photo = myFunction.CreateThumbnail(oSelectItem.Photo, 1500)
        End If

        Controller.Saving(oSelectItem)

    End Sub

    Property oSelectItem As tr_ijin
        Get
            Return CType(Session(ViewState("_PageID").ToString()), tr_ijin)
        End Get
        Set(value As tr_ijin)
            Session(ViewState("_PageID").ToString()) = value
        End Set
    End Property
    Private Property oAction As Action
        Get
            Return Session(ViewState("_PageID").ToString() & "_Action")
        End Get
        Set(value As Action)
            Session(ViewState("_PageID").ToString() & "_Action") = value
        End Set
    End Property
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()

        End If
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub cb_karyawan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_karyawan.ItemRequestedByValue
        MyMethod.Karyawan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_karyawan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_karyawan.ItemsRequestedByFilterCondition
        MyMethod.Karyawan_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub

    Private Sub cb_ijin_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_ijin.ItemRequestedByValue
        MyMethod.Ijin_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_ijin_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_ijin.ItemsRequestedByFilterCondition
        MyMethod.Ijin_ItemsRequestedByFilterCondition(source, e)
    End Sub
End Class