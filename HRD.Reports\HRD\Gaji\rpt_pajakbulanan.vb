﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Imports HRD.Helpers

Public Class rpt_pajakbulanan
    Public Periode As Date
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint
        Static i As Integer

        i += 1

        txt_no.Text = $"{i}."
    End Sub

    Private Sub fMasaPajak_GetValue(sender As Object, e As GetValueEventArgs) Handles fMasaPajak.GetValue
        e.Value = Periode.Month
    End Sub

    Private Sub fTahunPajak_GetValue(sender As Object, e As GetValueEventArgs) Handles fTahunPajak.GetValue
        e.Value = Periode.Year
    End Sub

    Private Sub fStatusPajak_GetValue(sender As Object, e As GetValueEventArgs) Handles fStatusPajak.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If



        ' Default to the employee's status
        e.Value = o.tm_status_PTKP.KodeStatus

        ' Find the first posted salary line for the given period
        Dim gjLine = o.tr_gaji_line.FirstOrDefault(Function(f) f.tr_gaji.Posted AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year)

        ' If a salary line is found and it has a StatusPTKP_id, use its status
        If gjLine?.StatusPTKP_id.HasValue Then
            e.Value = gjLine.tm_status_PTKP.KodeStatus
        End If
    End Sub

    Private Sub fJabatan_GetValue(sender As Object, e As GetValueEventArgs) Handles fJabatan.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If

        Dim gjLine = o.tr_gaji_line.Where(Function(f) f.tr_gaji.Posted = True AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).FirstOrDefault
        If gjLine Is Nothing Then
            If o.tm_karyawan_datadinas.Any Then
                Dim dn = o.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).FirstOrDefault
                e.Value = dn.tm_jabatan.Jabatan
            End If
            Return
        End If
        If gjLine.DataDinas_id.HasValue Then
            e.Value = gjLine.tm_karyawan_datadinas.tm_jabatan.Jabatan
        End If
    End Sub

    Private Sub fPenhasilanBruto_GetValue(sender As Object, e As GetValueEventArgs) Handles fPenhasilanBruto.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If

        e.Value = 0

        Dim gjLine = o.tr_gaji_line.Where(Function(f) f.tr_gaji.Posted = True AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).FirstOrDefault
        If gjLine IsNot Nothing Then
            e.Value = gjLine.TotalPendapatan
        End If
    End Sub

    Private Sub fTarifTer_GetValue(sender As Object, e As GetValueEventArgs) Handles fTarifTer.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If

        e.Value = 0

        Dim gjLine = o.tr_gaji_line.Where(Function(f) f.tr_gaji.Posted = True AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).FirstOrDefault
        If gjLine IsNot Nothing Then
            If gjLine.Ter_pajak_id.HasValue Then
                e.Value = gjLine.Ter_pajak_percent

                Return
            End If
            Dim r = myFunction.PPh21CalculatorBulanan(gjLine.TotalPendapatan, o.tm_status_PTKP.KodeStatus)
            e.Value = r.tarifPercent
        End If
    End Sub

    Private Sub fTarifPPh_GetValue(sender As Object, e As GetValueEventArgs) Handles fTarifPPh.GetValue
        Dim o = CType(e.Row, tm_karyawan)

        If o Is Nothing Then
            Return
        End If

        e.Value = 0

        Dim gjLine = o.tr_gaji_line.Where(Function(f) f.tr_gaji.Posted = True AndAlso f.tr_gaji.Tanggal.Month = Periode.Month AndAlso f.tr_gaji.Tanggal.Year = Periode.Year).FirstOrDefault
        If gjLine IsNot Nothing Then
            e.Value = gjLine.Pt_PPH21
        End If
    End Sub

    Private Sub fTglPrmotongan_GetValue(sender As Object, e As GetValueEventArgs) Handles fTglPrmotongan.GetValue
        ' Hitung tanggal terakhir bulan berdasarkan periode
        Dim lastDayOfMonth As Integer = DateTime.DaysInMonth(Periode.Year, Periode.Month)

        ' Format tanggal menjadi "yyyy-MM-dd" dengan hari terakhir bulan
        e.Value = New DateTime(Periode.Year, Periode.Month, lastDayOfMonth)
    End Sub
End Class