﻿Imports HRD.Application
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class StatusPerkawinanController
    Inherits Controller(Of tm_status_perkawinan)
    Implements IStatusPerkawinanListController, IStatusPerkawinanEditController

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        SelectedItem = New tm_status_perkawinan
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of StatusPerkawinanService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of StatusPerkawinanService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_status_perkawinan)
        ExpressMapper.Mapper.Register(Of tm_status_perkawinan, tm_status_perkawinan)() _
            .Ignore(Function(x) x.tm_karyawan)

        Dim o = ExpressMapper.Mapper.Map(Of tm_status_perkawinan, tm_status_perkawinan)(TEntity)

        Dim Serv = ObjectFactory.GetInstance(Of StatusPerkawinanService)()

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IStatusPerkawinanListController
    Inherits IControllerMain, IControllerList

End Interface
Public Interface IStatusPerkawinanEditController
    Inherits IControllerMain, IControllerEdit(Of tm_status_perkawinan)

End Interface