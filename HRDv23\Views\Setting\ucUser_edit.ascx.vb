﻿Imports ILS.MVVM
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports DevExpress.Web
Imports DevExpress.Web.Data
Imports StructureMap
Imports HRD.Application

Public Class ucUser_edit
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IUserEditController


    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        If Controller.SelectedItem IsNot Nothing Then

            Visible = True

            Session(ViewState("_PageID").ToString()) = Controller.SelectedItem
            Session(ViewState("_PageID").ToString() & "_Action") = Controller.Action



            ASPxFormLayout1.DataSource = Controller.SelectedItem
            Me.ASPxFormLayout1.DataBind()


            If Controller.SelectedItem.tm_user_area IsNot Nothing Then
                For Each area In Controller.SelectedItem.tm_user_area
                    Dim chk = chkList_perusahaan.Items.FindByValue(area.Area_id)
                    chk.Selected = True
                Next
            End If

            If Controller.Action = Action.AddNew Or Controller.Action = Action.Edit Then

            Else
                MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetDisableForm)
            End If

            If Controller.Action = Action.Posting Then
                'btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_ijin_release.PerformCallback('save');s.SetEnabled(false);};}"
                'btn_back.ClientSideEvents.Click = "function(s, e) {cp_ijin_release.PerformCallback('back');}"

                btn_save.Text = "Release"
            Else
                btn_save.ClientSideEvents.Click = "function(s, e) {if (ASPxClientEdit.ValidateGroup(null) == true) {cp_user.PerformCallback('save');s.SetEnabled(false);};}"
                btn_back.ClientSideEvents.Click = "function(s, e) {cp_user.PerformCallback('back');}"

                If Controller.Action = Action.View Then
                    btn_save.Visible = False
                End If
            End If

            txt_password.ReadOnly = Not (Controller.Action = Action.AddNew)
        Else
            Visible = False
        End If



    End Sub


    Sub Saving()
        Controller.Action = Session(ViewState("_PageID").ToString() & "_Action")

        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)


        MyMethod.FormLayoutMyMethod(Me.ASPxFormLayout1, TipeEnumFormLayout.SetValueToDomain, oSelect)

        oSelect.tm_user_area.Clear()
        For Each x In chkList_perusahaan.SelectedValues

            oSelect.tm_user_area.Add(New tm_user_area With {.Area_id = x, .User_id = oSelect.Id})
        Next

        Controller.SelectedItem = oSelect
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ViewState("_PageID") = (New Random()).Next().ToString()
        Else

        End If
        'BindingAreaList()

    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Protected Sub cb_role_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs)
        MyMethod.Role_ItemRequestedByValue(source, e)
    End Sub

    Protected Sub cb_role_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs)
        MyMethod.Role_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Protected Sub EntityServerModeDataSource1_Selecting(sender As Object, e As DevExpress.Data.Linq.LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If
        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)
        If oSelect Is Nothing Then
            Return
        End If

        e.QueryableSource = oSelect.tm_user_role.Where(Function(f) f.Deleting <> True).AsQueryable
    End Sub

    Private Sub ASPxGridView1_RowInserting(sender As Object, e As ASPxDataInsertingEventArgs) Handles ASPxGridView1.RowInserting
        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)
        Dim o As New tm_user_role
        With o
            .Role_id = e.NewValues("Role_id")

            oSelect.tm_user_role.Add(o)
        End With
        e.Cancel = True

        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowUpdating(sender As Object, e As ASPxDataUpdatingEventArgs) Handles ASPxGridView1.RowUpdating
        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)
        Dim o = CType(oSelect.tm_user_role.Where(Function(f) f.Deleting <> True And f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_user_role)
        With o
            .Role_id = e.NewValues("Role_id")
        End With
        e.Cancel = True

        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowDeleting(sender As Object, e As ASPxDataDeletingEventArgs) Handles ASPxGridView1.RowDeleting
        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)
        Dim o = CType(oSelect.tm_user_role.Where(Function(f) f.Deleting <> True And f.RowGuid = e.Keys("RowGuid")).FirstOrDefault, tm_user_role)
        With o
            .Deleting = True
        End With
        e.Cancel = True

        Me.ASPxGridView1.CancelEdit()
        Me.ASPxGridView1.DataBind()
    End Sub

    Private Sub ASPxGridView1_RowValidating(sender As Object, e As ASPxDataValidationEventArgs) Handles ASPxGridView1.RowValidating
        Dim oSelect = CType(Session(ViewState("_PageID").ToString()), tm_user)
        Dim os = oSelect.tm_user_role.Where(Function(f) f.Deleting <> True And f.RowGuid <> e.Keys("RowGuid"))
        os = os.Where(Function(f) f.Role_id = e.NewValues("Role_id"))
        If os.FirstOrDefault IsNot Nothing Then
            e.RowError = "Role ini sudah ada di list!"
        End If
    End Sub
    Private Sub BindingAreaList()
        If Controller Is Nothing Then
            Return
        End If
        Dim result = Controller.GetAreaList

        chkList_perusahaan.DataSource = result
        'chkList_perusahaan.TextField = "NamaArea"
        'chkList_perusahaan.ValueField = "Id"
        chkList_perusahaan.DataBind()
    End Sub


End Class