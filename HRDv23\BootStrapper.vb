﻿
Imports HRD.Application
Imports HRD.Infrastructure
Imports StructureMap
Imports StructureMap.Configuration.DSL

Public Class BootStrapper
    Public Shared Sub ConfigureDependencies()
        ' Initialize the registry
        ObjectFactory.Initialize(Sub(x)
                                     x.AddRegistry(Of ControllerRegistry)()

                                 End Sub)



    End Sub

    Public Class ControllerRegistry
        Inherits Registry

        Public Sub New()
            ForRequestedType(Of IUnitOfWork)().TheDefault.Is.OfConcreteType(Of UnitOfWork)()
            ForRequestedType(Of IErrorMessageLog)().TheDefault.Is.OfConcreteType(Of ErrorMessageLog)()
            ForRequestedType(Of IMyFunction)().TheDefault.Is.OfConcreteType(Of MyFunctionInfra)()



        End Sub
    End Class
End Class
