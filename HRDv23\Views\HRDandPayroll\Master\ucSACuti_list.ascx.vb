﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers
Imports DevExpress.Web.Data

Public Class ucSACuti_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IDataDinasListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.SelectedIndex = 0
            cb_cabang.SelectedIndex = 0
        End If
    End Sub
    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.QueryableSource = Controller.DataListEntriSACuti(cb_cabang.Value)
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub

    Private Sub ASPxGridView1_BatchUpdate(sender As Object, e As ASPxDataBatchUpdateEventArgs) Handles ASPxGridView1.BatchUpdate
        'For Each args As ASPxDataUpdateValues In e.UpdateValues
        '    Dim oldValues As OrderedDictionary = args.OldValues
        '    Dim newValues As OrderedDictionary = args.NewValues

        '    If IsRowHasChanges(oldValues, newValues) Then
        '        Dim _id As String = args.Keys("Id")
        '        Dim _cuti As String = args.NewValues("SaldoAwalCuti")
        '        Controller.UpdateDataCuti(_id, _cuti)
        '    End If
        'Next

        'e.Handled = True

        'Me.ASPxGridView1.DataBind()

        Dim updates As New List(Of KeyValuePair(Of String, String))()

        For Each args As ASPxDataUpdateValues In e.UpdateValues
            Dim oldValues As OrderedDictionary = args.OldValues
            Dim newValues As OrderedDictionary = args.NewValues

            If IsRowHasChanges(oldValues, newValues) Then
                Dim _id As String = args.Keys("Id").ToString()
                Dim _cuti As String = args.NewValues("SaldoAwalCuti").ToString()
                updates.Add(New KeyValuePair(Of String, String)(_id, _cuti))
            End If
        Next

        Try
            ' Assuming Controller.UpdateDataCutiBulk is a method you implement to handle bulk updates
            Controller.UpdateDataCutiBulk(updates)
        Catch ex As Exception
            ' Handle the exception (e.g., log the error and notify the user)
        End Try

        e.Handled = True
        Me.ASPxGridView1.DataBind()

    End Sub
    Protected Function IsRowHasChanges(ByVal oldParamters As OrderedDictionary, ByVal newParamters As OrderedDictionary) As Boolean
        Dim hasChanges As Boolean = False

        For i As Integer = 0 To newParamters.Count - 1
            If newParamters(i) = Nothing Then
                If oldParamters(i) <> Nothing Then
                    hasChanges = True
                    Exit For
                End If
            Else
                If Not newParamters(i).Equals(oldParamters(i)) Then
                    hasChanges = True
                    Exit For
                End If
            End If

        Next i

        Return hasChanges
    End Function
End Class