﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated. 
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On


Partial Public Class Root
    
    '''<summary>
    '''Head control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents Head As Global.System.Web.UI.WebControls.ContentPlaceHolder
    
    '''<summary>
    '''form1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents form1 As Global.System.Web.UI.HtmlControls.HtmlForm
    
    '''<summary>
    '''HeaderPanel control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents HeaderPanel As Global.DevExpress.Web.ASPxPanel
    
    '''<summary>
    '''LeftAreaMenu control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents LeftAreaMenu As Global.DevExpress.Web.ASPxMenu
    
    '''<summary>
    '''RightAreaMenu control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents RightAreaMenu As Global.DevExpress.Web.ASPxMenu
    
    '''<summary>
    '''ApplicationMenu control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ApplicationMenu As Global.DevExpress.Web.ASPxMenu
    
    '''<summary>
    '''LeftPanel control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents LeftPanel As Global.DevExpress.Web.ASPxPanel
    
    '''<summary>
    '''LeftPanelContent control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents LeftPanelContent As Global.System.Web.UI.WebControls.ContentPlaceHolder
    
    '''<summary>
    '''RightPanel control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents RightPanel As Global.DevExpress.Web.ASPxPanel
    
    '''<summary>
    '''RightPanelContent control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents RightPanelContent As Global.System.Web.UI.WebControls.ContentPlaceHolder
    
    '''<summary>
    '''PageToolbarPanel control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents PageToolbarPanel As Global.DevExpress.Web.ASPxPanel
    
    '''<summary>
    '''PageToolbar control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents PageToolbar As Global.System.Web.UI.WebControls.ContentPlaceHolder
    
    '''<summary>
    '''SiteMapPath1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents SiteMapPath1 As Global.System.Web.UI.WebControls.SiteMapPath
    
    '''<summary>
    '''PageContent control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents PageContent As Global.System.Web.UI.WebControls.ContentPlaceHolder
End Class
