﻿Imports System.Linq.Expressions
Imports HRD.Controller

Public MustInherit Class CompositeSpecification(Of T)
    Implements ISpecification(Of T)

    Public MustOverride ReadOnly Property Criteria As Expression(Of Func(Of T, Boolean)) Implements ISpecification(Of T).Criteria

    Public MustOverride Function SatisfyingEntitiesInQuery(query As IQueryable(Of T)) As IQueryable(Of T) Implements ISpecification(Of T).SatisfyingEntitiesInQuery

    Public Function [And](other As ISpecification(Of T)) As ISpecification(Of T) Implements ISpecification(Of T).And
        Return New AndSpecification(Of T)(Me, other)
    End Function

    Public Function [Not]() As ISpecification(Of T) Implements ISpecification(Of T).Not
        Return New NotSpecification(Of T)(Me)
    End Function
End Class
