﻿Imports HRD.Domain
Public Class ObjectContextManager
    'Private Shared _context As New HRDEntities

    'Private Sub New()
    'End Sub

    'Public Shared ReadOnly Property Context As HRDEntities
    '    Get
    '        Return _context
    '    End Get
    'End Property

    'Public Shared Sub ResetContext()
    '    If _context IsNot Nothing Then
    '        _context.Dispose()
    '    End If
    '    _context = New HRDEntities()
    'End Sub

    <ThreadStatic>
    Private Shared _context As HRDEntities

    Private Sub New()
    End Sub

    Public Shared ReadOnly Property Context As HRDEntities
        Get
            If _context Is Nothing Then
                _context = New HRDEntities()
            End If
            Return _context
        End Get
    End Property

    Public Shared Sub ResetContext()
        If _context IsNot Nothing Then
            _context.Dispose()
            _context = New HRDEntities()
        End If
    End Sub
End Class
