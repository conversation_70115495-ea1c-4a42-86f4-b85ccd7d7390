﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmStatusPerkawinan.aspx.vb" Inherits="HRDv23.frmStatusPerkawinan" %>

<%@ Register Src="~/Views/HRDandPayroll/Master/ucStatusPerkawinan_list.ascx" TagPrefix="uc1" TagName="ucStatusPerkawinan_list" %>
<%@ Register Src="~/Views/HRDandPayroll/Master/ucStatusPerkawinan_edit.ascx" TagPrefix="uc1" TagName="ucStatusPerkawinan_edit" %>


<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
     <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_status_perkawinan" Width="100%">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Caption="List" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucStatusPerkawinan_list runat="server" id="ucStatusPerkawinan_list" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Edit" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucStatusPerkawinan_edit runat="server" id="ucStatusPerkawinan_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
