Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Imports HRD.Application
Imports HRD.Controller

Public Class CutiBesarController
    Inherits Controller(Of tr_cuti_besar)
    Implements ICutiBesarListController, ICutiBesarEditController

    Public ReadOnly Property DataList(area_id As String, posted As Boolean) As IQueryable(Of tr_cuti_besar) Implements ICutiBesarListController.DataList
        Get
            Dim serv = ObjectFactory.GetInstance(Of CutiBesarService)
            Dim r = serv.GetQueryableAsync().GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_cuti_besar) = r.Output
                os = os.Where(Function(f) f.Area_id = area_id AndAlso f.Posted = posted)
                Return os
            Else
                Return Nothing
            End If
        End Get
    End Property
    Public ReadOnly Property DataListCutiBesarLine(cuti_besar_id As String) As IQueryable(Of tr_cuti_besar_line) Implements ICutiBesarEditController.DataListCutiBesarLine
        Get
            If String.IsNullOrEmpty(cuti_besar_id) Then
                Return Nothing
            End If

            Dim cutiId As Integer
            If Not Integer.TryParse(cuti_besar_id, cutiId) Then
                Return Nothing
            End If

            ' For now return empty queryable - this will be implemented when detail functionality is added
            Dim emptyList = New List(Of tr_cuti_besar_line)()
            Return emptyList.AsQueryable()
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Dim _user = AuthHelper.GetLoggedInUserInfo
        Action = Action.AddNew
        SelectedItem = New tr_cuti_besar With {
            .Area_id = _user.Area_id,
            .Tanggal = Now,
            .Tahun = Now.Year,
            .Posted = False,
            .Status = "DRAFT"
        }
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of CutiBesarService)

        Dim r = serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim serv = ObjectFactory.GetInstance(Of CutiBesarService)

            Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult
            If r.Success Then
                sMsg = MessageFormatter.GetFormattedSuccessMessage(r.Message)
                Reset()
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_cuti_besar)
        Dim serv = ObjectFactory.GetInstance(Of CutiBesarService)
        If TEntity.Id <= 0 Then
            TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        Else
            TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.ModifiedDate = Now
        End If
        Dim r = serv.UpsertAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Public Sub Posting(TEntity As tr_cuti_besar) Implements ICutiBesarEditController.Posting
        Dim serv = ObjectFactory.GetInstance(Of CutiBesarService)

        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        TEntity.PostedDate = Now

        Dim r = serv.PostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class

Public Interface ICutiBesarListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(area_id As String, posted As Boolean) As IQueryable(Of tr_cuti_besar)
End Interface

Public Interface ICutiBesarEditController
    Inherits IControllerMain, IControllerEdit(Of tr_cuti_besar)

    ReadOnly Property DataListCutiBesarLine(cuti_besar_id As String) As IQueryable(Of tr_cuti_besar_line)
    Sub Posting(oSelectItem As tr_cuti_besar)
End Interface
