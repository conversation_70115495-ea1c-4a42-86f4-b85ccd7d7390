﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="XrPictureBox2.ImageSource" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="XrPictureBox1.ImageSource" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="XrRichText1.SerializableRtfString" xml:space="preserve">
    <value>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</value>
  </data>
</root>