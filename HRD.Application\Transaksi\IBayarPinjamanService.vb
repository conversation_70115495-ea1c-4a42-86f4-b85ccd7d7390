﻿Imports HRD.Domain

Public Interface IBayarPinjamanService
    Function GetQueryableAsync() As Task(Of ResponseModel)
    Function GetByIdAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function UpsertAsync(ByVal o As tr_pinjaman_bayar) As Task(Of ResponseModel)
    Function DeleteAsync(ByVal Id As Integer) As Task(Of ResponseModel)
    Function Posting(ByVal o As tr_pinjaman_bayar) As Task(Of ResponseModel)
End Interface
