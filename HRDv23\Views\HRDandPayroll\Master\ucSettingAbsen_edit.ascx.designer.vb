﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated. 
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On


Partial Public Class ucSettingAbsen_edit
    
    '''<summary>
    '''ASPxFormLayout1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1 As Global.DevExpress.Web.ASPxFormLayout
    
    '''<summary>
    '''cb_area control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_area As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_cabang control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_cabang As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''txt_jammasuk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jamkeluar control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jamkeluar As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jammasuk0 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk0 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jamkeluar0 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jamkeluar0 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jammasuk1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk1 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jammasuk2 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk2 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jammasuk3 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk3 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_jammasuk4 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_jammasuk4 As Global.DevExpress.Web.ASPxTimeEdit
    
    '''<summary>
    '''txt_tol_jammasuk control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tol_jammasuk As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_tol_jammasuk0 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tol_jammasuk0 As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_tol_jammasuk1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tol_jammasuk1 As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''ASPxFormLayout1_E9 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1_E9 As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''ASPxFormLayout1_E10 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1_E10 As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''ASPxFormLayout1_E11 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1_E11 As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''ASPxFormLayout1_E12 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1_E12 As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''btn_save control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_save As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_back control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_back As Global.DevExpress.Web.ASPxButton
End Class
