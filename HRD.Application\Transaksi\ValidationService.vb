﻿Imports HRD.Domain

Public Class ValidationService
    Private ReadOnly _unitOfWork As IUnitOfWork

    Public Sub New(unitOfWork As IUnitOfWork)
        _unitOfWork = unitOfWork
    End Sub

    Public Function IsPayrollPosted(cabangId As Integer, tgl As Date) As Boolean
        Dim tgl0 As Date = New DateTime(tgl.Year, tgl.Month, 21)
        If tgl >= tgl0 Then
            tgl0 = tgl0.AddMonths(1)
        End If
        Dim gajiRepo = _unitOfWork.Repository(Of tr_gaji)
        Return gajiRepo.TableNoTracking.Any(Function(g) g.Bulan = tgl0.Month AndAlso g.<PERSON>hun = tgl0.Year AndAlso g.Cabang_id = cabangId AndAlso g.Posted = True)
    End Function
End Class
