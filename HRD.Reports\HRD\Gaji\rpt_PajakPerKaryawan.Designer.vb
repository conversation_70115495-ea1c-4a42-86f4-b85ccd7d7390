﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class rpt_PajakPerKaryawan
    Inherits DevExpress.XtraReports.UI.XtraReport

    'XtraReport overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Designer
    'It can be modified using the Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.TopMargin = New DevExpress.XtraReports.UI.TopMarginBand()
        Me.BottomMargin = New DevExpress.XtraReports.UI.BottomMarginBand()
        Me.Detail = New DevExpress.XtraReports.UI.DetailBand()
        Me.XrTable6 = New DevExpress.XtraReports.UI.XRTable()
        Me.XrTableRow5 = New DevExpress.XtraReports.UI.XRTableRow()
        Me.XrTableCell10 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell11 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrTableCell16 = New DevExpress.XtraReports.UI.XRTableCell()
        Me.txt_periode = New DevExpress.XtraReports.UI.XRTableCell()
        Me.XrSubreport1 = New DevExpress.XtraReports.UI.XRSubreport()
        Me.ReportHeader = New DevExpress.XtraReports.UI.ReportHeaderBand()
        Me.XrLabel1 = New DevExpress.XtraReports.UI.XRLabel()
        Me.ObjectDataSource1 = New DevExpress.DataAccess.ObjectBinding.ObjectDataSource(Me.components)
        Me.fHariKerja = New DevExpress.XtraReports.UI.CalculatedField()
        Me.fIjin = New DevExpress.XtraReports.UI.CalculatedField()
        Me.fIMP = New DevExpress.XtraReports.UI.CalculatedField()
        Me.fLembur = New DevExpress.XtraReports.UI.CalculatedField()
        Me.fMasuk = New DevExpress.XtraReports.UI.CalculatedField()
        Me.fLembur_9_10 = New DevExpress.XtraReports.UI.CalculatedField()
        Me.pTahun = New DevExpress.XtraReports.Parameters.Parameter()
        CType(Me.XrTable6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ObjectDataSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'TopMargin
        '
        Me.TopMargin.HeightF = 50.0!
        Me.TopMargin.Name = "TopMargin"
        '
        'BottomMargin
        '
        Me.BottomMargin.HeightF = 50.0!
        Me.BottomMargin.Name = "BottomMargin"
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New DevExpress.XtraReports.UI.XRControl() {Me.XrTable6, Me.XrSubreport1})
        Me.Detail.HeightF = 55.83333!
        Me.Detail.Name = "Detail"
        Me.Detail.PageBreak = DevExpress.XtraReports.UI.PageBreak.BeforeBandExceptFirstEntry
        Me.Detail.SortFields.AddRange(New DevExpress.XtraReports.UI.GroupField() {New DevExpress.XtraReports.UI.GroupField("Tanggal", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)})
        '
        'XrTable6
        '
        Me.XrTable6.Borders = CType((((DevExpress.XtraPrinting.BorderSide.Left Or DevExpress.XtraPrinting.BorderSide.Top) _
            Or DevExpress.XtraPrinting.BorderSide.Right) _
            Or DevExpress.XtraPrinting.BorderSide.Bottom), DevExpress.XtraPrinting.BorderSide)
        Me.XrTable6.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrTable6.LocationFloat = New DevExpress.Utils.PointFloat(0!, 0!)
        Me.XrTable6.Name = "XrTable6"
        Me.XrTable6.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96.0!)
        Me.XrTable6.Rows.AddRange(New DevExpress.XtraReports.UI.XRTableRow() {Me.XrTableRow5})
        Me.XrTable6.SizeF = New System.Drawing.SizeF(750.0!, 25.0!)
        Me.XrTable6.StylePriority.UseBorders = False
        Me.XrTable6.StylePriority.UseFont = False
        '
        'XrTableRow5
        '
        Me.XrTableRow5.Cells.AddRange(New DevExpress.XtraReports.UI.XRTableCell() {Me.XrTableCell10, Me.XrTableCell11, Me.XrTableCell16, Me.txt_periode})
        Me.XrTableRow5.Name = "XrTableRow5"
        Me.XrTableRow5.Weight = 1.0R
        '
        'XrTableCell10
        '
        Me.XrTableCell10.Multiline = True
        Me.XrTableCell10.Name = "XrTableCell10"
        Me.XrTableCell10.Text = "Nama Karyawan"
        Me.XrTableCell10.Weight = 0.51R
        '
        'XrTableCell11
        '
        Me.XrTableCell11.ExpressionBindings.AddRange(New DevExpress.XtraReports.UI.ExpressionBinding() {New DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Nama]+' - '+[NIK]")})
        Me.XrTableCell11.Multiline = True
        Me.XrTableCell11.Name = "XrTableCell11"
        Me.XrTableCell11.Text = "XrTableCell2"
        Me.XrTableCell11.Weight = 1.5950000000000002R
        '
        'XrTableCell16
        '
        Me.XrTableCell16.Multiline = True
        Me.XrTableCell16.Name = "XrTableCell16"
        Me.XrTableCell16.Text = "Periode"
        Me.XrTableCell16.Weight = 0.37791674804687492R
        '
        'txt_periode
        '
        Me.txt_periode.Multiline = True
        Me.txt_periode.Name = "txt_periode"
        Me.txt_periode.Text = "txt_periode"
        Me.txt_periode.Weight = 0.5170832519531251R
        '
        'XrSubreport1
        '
        Me.XrSubreport1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 25.0!)
        Me.XrSubreport1.Name = "XrSubreport1"
        Me.XrSubreport1.SizeF = New System.Drawing.SizeF(750.0!, 23.0!)
        '
        'ReportHeader
        '
        Me.ReportHeader.Controls.AddRange(New DevExpress.XtraReports.UI.XRControl() {Me.XrLabel1})
        Me.ReportHeader.HeightF = 43.33333!
        Me.ReportHeader.Name = "ReportHeader"
        '
        'XrLabel1
        '
        Me.XrLabel1.Font = New DevExpress.Drawing.DXFont("Segoe UI", 16.0!, DevExpress.Drawing.DXFontStyle.Bold)
        Me.XrLabel1.LocationFloat = New DevExpress.Utils.PointFloat(0!, 0!)
        Me.XrLabel1.Multiline = True
        Me.XrLabel1.Name = "XrLabel1"
        Me.XrLabel1.Padding = New DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100.0!)
        Me.XrLabel1.SizeF = New System.Drawing.SizeF(750.0!, 32.16667!)
        Me.XrLabel1.StylePriority.UseFont = False
        Me.XrLabel1.Text = "LAPORAN PAJAK PER KARYAWAN"
        '
        'ObjectDataSource1
        '
        Me.ObjectDataSource1.DataSource = GetType(HRD.Domain.tm_karyawan)
        Me.ObjectDataSource1.Name = "ObjectDataSource1"
        '
        'fHariKerja
        '
        Me.fHariKerja.FieldType = DevExpress.XtraReports.UI.FieldType.Int32
        Me.fHariKerja.Name = "fHariKerja"
        '
        'fIjin
        '
        Me.fIjin.FieldType = DevExpress.XtraReports.UI.FieldType.Int32
        Me.fIjin.Name = "fIjin"
        '
        'fIMP
        '
        Me.fIMP.FieldType = DevExpress.XtraReports.UI.FieldType.Int32
        Me.fIMP.Name = "fIMP"
        '
        'fLembur
        '
        Me.fLembur.FieldType = DevExpress.XtraReports.UI.FieldType.Int32
        Me.fLembur.Name = "fLembur"
        '
        'fMasuk
        '
        Me.fMasuk.FieldType = DevExpress.XtraReports.UI.FieldType.Int32
        Me.fMasuk.Name = "fMasuk"
        '
        'fLembur_9_10
        '
        Me.fLembur_9_10.FieldType = DevExpress.XtraReports.UI.FieldType.[Decimal]
        Me.fLembur_9_10.Name = "fLembur_9_10"
        '
        'pTahun
        '
        Me.pTahun.Description = "Periode Tahun"
        Me.pTahun.Name = "pTahun"
        Me.pTahun.Visible = False
        '
        'rpt_PajakPerKaryawan
        '
        Me.Bands.AddRange(New DevExpress.XtraReports.UI.Band() {Me.TopMargin, Me.BottomMargin, Me.Detail, Me.ReportHeader})
        Me.CalculatedFields.AddRange(New DevExpress.XtraReports.UI.CalculatedField() {Me.fHariKerja, Me.fIjin, Me.fIMP, Me.fLembur, Me.fMasuk, Me.fLembur_9_10})
        Me.ComponentStorage.AddRange(New System.ComponentModel.IComponent() {Me.ObjectDataSource1})
        Me.DataSource = Me.ObjectDataSource1
        Me.DrawGrid = False
        Me.Font = New DevExpress.Drawing.DXFont("Segoe UI", 10.0!)
        Me.Margins = New DevExpress.Drawing.DXMargins(50.0!, 50.0!, 50.0!, 50.0!)
        Me.ParameterPanelLayoutItems.AddRange(New DevExpress.XtraReports.Parameters.ParameterPanelLayoutItem() {New DevExpress.XtraReports.Parameters.ParameterLayoutItem(Me.pTahun, DevExpress.XtraReports.Parameters.Orientation.Horizontal)})
        Me.Parameters.AddRange(New DevExpress.XtraReports.Parameters.Parameter() {Me.pTahun})
        Me.SnapGridSize = 3.0!
        Me.Version = "23.1"
        CType(Me.XrTable6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ObjectDataSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Friend WithEvents TopMargin As DevExpress.XtraReports.UI.TopMarginBand
    Friend WithEvents BottomMargin As DevExpress.XtraReports.UI.BottomMarginBand
    Friend WithEvents Detail As DevExpress.XtraReports.UI.DetailBand
    Friend WithEvents ReportHeader As DevExpress.XtraReports.UI.ReportHeaderBand
    Friend WithEvents XrLabel1 As DevExpress.XtraReports.UI.XRLabel
    Friend WithEvents ObjectDataSource1 As DevExpress.DataAccess.ObjectBinding.ObjectDataSource
    Friend WithEvents fHariKerja As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents fIjin As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents fIMP As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents fLembur As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents fMasuk As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents fLembur_9_10 As DevExpress.XtraReports.UI.CalculatedField
    Friend WithEvents pTahun As DevExpress.XtraReports.Parameters.Parameter
    Friend WithEvents XrSubreport1 As DevExpress.XtraReports.UI.XRSubreport
    Friend WithEvents XrTable6 As DevExpress.XtraReports.UI.XRTable
    Friend WithEvents XrTableRow5 As DevExpress.XtraReports.UI.XRTableRow
    Friend WithEvents XrTableCell10 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell11 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents XrTableCell16 As DevExpress.XtraReports.UI.XRTableCell
    Friend WithEvents txt_periode As DevExpress.XtraReports.UI.XRTableCell
End Class
