Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers

Public Class ucCutiBesar_list
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As ICutiBesarListController

    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        grd_cuti_besar.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then Return
        e.DefaultSorting = "Id Desc"
        
        ' Default to showing non-posted items
        Dim showPosted As Boolean = False
        Try
            ' Check if there's a checkbox or other control to toggle posted status
            showPosted = CBool(ViewState("ShowPosted"))
        Catch ex As Exception
            showPosted = False
        End Try
        
        e.QueryableSource = Controller.DataList(cb_area.Value, showPosted).AsQueryable
    End Sub

    Private Sub grd_cuti_besar_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles grd_cuti_besar.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then Return
        If e.VisibleIndex = -1 Then Return
        
        Try
            Dim isPosted As Boolean = CBool(CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Posted"))
            Select Case e.ButtonID
                Case "btn_edit", "btn_delete"
                    e.Visible = If(Not isPosted, DevExpress.Utils.DefaultBoolean.True, DevExpress.Utils.DefaultBoolean.False)
                Case "btn_view"
                    e.Visible = DevExpress.Utils.DefaultBoolean.True
            End Select
        Catch ex As Exception
            ' If Posted column doesn't exist or there's an error, show all buttons
            e.Visible = DevExpress.Utils.DefaultBoolean.True
        End Try
    End Sub

    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub
End Class