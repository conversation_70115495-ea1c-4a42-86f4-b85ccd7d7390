﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class RegisterPinjamanController
    Inherits Controller(Of tr_register_pinjaman)
    Implements IRegisterPinjamanListController, IRegisterPinjamanEditController

    Public ReadOnly Property DataList(bPosted As Boolean, sStatus As String) As IQueryable(Of tr_register_pinjaman) Implements IRegisterPinjamanListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_register_pinjaman) = r.Output
                Dim _user = AuthHelper.GetLoggedInUserInfo
                Dim PermisionSpec = New PinjamanPermisionSpec(bPosted, sStatus)

                os = PermisionSpec.SatisfyingEntitiesInQuery(os)

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property DataListHistory(pinj_id As String) As IQueryable(Of tr_pinjaman_move) Implements IRegisterPinjamanEditController.DataListHistory
        Get
            Dim Serv = ObjectFactory.GetInstance(Of PinjamanMoveService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_pinjaman_move) = r.Output

                os = os.Where(Function(f) f.Pinjaman_id = pinj_id)

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_register_pinjaman With {.Area_id = _user.Area_id, .Cabang_id = _user.Cabang_id, .Tanggal = Now, .Status = "New"}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()

        Throw New NotImplementedException()

    End Sub

    Public Overrides Sub Saving(TEntity As tr_register_pinjaman)

        TEntity.Saldo = TEntity.Amount

        ExpressMapper.Mapper.Register(Of tr_register_pinjaman, tr_register_pinjaman)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_karyawan)

        Dim o = ExpressMapper.Mapper.Map(Of tr_register_pinjaman, tr_register_pinjaman)(TEntity)

        'If Action = Action.Posting Then
        '    If Posting(o) Then
        '        Saved = True
        '        Return
        '    End If
        'End If

        Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Public Function Posting(TEntity As tr_register_pinjaman) As Boolean Implements IRegisterPinjamanEditController.Posting
        Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()
        Dim rCek = Serv.GetByIdAsync(TEntity.Id).GetAwaiter.GetResult
        If rCek.Success Then
            Dim oCek As tr_register_pinjaman = rCek.Output
            If oCek.Posted Then
                sMsg = MessageFormatter.GetFormattedErrorMessage("Data ini sudah diposting")
                Saved = False
                Return False
            End If
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(rCek.Message)
            Saved = False
            Return False
        End If

        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        TEntity.PostedDate = Now

        Dim r = Serv.Posting(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Function
End Class
Public Interface IRegisterPinjamanListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bPosted As Boolean, sStatus As String) As IQueryable(Of tr_register_pinjaman)
End Interface
Public Interface IRegisterPinjamanEditController
    Inherits IControllerMain, IControllerEdit(Of tr_register_pinjaman)

    ReadOnly Property DataListHistory(pinj_id As String) As IQueryable(Of tr_pinjaman_move)

    Function Posting(TEntity As tr_register_pinjaman) As Boolean
End Interface