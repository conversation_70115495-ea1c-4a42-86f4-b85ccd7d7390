﻿Imports HRD.Domain

Public Class MapperConfig
    Public Shared Sub RegisterMappings()
        ' Konfigurasi mapper untuk tr_tunjangan_onetime
        ExpressMapper.Mapper.Register(Of tr_tunjangan_onetime, tr_tunjangan_onetime)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_karyawan)

        ' Konfigurasi mapper untuk tm_karyawan
        ExpressMapper.Mapper.Register(Of tm_karyawan, tm_karyawan)() _
            .Ignore(Function(x) x.tm_agama) _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_jenis_kelamin) _
            .Ignore(Function(x) x.tm_jurusan) _
            .Ignore(Function(x) x.tm_namabank) _
            .Ignore(Function(x) x.tm_pendidikan) _
            .Ignore(Function(x) x.tm_status_PTKP) _
            .Ignore(Function(x) x.tm_status_perkawinan) _
            .Ignore(Function(x) x.tm_karyawan_datadinas) _
            .Ignore(Function(x) x.tr_absensi) _
            .Ignore(Function(x) x.tr_gaji_line) _
            .Ignore(Function(x) x.tr_ijin) _
            .Ignore(Function(x) x.tr_register_pinjaman) _
            .Ignore(Function(x) x.tr_spk_lembur_line) _
            .Ignore(Function(x) x.tm_off_karyawan_line) _
            .Ignore(Function(x) x.tr_tunjangan_onetime)

        ' Konfigurasi mapper untuk tr_ijin
        ExpressMapper.Mapper.Register(Of tr_ijin, tr_ijin)() _
            .Ignore(Function(x) x.tm_area) _
            .Ignore(Function(x) x.tm_cabang) _
            .Ignore(Function(x) x.tm_ijin_master) _
            .Ignore(Function(x) x.tm_karyawan) _
            .Ignore(Function(x) x.tr_absensi)

        ' Konfigurasi mapper untuk tr_spk_lembur
        ExpressMapper.Mapper.Register(Of tr_spk_lembur, tr_spk_lembur)() _
            .Ignore(Function(i) i.tm_area) _
            .Ignore(Function(i) i.tm_cabang) _
            .Ignore(Function(i) i.tr_spk_lembur_line)
    End Sub
End Class
