﻿Imports System.Linq.Expressions
Imports HRD.Dao
Imports HRD.Domain

Public MustInherit Class DaoBase(Of T As Class)
    Implements IDao(Of T)

    Public ReadOnly Property Context() As HRDEntities
        Get
            Return ContextFactory.Instance.GetContextPerRequest()
        End Get
    End Property

    Public ReadOnly Property GetContext As HRDEntities Implements IDao(Of T).GetContext
        Get
            Return Context
        End Get
    End Property

    Public Sub Add(entity As T) Implements IDao(Of T).Add
        Context.Entry(entity).State = Data.Entity.EntityState.Added
    End Sub

    Public Sub Delete(entity As T) Implements IDao(Of T).Delete
        Context.Entry(entity).State = Data.Entity.EntityState.Deleted
    End Sub

    Public Sub Edit(entity As T) Implements IDao(Of T).Edit
        Context.Entry(entity).State = Data.Entity.EntityState.Modified
    End Sub

    Public Sub Save() Implements IDao(Of T).Save
        Try
            Context.SaveChanges()
        Catch ex As Exception
            Dim s As String = Nothing
            For Each x In Context.GetValidationErrors
                For Each y In x.ValidationErrors
                    s &= String.Format("{0}{1}", y.ErrorMessage, "</BR>")
                Next
            Next
            If s <> Nothing Then
                Throw New ApplicationException(s)
            Else
                Throw New ApplicationException("Save Failed", ex.InnerException)
            End If

        End Try

    End Sub

    Public Sub AddRange(entities As List(Of T)) Implements IDao(Of T).AddRange
        If entities.Count > 0 Then
            Context.Set(Of T).AddRange(entities)
        End If

    End Sub

    Public Sub DeleteRange(entities As List(Of T)) Implements IDao(Of T).DeleteRange
        If entities.Count > 0 Then
            Context.Set(Of T).RemoveRange(entities)
        End If

    End Sub

    Public Sub ResetContext() Implements IDao(Of T).ResetContext
        ObjectContextManager.ResetContext()
    End Sub

    Public Function GetAll() As IQueryable(Of T) Implements IDao(Of T).GetAll
        Dim query As IQueryable(Of T) = Context.Set(Of T)()
        Return query
    End Function

    Public Function FindBy(predicate As Expression(Of Func(Of T, Boolean))) As IQueryable(Of T) Implements IDao(Of T).FindBy
        Dim query As IQueryable(Of T) = Context.Set(Of T)().Where(predicate)
        Return query
    End Function


End Class
