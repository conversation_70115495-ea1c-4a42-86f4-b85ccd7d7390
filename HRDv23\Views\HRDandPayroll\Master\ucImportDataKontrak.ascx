﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucImportDataKontrak.ascx.vb" Inherits="HRDv23.ucImportDataKontrak" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:LayoutGroup Caption="Upload" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxUploadControl ID="ASPxUploadControl1" runat="server" ShowProgressPanel="True" ShowUploadButton="True" UploadMode="Auto" Width="280px">
    <ValidationSettings AllowedFileExtensions=".xlsx, .XLSX">
    </ValidationSettings>
    <ClientSideEvents FileUploadComplete="function(s, e) {
	grd_datakontrak.PerformCallback('import');
}" />
    <AdvancedModeSettings EnableDragAndDrop="True">
    </AdvancedModeSettings>
</dx:ASPxUploadControl>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="List Item For Import" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" ClientInstanceName="grd_datakontrak" AutoGenerateColumns="False" KeyFieldName="Id">
                                <ClientSideEvents EndCallback="function(s, e) {
	if(s.cpMsg){
		alert(s.cpMsg);
}
}" />
                                <SettingsPopup>
                                    <FilterControl AutoUpdatePosition="False">
                                    </FilterControl>
                                </SettingsPopup>
                                <SettingsSearchPanel Visible="True" />
                                <Columns>
                                    <dx:GridViewDataDateColumn FieldName="Tanggal" ShowInCustomizationForm="True" VisibleIndex="6">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_karyawan.Nama" ShowInCustomizationForm="True" VisibleIndex="3" Caption="Karyawan">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_department.NamaDepartemen" ShowInCustomizationForm="True" VisibleIndex="4" Caption="Department">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_jabatan.Jabatan" ShowInCustomizationForm="True" VisibleIndex="5" Caption="Jabatan">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglAwalKontrak" ShowInCustomizationForm="True" VisibleIndex="10">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataDateColumn FieldName="TglAkhirKontrak" ShowInCustomizationForm="True" VisibleIndex="11">
                                        <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                                        </PropertiesDateEdit>
                                    </dx:GridViewDataDateColumn>
                                    <dx:GridViewDataTextColumn FieldName="tm_area.NamaArea" ShowInCustomizationForm="True" VisibleIndex="0" Caption="Perusahaan">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Cabang" ShowInCustomizationForm="True" VisibleIndex="1" FieldName="tm_cabang.NamaCabang">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="NIK" FieldName="tm_karyawan.NIK" ShowInCustomizationForm="True" VisibleIndex="2">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="NoKontrakKerja" ShowInCustomizationForm="True" VisibleIndex="7">
                                    </dx:GridViewDataTextColumn>
                                </Columns>
                            </dx:ASPxGridView>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
        <dx:LayoutGroup Caption="Action" ColSpan="1">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Save">
                                <ClientSideEvents Click="function(s, e) {
	cp_importdatakontrak.PerformCallback('save');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>