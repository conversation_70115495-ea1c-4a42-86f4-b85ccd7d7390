﻿Imports HRD.Application
Imports HRD.Domain
Public Class RegisterPinjamanService
    Implements IRegisterPinjamanService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Register Pinjaman Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements IRegisterPinjamanService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_register_pinjaman)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IRegisterPinjamanService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_register_pinjaman)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_register_pinjaman) As Task(Of ResponseModel) Implements IRegisterPinjamanService.UpsertAsync
        Try

            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_register_pinjaman)().Update(o)
            Else
                o.KodePinjaman = CreateKodePinjaman(o)
                Await _unitOfWork.Repository(Of tr_register_pinjaman)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IRegisterPinjamanService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_register_pinjaman)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_register_pinjaman).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function Posting(TEntity As tr_register_pinjaman) As Task(Of ResponseModel) Implements IRegisterPinjamanService.Posting
        Try

            Dim o = Await _unitOfWork.Repository(Of tr_register_pinjaman)().Get(TEntity.Id)

            o.Posted = True
            o.PostedBy = TEntity.PostedBy
            o.PostedDate = Now

            _unitOfWork.Repository(Of tr_register_pinjaman)().Update(o)

            AddToHistoryPinjaman(o)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Private Function CreateKodePinjaman(o As tr_register_pinjaman) As String
        Dim area = _myFunction.tm_area(o.Area_id)
        Dim sFormat As String = $"REG-{area.KodeArea}-{Format(o.Tanggal, "yy")}-"
        Dim pinj = _unitOfWork.Repository(Of tr_register_pinjaman).TableNoTracking.Where(Function(f) f.Area_id = o.Area_id And f.KodePinjaman.StartsWith(sFormat)).OrderByDescending(Function(od) od.KodePinjaman).Take(1).FirstOrDefault
        Dim i As Integer = 0
        If pinj IsNot Nothing Then
            i = pinj.KodePinjaman.Replace(sFormat, "")
        End If
        i += 1

        Return sFormat & Format(i, "00000")
    End Function
    Private Function AddToHistoryPinjaman(src As tr_register_pinjaman) As Boolean
        Try
            Dim o As New tr_pinjaman_move
            With o
                .Amount = src.Amount
                .CreatedBy = src.PostedBy
                .CreatedDate = Now
                .Memo = "Register Pinjaman"
                .Pinjaman_id = src.Id
                .TipeTrans = "REG"
                .TransDate = src.Tanggal
            End With
            _unitOfWork.Repository(Of tr_pinjaman_move).AddAsync(o)

            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function
End Class
