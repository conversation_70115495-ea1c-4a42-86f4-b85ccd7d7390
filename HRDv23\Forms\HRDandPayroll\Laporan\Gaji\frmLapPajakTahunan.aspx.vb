﻿Imports DevExpress.Web
Public Class frmLapPajakTahunan
    Inherits PageBase

    Public Sub New()
        MyBase.New("7375")
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub
    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")
        Select Case s(0)
            Case "show_rpt"
                Me.ucLapPajakTahunan.ShowRPT()
        End Select
    End Sub
End Class