'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Collections.Generic

Partial Public Class tr_gaji_line
    Public Property Id As Long
    Public Property Gaji_id As Integer
    Public Property Karyawan_id As Integer
    Public Property Pd_GajiPokok As Decimal
    Public Property Pd_T_Jabatan As Decimal
    Public Property Pd_T_Transport As Decimal
    Public Property Pd_T_Makan As Decimal
    Public Property Pd_T_PremiHadir As Decimal
    Public Property Pd_T_Susu As Decimal
    Public Property Pd_T_Kontrak As Decimal
    Public Property Pd_T_Probation As Decimal
    Public Property Pd_T_PremiAss As Decimal
    Public Property Pd_T_Pajak As Decimal
    Public Property Pd_T_JKK As Decimal
    Public Property Pd_T_JKM As Decimal
    Public Property Pd_T_JHT As Decimal
    Public Property Pd_T_JPK As Decimal
    Public Property Pd_T_JP As Decimal
    Public Property TotalPendapatan As Decimal
    Public Property Pt_P_JKK As Decimal
    Public Property Pt_P_JKM As Decimal
    Public Property Pt_P_JHT As Decimal
    Public Property Pt_P_JPK As Decimal
    Public Property Pt_P_JP As Decimal
    Public Property Pt_K_JHT As Decimal
    Public Property Pt_K_JPK As Decimal
    Public Property Pt_K_JP As Decimal
    Public Property Pt_K_JPK_Mandiri As Decimal
    Public Property Pt_PPH21 As Decimal
    Public Property Pt_SP As Decimal
    Public Property Pt_SerPekerja As Decimal
    Public Property Pt_Kasbon As Decimal
    Public Property TotalPotongan As Decimal
    Public Property THP As Decimal
    Public Property Pot_Alpha As Decimal
    Public Property HariKerja As Integer
    Public Property JumlahAlpha As Integer
    Public Property JumlahIjin As Integer
    Public Property JumlahIjinDispensasi As Integer
    Public Property JumlahCuti As Integer
    Public Property Pd_Lembur As Decimal
    Public Property JumlahSakit As Integer
    Public Property JumlahLembur As Integer
    Public Property JumlahPotongGaji As Integer
    Public Property Pd_Insentif As Decimal
    Public Property Pd_T_Lain As Decimal
    Public Property DataDinas_id As Nullable(Of Integer)
    Public Property StatusPTKP_id As Nullable(Of Integer)
    Public Property Ter_pajak_id As Nullable(Of Integer)
    Public Property Ter_pajak_percent As Decimal

    Public Overridable Property tm_status_PTKP As tm_status_PTKP
    Public Overridable Property tm_ter_pajak As tm_ter_pajak
    Public Overridable Property tr_gaji As tr_gaji
    Public Overridable Property tr_pinjaman_move As ICollection(Of tr_pinjaman_move) = New HashSet(Of tr_pinjaman_move)
    Public Overridable Property tm_karyawan As tm_karyawan
    Public Overridable Property tm_karyawan_datadinas As tm_karyawan_datadinas

End Class
