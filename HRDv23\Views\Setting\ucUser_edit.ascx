﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucUser_edit.ascx.vb" Inherits="HRDv23.ucUser_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Perusahaan" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" ClientInstanceName="cb_cabang">
                                        <ClientSideEvents Init="onInitCB" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Username">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E2" runat="server">
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Password">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txt_password" runat="server">
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Email">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E4" runat="server">
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="isApproved">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxCheckBox ID="ASPxFormLayout1_E5" runat="server" CheckState="Unchecked">
                                    </dx:ASPxCheckBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="isLockedOut">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxCheckBox ID="ASPxFormLayout1_E6" runat="server" CheckState="Unchecked">
                                    </dx:ASPxCheckBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="Perusahaan" ColSpan="2" ColumnSpan="2" RowSpan="5">
                            <Items>
                                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxCheckBoxList ID="chkList_perusahaan" runat="server" RepeatDirection="Horizontal" TextField="NamaArea" ValueField="Id" ValueType="System.Int32" DataSourceID="ObjectDataSource1">
                                            </dx:ASPxCheckBoxList>
                                            <asp:ObjectDataSource ID="ObjectDataSource1" runat="server" SelectMethod="GetAreaList" TypeName="HRD.Controller.UserController"></asp:ObjectDataSource>
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                        <dx:LayoutItem ColSpan="1" FieldName="PermisionArea">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxRadioButtonList ID="ASPxRadioButtonList1" runat="server" RepeatDirection="Horizontal" ValueType="System.Int32">
                                        <Items>
                                            <dx:ListEditItem Text="1 Cabang" Value="0" />
                                            <dx:ListEditItem Text="Semua Cabang" Value="1" />
                                        </Items>
                                    </dx:ASPxRadioButtonList>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="List Of Role" ColSpan="2" ColumnSpan="2">
                            <Items>
                                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id;RowGuid">
                                                <SettingsAdaptivity AdaptivityMode="HideDataCells">
                                                </SettingsAdaptivity>
                                                <SettingsPager AlwaysShowPager="True">
                                                    <AllButton Visible="True">
                                                    </AllButton>
                                                    <PageSizeItemSettings Visible="True">
                                                    </PageSizeItemSettings>
                                                </SettingsPager>
                                                <SettingsEditing EditFormColumnCount="1" Mode="PopupEditForm">
                                                </SettingsEditing>
                                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                                <SettingsBehavior AllowFocusedRow="True" />
                                                <SettingsPopup>
                                                    <EditForm AllowResize="True" HorizontalAlign="WindowCenter" Modal="True" VerticalAlign="WindowCenter">
                                                        <SettingsAdaptivity Mode="OnWindowInnerWidth" VerticalAlign="WindowCenter" />
                                                    </EditForm>
                                                    <FilterControl AutoUpdatePosition="False">
                                                    </FilterControl>
                                                </SettingsPopup>
                                                <SettingsSearchPanel Visible="True" />
                                                <SettingsExport EnableClientSideExportAPI="True">
                                                </SettingsExport>
                                                <Columns>
                                                    <dx:GridViewCommandColumn ShowDeleteButton="True" ShowEditButton="True" ShowInCustomizationForm="True" ShowNewButtonInHeader="True" VisibleIndex="0">
                                                    </dx:GridViewCommandColumn>
                                                    <dx:GridViewDataComboBoxColumn Caption="Role" FieldName="Role_id" ShowInCustomizationForm="True" VisibleIndex="1">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextField="RoleName" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_role_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_role_ItemsRequestedByFilterCondition">
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                </Columns>
                                            </dx:ASPxGridView>
                                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" TableName="tm_user_role" />
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E7" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E8" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E9" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E10" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>
        <dx:LayoutGroup Caption="Action" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Save" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_cabang.PerformCallback('save');
		s.SetEnabled(false);
	};

}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Back" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_cabang.PerformCallback('back');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

