﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class IjinController
    Inherits Controller(Of tr_ijin)
    Implements IIjinListController, IIjinEditController

    Public ReadOnly Property DataList(bPosted As Boolean) As IQueryable(Of tr_ijin) Implements IIjinListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of IjinService)()
            Dim r = Serv.GetIjinsAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_ijin) = r.Output
                Dim spec = New IjinPermisionSpec(bPosted)
                os = spec.SatisfyingEntitiesInQuery(os)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_ijin With {.StartDate = Now, .EndDate = Now, .Area_id = _user.Area_id, .Cabang_id = _user.Cabang_id}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of IjinService)()
        Dim r = Serv.GetIjinByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of IjinService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_ijin)

        If TEntity.StartDate > TEntity.EndDate Then
            sMsg = MessageFormatter.GetFormattedErrorMessage("Start Date Harus Lebih Kecil atau sama dengan End Date")
            Saved = False
            Return
        End If

        ExpressMapper.Mapper.Register(Of tr_ijin, tr_ijin)() _
        .Ignore(Function(i) i.tm_area) _
        .Ignore(Function(i) i.tm_cabang) _
        .Ignore(Function(i) i.tm_karyawan) _
        .Ignore(Function(i) i.tm_ijin_master) _
        .Ignore(Function(i) i.tr_absensi)


        Dim o = ExpressMapper.Mapper.Map(Of tr_ijin, tr_ijin)(TEntity)

        If Action = Action.Posting Then
            If Posting(o) Then
                Saved = True
                Return
            End If
        End If

        If Action = Action.UnPosting Then
            If UnPosting(o) Then
                Saved = True
                Return
            End If
        End If

        Dim Serv = ObjectFactory.GetInstance(Of IjinService)()

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub

    Private Function Posting(o As tr_ijin) As Boolean
        Dim Serv = ObjectFactory.GetInstance(Of IjinService)()

        o.Posted = True
        o.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        o.PostedDate = Now


        Dim r = Serv.PostingAsync(o).GetAwaiter.GetResult()
        If r.Success Then
            Return True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return False
        End If
    End Function
    Private Function UnPosting(o As tr_ijin) As Boolean
        Dim Serv = ObjectFactory.GetInstance(Of IjinService)()

        o.Posted = False
        o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
        o.ModifiedDate = Now


        Dim r = Serv.UnPostingAsync(o).GetAwaiter.GetResult()
        If r.Success Then
            Return True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Return False
        End If
    End Function
End Class
Public Interface IIjinListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(bPosted As Boolean) As IQueryable(Of tr_ijin)
End Interface
Public Interface IIjinEditController
    Inherits IControllerMain, IControllerEdit(Of tr_ijin)


End Interface