﻿<?xml version="1.0" encoding="utf-8"?>
<Dashboard>
  <Title Text="Human Reources" />
  <DataSources>
    <EFDataSource Name="EF Core Data Source" Source="HRD.Domain.HRDEntities, HRD.Domain, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" ComponentName="eFDataSource1" />
  </DataSources>
  <Items>
    <Grid ComponentName="gridDashboardItem1" Name="Karyawan Akan Habis Kontrak" DataSource="eFDataSource1" DataMember="vw_KaryawanAkanHabisKontrak" FilterString="Not([DataItem2] Is Null) And [DataItem2]&lt;=  AddMonths(Today(), 1)">
      <DataItems>
        <Dimension DataMember="NIK" DefaultId="DataItem0" />
        <Dimension DataMember="Nama" DefaultId="DataItem1" />
        <Dimension DataMember="TglAkhirKontrak" DateTimeGroupInterval="DayMonthYear" DefaultId="DataItem2">
          <DateTimeFormat DateFormat="Short" />
        </Dimension>
        <Dimension DataMember="NamaArea" DefaultId="DataItem3" />
        <Dimension DataMember="NamaCabang" DefaultId="DataItem4" />
      </DataItems>
      <GridColumns>
        <GridDimensionColumn Name="Perusahaan">
          <Dimension DefaultId="DataItem3" />
        </GridDimensionColumn>
        <GridDimensionColumn>
          <Dimension DefaultId="DataItem4" />
        </GridDimensionColumn>
        <GridDimensionColumn>
          <Dimension DefaultId="DataItem0" />
        </GridDimensionColumn>
        <GridDimensionColumn>
          <Dimension DefaultId="DataItem1" />
        </GridDimensionColumn>
        <GridDimensionColumn>
          <Dimension DefaultId="DataItem2" />
        </GridDimensionColumn>
      </GridColumns>
      <GridOptions />
      <ColumnFilterOptions />
    </Grid>
  </Items>
  <LayoutTree>
    <LayoutGroup>
      <LayoutItem DashboardItem="gridDashboardItem1" />
    </LayoutGroup>
  </LayoutTree>
</Dashboard>