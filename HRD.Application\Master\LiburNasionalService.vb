﻿Imports HRD.Application
Imports HRD.Domain
Public Class LiburNasionalService
    Implements ILiburNasionalService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Libur Nasional Service", method, msg)
    End Sub

    Public Async Function GetQueryableAsync() As Task(Of ResponseModel) Implements ILiburNasionalService.GetQueryableAsync
        Try
            Dim os = _unitOfWork.Repository(Of tm_libur_nasional)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetQueryableAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ILiburNasionalService.GetByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_libur_nasional)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_libur_nasional) As Task(Of ResponseModel) Implements ILiburNasionalService.UpsertAsync
        Try

            If o.Id > 0 Then
                _unitOfWork.Repository(Of tm_libur_nasional)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tm_libur_nasional)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ILiburNasionalService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_libur_nasional)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_libur_nasional).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
