﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap

Public Class SettingAbsensiController
    Inherits Controller(Of tm_absensi_setting)
    Implements ISettingAbsensiListController, ISettingAbsensiEditController

    Public ReadOnly Property DataList(isHariKhusus As Boolean) As IQueryable(Of tm_absensi_setting) Implements ISettingAbsensiListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of SettingAbsenService)()
            Dim r = Serv.GetSettingAbsensAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tm_absensi_setting) = r.Output
                Dim spec = New SettingAbsensiPermision
                os = spec.SatisfyingEntitiesInQuery(os)
                os = os.Where(Function(f) f.HariKhusus = isHariKhusus)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tm_absensi_setting With {.Area_id = _user.Area_id, .Cabang_id = _user.Cabang_id, .JamMasuk = Now, .JamKeluar = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SettingAbsenService)()
        Dim r = Serv.GetSettingAbsenByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of SettingAbsenService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tm_absensi_setting)

        ExpressMapper.Mapper.Register(Of tm_absensi_setting, tm_absensi_setting)() _
            .Ignore(Function(s) s.tm_area) _
            .Ignore(Function(s) s.tm_cabang)

        Dim o = ExpressMapper.Mapper.Map(Of tm_absensi_setting, tm_absensi_setting)(TEntity)
        Dim Serv = ObjectFactory.GetInstance(Of SettingAbsenService)()

        If o.JamMasukJumat.HasValue Then
            o.JamMasukJumat = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamMasukJumat.Value, "HH:mm:ss"))
        End If
        If o.JamKeluarJumat.HasValue Then
            o.JamKeluarJumat = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamKeluarJumat.Value, "HH:mm:ss"))
        End If
        If o.JamMasukSabtu.HasValue Then
            o.JamMasukSabtu = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamMasukSabtu.Value, "HH:mm:ss"))
        End If
        If o.JamKeluarSabtu.HasValue Then
            o.JamKeluarSabtu = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamKeluarSabtu.Value, "HH:mm:ss"))
        End If
        If o.JamMasukMinggu.HasValue Then
            o.JamMasukMinggu = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamMasukMinggu.Value, "HH:mm:ss"))
        End If
        If o.JamKeluarMinggu.HasValue Then
            o.JamKeluarMinggu = String.Format("{0} {1}", Format(Now, "yyyy-MM-dd"), Format(o.JamKeluarMinggu.Value, "HH:mm:ss"))
        End If

        If o.Id <= 0 Then
            o.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.CreatedDate = Now
        Else
            o.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            o.ModifiedDate = Now
        End If

        Dim r = Serv.UpsertAsync(o).GetAwaiter.GetResult
        If r.Success Then
            Saved = True
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface ISettingAbsensiListController
    Inherits IControllerMain, IControllerList


    ReadOnly Property DataList(isHariKhusus As Boolean) As IQueryable(Of tm_absensi_setting)
End Interface
Public Interface ISettingAbsensiEditController
    Inherits IControllerMain, IControllerEdit(Of tm_absensi_setting)

End Interface