Imports System.Threading
Imports HRD.Application
Imports MediatR
Imports HRD.Domain
Imports Z.EntityFramework.Plus

Public Class GetAreaHandler


    Private ReadOnly _uow As IUnitOfWork

    Public Sub New(uow As IUnitOfWork)
        _uow = uow
    End Sub


    Public Async Function Handle(request As GetAreaListQuery, cancellationToken As CancellationToken) As Task(Of IEnumerable(Of tm_area))
        Dim query = _uow.Repository(Of tm_area).TableNoTracking


        Return query.FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddDays(1)}).AsEnumerable
    End Function

    Public Async Function Handle(request As GetAreaQuery, cancellationToken As CancellationToken) As Task(Of tm_area)
        Dim query = _uow.Repository(Of tm_area).TableNoTracking.Where(Function(f) f.KodeArea = request.Kd_area)


        Return query.FirstOrDefault
    End Function


End Class