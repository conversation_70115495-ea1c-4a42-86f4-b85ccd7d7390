﻿Imports HRD.Application
Imports HRD.Domain
Public Class IjinService
    Implements IIjinService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction
    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog, myfunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myfunction
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Data Dinas Service", method, msg)
    End Sub

    Public Async Function GetIjinsAsync() As Task(Of ResponseModel) Implements IIjinService.GetIjinsAsync
        Try
            Dim os = _unitOfWork.Repository(Of tr_ijin)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetIjinsAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetIjinByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IIjinService.GetIjinByIdAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_ijin)().Get(Id)

            If o IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetIjinByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_ijin) As Task(Of ResponseModel) Implements IIjinService.UpsertAsync
        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_ijin)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tr_ijin)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IIjinService.DeleteAsync
        Try
            Dim o = Await _unitOfWork.Repository(Of tr_ijin)().Get(Id)
            If o IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_ijin).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function PostingAsync(o As tr_ijin) As Task(Of ResponseModel) Implements IIjinService.PostingAsync
        Try
            _unitOfWork.Repository(Of tr_ijin)().Update(o)

            UpdateAbsensi(o)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UnPostingAsync(o As tr_ijin) As Task(Of ResponseModel) Implements IIjinService.UnPostingAsync

        Try
            _unitOfWork.Repository(Of tr_ijin)().Update(o)

            Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.Ijin_id = o.Id).FirstOrDefault
            If abs IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_absensi).Delete(Function(f) f.Ijin_id = o.Id AndAlso f.tr_ijin.tm_ijin_master.KodeIjin <> "IP")
            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.PostingAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try

    End Function

    Private Function UpdateAbsensi(o As tr_ijin) As Boolean
        Try
            Dim mIjin = _unitOfWork.Repository(Of tm_ijin_master).Get(o.Ijin_id).GetAwaiter.GetResult
            Dim mKaryawan = _unitOfWork.Repository(Of tm_karyawan).Get(o.Karyawan_id).GetAwaiter.GetResult
            Dim tgl As Date = o.StartDate
            Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, o.EndDate)
            Do While tgl < tgl2
                Dim abs = _unitOfWork.Repository(Of tr_absensi).Table.Where(Function(f) f.karyawan_id = o.Karyawan_id And f.Tanggal = tgl).FirstOrDefault
                If abs Is Nothing Then
                    abs = New tr_absensi
                End If
                Dim ketLibur As String = Nothing
                If _myFunction.CekHariLibur(o.Area_id, mKaryawan, tgl, ketLibur) AndAlso abs.GantiHari <> True Then
                    abs.Off_b = True
                    abs.Keterangan = ketLibur
                    abs.Cuti_b = False
                    abs.Ijin_b = False
                Else
                    abs.Cuti_b = If(mIjin.IsCuti = True, True, False)
                    abs.Ijin_b = If(mIjin.KodeIjin <> "C", True, False)
                    abs.Keterangan = $"Ijin : {mIjin.NamaIjin}"

                    abs.Off_b = False
                End If
                abs.PotongGaji = mIjin.PotongGaji
                abs.Alpha_b = False
                abs.Area_id = o.Area_id
                abs.Cabang_id = o.Cabang_id
                abs.CreatedBy = o.PostedBy
                abs.CreatedDate = Now


                abs.karyawan_id = o.Karyawan_id

                abs.Masuk_b = False
                abs.Overtime = 0
                abs.OverTime_HLibur = 0
                abs.PulangCepat = 0
                abs.Tanggal = tgl
                abs.Terlambat = 0
                abs.Ijin_id = o.Id

                abs.FromAbsensi = True

                If abs.Id <= 0 Then
                    _unitOfWork.Repository(Of tr_absensi).AddAsync(abs)
                Else
                    _unitOfWork.Repository(Of tr_absensi).Update(abs)
                End If

                tgl = DateAdd(DateInterval.Day, 1, tgl)
            Loop

            Return True
        Catch ex As Exception
            Throw New ApplicationException(ex.Message)
        End Try

    End Function


End Class
