﻿Imports System.Drawing
Imports System.IO
Imports System.Net.Mail
Imports System.Runtime.InteropServices.ComTypes
Imports System.Web
Imports System.Web.UI
Imports DevExpress.XtraReports.UI
Imports DevExpress.XtraRichEdit.Model
Imports HRD.Application
Imports HRD.Dao
Imports HRD.Domain
Imports StructureMap
Imports Z.EntityFramework.Plus
Public NotInheritable Class myFunction
    Private Shared ReadOnly daoFactory As IDaoFactory = New DaoFactory()

    Public Shared Function CreateThumbnail(ByVal PassedImage() As Byte, ByVal LargestSide As Integer) As Byte()
        Dim ReturnedThumbnail() As Byte

        Using StartMemoryStream As New MemoryStream(), NewMemoryStream As New MemoryStream()
            ' write the string to the stream
            StartMemoryStream.Write(PassedImage, 0, PassedImage.Length)

            ' create the start Bitmap from the MemoryStream that contains the image
            Dim startBitmap As New Bitmap(StartMemoryStream)

            ' set thumbnail height and width proportional to the original image.
            Dim newHeight As Integer
            Dim newWidth As Integer
            Dim HW_ratio As Double
            If startBitmap.Height > startBitmap.Width Then
                newHeight = LargestSide
                HW_ratio = CDbl(CDbl(LargestSide) / CDbl(startBitmap.Height))
                newWidth = CInt(Math.Truncate(HW_ratio * CDbl(startBitmap.Width)))
            Else
                newWidth = LargestSide
                HW_ratio = CDbl(CDbl(LargestSide) / CDbl(startBitmap.Width))
                newHeight = CInt(Math.Truncate(HW_ratio * CDbl(startBitmap.Height)))
            End If

            ' create a new Bitmap with dimensions for the thumbnail.
            Dim newBitmap As New Bitmap(newWidth, newHeight)

            ' Copy the image from the START Bitmap into the NEW Bitmap.
            ' This will create a thumnail size of the same image.
            newBitmap = ResizeImage(startBitmap, newWidth, newHeight)

            ' Save this image to the specified stream in the specified format.
            newBitmap.Save(NewMemoryStream, System.Drawing.Imaging.ImageFormat.Jpeg)

            ' Fill the byte[] for the thumbnail from the new MemoryStream.
            ReturnedThumbnail = NewMemoryStream.ToArray()
        End Using
        'ReturnedThumbnail = CompressByImageAlg(10, ReturnedThumbnail)
        ' return the resized image as a string of bytes.
        Return ReturnedThumbnail
    End Function
    ' Resize a Bitmap
    Private Shared Function ResizeImage(ByVal image As Bitmap, ByVal width As Integer, ByVal height As Integer) As Bitmap
        Dim resizedImage As New Bitmap(width, height)
        Using gfx As Graphics = Graphics.FromImage(resizedImage)
            gfx.DrawImage(image, New Rectangle(0, 0, width, height), New Rectangle(0, 0, image.Width, image.Height), GraphicsUnit.Pixel)
        End Using
        Return resizedImage
    End Function

    Public Shared Function FindControlRecursive(root As Control, id As String) As Control
        If root.ID = id Then
            Return root
        End If
        For Each c In root.Controls
            Dim t As Control = FindControlRecursive(c, id)
            If t IsNot Nothing Then
                Return t
            End If
        Next
        Return Nothing
    End Function

    Public Shared Function CekRoleMenu(_s As List(Of String)) As Boolean
        Return _s.Any(Function(x) HttpContext.Current.User.IsInRole(x))
    End Function

    Public Shared Function tm_area(id As String) As Domain.tm_area
        Dim mgr = daoFactory.GetAreaDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_areaByKode(kode As String) As Domain.tm_area
        Dim mgr = daoFactory.GetAreaDao

        Return mgr.FindBy(Function(f) f.KodeArea = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_cabang(id As String) As tm_cabang
        Dim mgr = daoFactory.GetCabangDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_cabangByKode(kode As String, area_id As String) As tm_cabang
        Dim mgr = daoFactory.GetCabangDao

        Return mgr.FindBy(Function(f) f.Area_id = area_id AndAlso f.KodeCabang = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_agama(id As String) As tm_agama
        Dim mgr = daoFactory.GetAgamaDao

        Return mgr.FindBy(Function(f) f.id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_agamaByNama(nama As String) As tm_agama
        Dim mgr = daoFactory.GetAgamaDao

        Return mgr.FindBy(Function(f) f.agama = nama).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_status_perkawinan(id As String) As tm_status_perkawinan
        Dim mgr = daoFactory.GetStatusPerkawinanDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_status_perkawinanByKode(kode As String) As tm_status_perkawinan
        Dim mgr = daoFactory.GetStatusPerkawinanDao

        Return mgr.FindBy(Function(f) f.KodeStatus = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_pendidikan(id As String) As tm_pendidikan
        Dim mgr = daoFactory.GetPendidikanDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_pendidikanByKode(kode As String) As tm_pendidikan
        Dim mgr = daoFactory.GetPendidikanDao

        Return mgr.FindBy(Function(f) f.KodePendidikan = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jurusan(id As String) As tm_jurusan
        Dim mgr = daoFactory.GetJurusanDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jurusanByKode(kode As String) As tm_jurusan
        Dim mgr = daoFactory.GetJurusanDao

        Return mgr.FindBy(Function(f) f.KodeJurusan = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_libur_nasional(id As String) As tm_libur_nasional
        Dim mgr = daoFactory.GetLiburNasionalDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jenis_kelamin(id As String) As tm_jenis_kelamin
        Dim mgr = daoFactory.GetJenisKelaminDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jenis_kelaminByNama(kode As String) As tm_jenis_kelamin
        Dim mgr = daoFactory.GetJenisKelaminDao

        Return mgr.FindBy(Function(f) f.JenisKelamin = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_namabank(id As String) As tm_namabank
        Dim mgr = daoFactory.GetNamaBankDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_namabankByKode(kode As String) As tm_namabank
        Dim mgr = daoFactory.GetNamaBankDao

        Return mgr.FindBy(Function(f) f.KodeBank = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_libur_nasional(tgl As Date) As tm_libur_nasional
        Dim mgr = daoFactory.GetLiburNasionalDao
        Dim tgl1 As Date = tgl.Date
        Dim tgl2 As Date = tgl.AddDays(1).Date
        Return mgr.FindBy(Function(f) f.StartDate = tgl1 AndAlso f.EndDate < tgl2).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_karyawan(id As String) As tm_karyawan
        Dim mgr = daoFactory.GetKaryawanDao
        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_karyawanByNIK(nik As String) As tm_karyawan
        Dim mgr = daoFactory.GetKaryawanDao
        Return mgr.FindBy(Function(f) f.NIK = nik).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_department(id As String) As tm_department
        Dim mgr = daoFactory.GetDepartmentDao
        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_departmentByNama(nama As String) As tm_department
        Dim mgr = daoFactory.GetDepartmentDao
        Return mgr.FindBy(Function(f) f.NamaDepartemen = nama).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jabatan(id As String) As tm_jabatan
        Dim mgr = daoFactory.GetJabatanDao
        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_jabatanByNama(nama As String) As tm_jabatan
        Dim mgr = daoFactory.GetJabatanDao
        Return mgr.FindBy(Function(f) f.Jabatan = nama).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_status_PTKP(id As String) As tm_status_PTKP
        Dim mgr = daoFactory.GetStatusPTKPDao

        Return mgr.FindBy(Function(f) f.Id = id).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_absensi_setting(cabang_id As String) As tm_absensi_setting
        Dim mgr = daoFactory.GetSettingAbsenDao

        Return mgr.FindBy(Function(f) f.Cabang_id = cabang_id And f.Shift = 0 And f.HariKhusus = False).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_absensi_setting(cabang_id As String, shift As String) As tm_absensi_setting
        Dim mgr = daoFactory.GetSettingAbsenDao

        Return mgr.FindBy(Function(f) f.Cabang_id = cabang_id And f.Shift = shift And f.HariKhusus = False).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function

    ''' <summary>
    ''' Mengambil pengaturan absensi berdasarkan tanggal. Jika tanggal berada dalam rentang pengaturan absensi khusus,
    ''' maka pengaturan absensi khusus akan digunakan. Jika tidak, pengaturan absensi reguler akan digunakan.
    ''' </summary>
    ''' <param name="cabang_id">ID cabang</param>
    ''' <param name="tanggal">Tanggal absensi</param>
    ''' <param name="shift">Shift (opsional, default 0)</param>
    ''' <returns>Pengaturan absensi yang berlaku</returns>
    Public Shared Function tm_absensi_setting_by_date(cabang_id As String, tanggal As Date, Optional shift As Integer = 0) As tm_absensi_setting
        Dim mgr = daoFactory.GetSettingAbsenDao

        ' Cari pengaturan absensi khusus yang berlaku untuk tanggal ini
        Dim settingKhusus = mgr.FindBy(Function(f) _
                                f.Cabang_id = cabang_id And _
                                f.Shift = shift And _
                                f.HariKhusus = True And _
                                f.StartDate.HasValue And f.EndDate.HasValue And _
                                f.StartDate.Value <= tanggal And f.EndDate.Value >= tanggal _
                            ).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault()

        ' Jika ada pengaturan khusus, gunakan itu
        If settingKhusus IsNot Nothing Then
            Return settingKhusus
        End If

        ' Jika tidak ada, gunakan pengaturan reguler
        Return tm_absensi_setting(cabang_id, shift.ToString())
    End Function

    Public Shared Function GetSettingAbsensi_OFF(kar_id As String, tgl As Date) As (bOff As Boolean, dMSmasuk As DateTime, dMSkeluar As DateTime, sKeterangan As String)
        Dim b_OFF As Boolean = False
        Dim msMasuk As DateTime = Nothing
        Dim msKeluar As DateTime = Nothing
        Dim sMessage As String = Nothing

        Dim mgr = daoFactory.GetOffKaryawanLineDao

        Dim setOff = mgr.FindBy(Function(f) f.Karyawan_id = kar_id And f.tm_off_karyawan.TglBerlaku <= tgl).OrderByDescending(Function(ob) ob.tm_off_karyawan.TglBerlaku).Take(1).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}, "abs_off").FirstOrDefault
        If setOff Is Nothing Then
            Dim oKar = myFunction.tm_karyawan(kar_id)
            sMessage = $"Setting Absen untuk Karyawan Store {oKar.NIK} - {oKar.Nama} Belum di setting untuk tgl berlaku {Format(tgl, "dd MMM yyyy")}"
            GoTo next1
        End If
        Select Case Weekday(tgl)
            Case 1
                If setOff.tm_shift_absensi_minggu.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_minggu.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_minggu.EndTime.ToString)
                End If

            Case 2
                If setOff.tm_shift_absensi_senin.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_senin.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_senin.EndTime.ToString)
                End If

            Case 3
                If setOff.tm_shift_absensi_selasa.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_selasa.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_selasa.EndTime.ToString)
                End If

            Case 4
                If setOff.tm_shift_absensi_rabu.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_rabu.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_rabu.EndTime.ToString)
                End If

            Case 5
                If setOff.tm_shift_absensi_kamis.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_kamis.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_kamis.EndTime.ToString)
                End If

            Case 6
                If setOff.tm_shift_absensi_jumat.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_jumat.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_jumat.EndTime.ToString)
                End If

            Case 7
                If setOff.tm_shift_absensi_sabtu.IsOff Then
                    b_OFF = True
                Else
                    msMasuk = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_sabtu.StartTime.ToString)
                    msKeluar = Format(tgl, "yyyy-MM-dd " & setOff.tm_shift_absensi_sabtu.EndTime.ToString)
                End If

        End Select

next1:

        Return (b_OFF, msMasuk, msKeluar, sMessage)
    End Function
    Public Shared Function tm_status_PTKP_byCode(kode As String) As tm_status_PTKP
        Dim mgr = daoFactory.GetStatusPTKPDao

        Return mgr.FindBy(Function(f) f.KodeStatus = kode).FromCache(New Runtime.Caching.CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(1)}).FirstOrDefault
    End Function
    Public Shared Function tm_karyawan_datadinasByIdKaryawan(kar_id As String) As tm_karyawan_datadinas
        Dim mgr = daoFactory.GetDataDinasDao

        Dim os = mgr.FindBy(Function(f) f.Karyawan_id = kar_id)
        Dim o = os.OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault
        Return o
    End Function

    Public Shared Function GetAbsensis(karyawan_id As String, startDate As Date, endDate As Date) As IQueryable(Of tr_absensi)
        Dim Tgl1 As DateTime = Format(startDate, "yyyy-MM-dd 00:00:00")
        Dim Tgl2 As DateTime = Format(endDate, "yyyy-MM-dd 23:59:59")
        Dim mgr = daoFactory.GetAbsensiDao

        'Dim pol = New CacheItemPolicy With {.SlidingExpiration = TimeSpan.FromMinutes(5)}

        Dim os = mgr.FindBy(Function(f) f.karyawan_id = karyawan_id And f.Tanggal >= Tgl1 And f.Tanggal <= Tgl2)
        Return os
    End Function
    Public Shared Function CekExistKaryawans(nik As String) As Boolean
        Dim dao = daoFactory.GetKaryawanDao
        Dim o = dao.FindBy(Function(f) f.NIK = nik).FirstOrDefault
        If o Is Nothing Then
            Return False
        End If
        Return True
    End Function
    Public Shared Function GetKodeNIK(o As tm_karyawan) As String
        Dim dao = daoFactory.GetKaryawanDao


        Dim area = tm_area(o.Area_id)
        Dim kode As String = If(o.KaryawanTetap, area.KodeNIK_Tetap, area.KodeNIK_Kontrak)
        Dim sFormat As String = Format(o.TglMasuk, "yy")
        'Dim k = dao.FindBy(Function(f) f.NIK.Substring(0, kode.Length) = kode And f.NIK.Substring(f.NIK.Length - 4) = sFormat).OrderByDescending(Function(od) od.NIK).Take(1).FirstOrDefault
        Dim k = dao.FindBy(Function(f) f.NIK.Substring(f.NIK.Length - sFormat.Length) = sFormat).OrderByDescending(Function(od) od.NIK.Substring(kode.Length)).Take(1).FirstOrDefault

        Dim i As Integer = 0
        If k IsNot Nothing Then
            Dim lastDigits = Integer.Parse(k.NIK.Substring(kode.Length, 3))
            i = lastDigits
        End If

        i += 1
        Return $"{kode}{i.ToString("000")}{Format(o.TglMasuk, "MM")}{sFormat}"
    End Function
    Public Shared Function GetPotonganPinjaman(id_kar As String, tgl As Date) As Decimal
        Dim tgl1 As DateTime = Format(tgl, "yyyy-MM-20 23:59:59")
        Dim dao = daoFactory.GetRegisterPinjamanDao

        Dim os = dao.FindBy(Function(f) f.Karyawan_id = id_kar And f.Saldo > 0 And f.Posted = True)
        os = os.Where(Function(f) f.Tanggal <= tgl1)
        Dim os1 = os.Where(Function(f) f.Saldo >= f.CicilanPerBulam)
        Dim os2 = os.Where(Function(f) f.Saldo < f.CicilanPerBulam)

        Dim jml1 = If(os1.Sum(Function(s) CType(s.CicilanPerBulam, Decimal?)), 0)
        Dim jml2 = If(os2.Sum(Function(s) CType(s.Saldo, Decimal?)), 0)

        Return jml1 + jml2
    End Function
    Public Shared Function GetSaldoAwalCuti(id_kar As String, PerTanggal As Date) As Integer
        Dim saCuti As Integer = 0
        Dim dao = daoFactory.GetKaryawanDao

        Dim startDateCuti As Date = Format(PerTanggal, "yyyy-01-01")

        Dim daoAbs = daoFactory.GetAbsensiDao
        Dim k = dao.FindBy(Function(f) f.Id = id_kar).FirstOrDefault
        Dim dn = k.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault
        If dn Is Nothing Then
            saCuti = 0
        Else
            saCuti = dn.SaldoAwalCuti
            If dn.Tetap <> True Then
                If k.TglReloadCuti.HasValue Then
                    startDateCuti = Format(k.TglReloadCuti, "yyyy-MM-dd")
                Else
                    startDateCuti = Format(dn.TglAwalKontrak, "yyyy-MM-dd")
                End If

            Else
                startDateCuti = Format(dn.TglTetap, "yyyy-MM-dd")
            End If
        End If
        'If startDateCuti.Month > PerTanggal.Month Then
        '    startDateCuti = Format(PerTanggal, String.Format("{0}-{1}-{2}", PerTanggal.Year - 1, startDateCuti.Month, startDateCuti.Day))
        'Else
        '    startDateCuti = Format(PerTanggal, String.Format("{0}-{1}-{2}", PerTanggal.Year, startDateCuti.Month, startDateCuti.Day))
        'End If
        If dn.Tetap <> True Then
            Dim t1 As Date = dn.TglAwalKontrak
            Dim t2 As Date = dn.TglAkhirKontrak

            If startDateCuti < t2 Then
                saCuti = 0
            End If
            If startDateCuti < t1 Then
                saCuti = DateDiff(DateInterval.Month, t1, t2)
            End If
            If startDateCuti >= t1 AndAlso startDateCuti <= t2 Then
                saCuti = DateDiff(DateInterval.Month, startDateCuti, t2)
            End If
        Else
            saCuti = 12
        End If

        Return saCuti

        'If saCuti <= 0 Then
        '    If dn.tm_jabatan IsNot Nothing Then
        '        saCuti = dn.tm_jabatan.JumlahCutiTahunan
        '    End If


        'End If

        'Dim tgl2 As Date = PerTanggal
        'Dim abs = daoAbs.FindBy(Function(f) f.karyawan_id = id_kar AndAlso f.Cuti_b = True And f.tr_ijin.tm_ijin_master.KodeIjin <> "CM" AndAlso f.Tanggal >= startDateCuti And f.Tanggal < tgl2).Count

        'Return saCuti - abs
    End Function
    'Public Shared Function GetSaldoCuti(id_kar As String, PerTanggal As Date) As Integer
    '    Dim saCuti As Integer = 0
    '    Dim dao = daoFactory.GetKaryawanDao
    '    Dim daoAbs = daoFactory.GetAbsensiDao


    '    ' Declare startDateCuti as Date
    '    Dim startDateCuti As Date
    '    'Dim tglCutOff As Date = New Date(2024, 9, 21)

    '    ' Retrieve employee and contract data in a single query
    '    Dim k = dao.FindBy(Function(f) f.Id = id_kar).FirstOrDefault
    '    Dim dn = k?.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).FirstOrDefault

    '    Dim tglCutOff As Date = k.tm_area.TglCustOffCuti

    '    If dn Is Nothing Then Return 0 ' No contract data found

    '    Dim tglReloadCuti As Date? = k.TglReloadCuti


    '    ' Initialize startDateCuti based on permanent or contract status
    '    If dn.Tetap Then
    '        saCuti = 12
    '        tglReloadCuti = If(tglReloadCuti, dn.TglTetap)
    '        'startDateCuti = If(k.TglReloadCuti, dn.TglTetap)
    '    Else
    '        saCuti = dn.SaldoAwalCuti
    '        tglReloadCuti = If(tglReloadCuti, dn.TglAwalKontrak)
    '        'startDateCuti = If(k.TglReloadCuti, dn.TglAwalKontrak)
    '    End If

    '    If tglReloadCuti.HasValue Then
    '        tglReloadCuti = New Date(PerTanggal.Year, tglReloadCuti.Value.Month, tglReloadCuti.Value.Day)
    '    End If
    '    If tglReloadCuti < tglCutOff Then
    '        saCuti = dn.SaldoAwalCuti
    '        startDateCuti = tglCutOff
    '    Else
    '        startDateCuti = tglReloadCuti
    '    End If


    '    ' Adjust contract-based leave calculation
    '    If Not dn.Tetap Then
    '        Dim t1 As Date = dn.TglAwalKontrak
    '        Dim t2 As Date = dn.TglAkhirKontrak

    '        If startDateCuti > t2 Or startDateCuti > PerTanggal Then
    '            saCuti = 0
    '        ElseIf startDateCuti < t1 Then
    '            saCuti = DateDiff(DateInterval.Month, t1, t2)
    '        Else
    '            saCuti = DateDiff(DateInterval.Month, startDateCuti, t2)
    '        End If
    '    End If

    '    ' Adjust start date for current year
    '    'startDateCuti = New Date(If(startDateCuti.Month > PerTanggal.Month, PerTanggal.Year - 1, PerTanggal.Year), startDateCuti.Month, startDateCuti.Day)

    '    ' If the start date is before the cutoff, reset saCuti to initial balance
    '    'If startDateCuti < tglCutOff Then
    '    '    saCuti = dn.SaldoAwalCuti
    '    '    startDateCuti = tglCutOff
    '    'End If

    '    ' Calculate the used leave
    '    Dim absCount = daoAbs.FindBy(Function(f) f.karyawan_id = id_kar AndAlso
    '                                      (f.tr_ijin.tm_ijin_master.PotongCutiTahunan Or f.CutiBersama) AndAlso
    '                                      f.Tanggal >= startDateCuti And f.Tanggal < PerTanggal).Count

    '    Return saCuti - absCount
    'End Function

    Public Shared Function GetSaldoCuti(id_kar As String, PerTanggal As Date) As Integer
        Dim dao = daoFactory.GetKaryawanDao
        Dim daoAbs = daoFactory.GetAbsensiDao

        ' Retrieve employee and their latest data dinas
        Dim k = dao.FindBy(Function(f) f.Id = id_kar).FirstOrDefault
        If k Is Nothing Then Return 0

        Dim dn = k.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).FirstOrDefault
        If dn Is Nothing Then Return 0

        ' Initialize variables
        Dim saCuti As Integer = If(dn.Tetap, 12, dn.SaldoAwalCuti)
        Dim startDateCuti As Date = If(dn.Tetap, dn.TglTetap, dn.TglAwalKontrak)
        Dim tglCutOff As Date = k.tm_area.TglCustOffCuti

        ' Adjust start date based on reload cuti or cutoff
        If k.TglReloadCuti.HasValue Then
            Dim reloadDate = New Date(PerTanggal.Year, k.TglReloadCuti.Value.Month, k.TglReloadCuti.Value.Day)
            startDateCuti = If(reloadDate < tglCutOff, tglCutOff, reloadDate)
        ElseIf startDateCuti < tglCutOff Then
            startDateCuti = tglCutOff
        End If

        ' Calculate remaining cuti for non-permanent employees
        If Not dn.Tetap Then
            Dim t1 As Date = dn.TglAwalKontrak
            Dim t2 As Date = dn.TglAkhirKontrak
            If startDateCuti > t2 Then Return 0
            saCuti = If(startDateCuti < t1, DateDiff(DateInterval.Month, t1, t2), DateDiff(DateInterval.Month, startDateCuti, t2))
            If DateDiff(DateInterval.Month, k.TglMasuk, PerTanggal) < 12 Then saCuti = 0
        End If

        ' Adjust start date for current year
        If startDateCuti.Month > PerTanggal.Month Then
            startDateCuti = New Date(PerTanggal.Year - 1, startDateCuti.Month, startDateCuti.Day)
        Else
            startDateCuti = New Date(PerTanggal.Year, startDateCuti.Month, startDateCuti.Day)
        End If

        ' Calculate used cuti
        Dim abs = daoAbs.FindBy(Function(f) f.karyawan_id = id_kar AndAlso
                                      (f.Cuti_b OrElse f.tr_ijin.tm_ijin_master.PotongCutiTahunan OrElse
                                       (f.LiburNasional AndAlso f.CutiBersama)) AndAlso
                                      f.tr_ijin.tm_ijin_master.KodeIjin <> "CM" AndAlso
                                      f.Tanggal >= startDateCuti AndAlso f.Tanggal < PerTanggal AndAlso
                                      f.Tanggal >= tglCutOff).Count

        ' Calculate remaining cuti
        Dim sal = saCuti - abs
        Return If(sal < 0, 0, sal)
    End Function
    'Public Shared Function GetSaldoCuti(id_kar As String, PerTanggal As Date) As Integer
    '    Dim saCuti As Integer = 0
    '    Dim dao = daoFactory.GetKaryawanDao

    '    'Dim tglCutOff As Date = New Date(2024, 9, 21)

    '    Dim startDateCuti As Date = Format(PerTanggal, "yyyy-01-01")

    '    Dim daoAbs = daoFactory.GetAbsensiDao
    '    Dim k = dao.FindBy(Function(f) f.Id = id_kar).FirstOrDefault
    '    Dim dn = k.tm_karyawan_datadinas.OrderByDescending(Function(od) od.Id).Take(1).FirstOrDefault

    '    Dim tglCutOff As Date = k.tm_area.TglCustOffCuti

    '    If dn Is Nothing Then
    '        saCuti = 0
    '        Return 0
    '    Else
    '        saCuti = dn.SaldoAwalCuti
    '        If dn.Tetap <> True Then
    '            If k.TglReloadCuti.HasValue Then
    '                startDateCuti = Format(k.TglReloadCuti, "yyyy-MM-dd")
    '            Else
    '                startDateCuti = Format(dn.TglAwalKontrak, "yyyy-MM-dd")
    '            End If

    '        Else
    '            If k.TglReloadCuti.HasValue Then
    '                startDateCuti = Format(k.TglReloadCuti.Value, "yyyy-MM-dd")
    '            Else
    '                startDateCuti = Format(dn.TglTetap, "yyyy-MM-dd")
    '            End If

    '        End If
    '    End If

    '    If dn.Tetap <> True Then
    '        Dim t1 As Date = dn.TglAwalKontrak
    '        Dim t2 As Date = dn.TglAkhirKontrak

    '        If startDateCuti > t2 Then
    '            saCuti = 0
    '        End If
    '        If startDateCuti < t1 Then
    '            saCuti = DateDiff(DateInterval.Month, t1, t2)

    '        End If
    '        If startDateCuti >= t1 AndAlso startDateCuti <= t2 Then
    '            saCuti = DateDiff(DateInterval.Month, startDateCuti, t2)
    '        End If
    '        Dim lamaBekerja = DateDiff(DateInterval.Month, k.TglMasuk, PerTanggal)
    '        If lamaBekerja < 12 Then
    '            saCuti = 0
    '        End If

    '    Else
    '        saCuti = 12

    '    End If

    '    If startDateCuti.Month > PerTanggal.Month Then
    '        startDateCuti = Format(PerTanggal, String.Format("{0}-{1}-{2}", PerTanggal.Year - 1, startDateCuti.Month, startDateCuti.Day))
    '    Else
    '        startDateCuti = Format(PerTanggal, String.Format("{0}-{1}-{2}", PerTanggal.Year, startDateCuti.Month, startDateCuti.Day))
    '    End If

    '    If startDateCuti < tglCutOff Then
    '        saCuti = dn.SaldoAwalCuti
    '    End If



    '    Dim tgl2 As Date = PerTanggal
    '    Dim abs = daoAbs.FindBy(Function(f) f.karyawan_id = id_kar AndAlso (f.Cuti_b = True Or f.tr_ijin.tm_ijin_master.PotongCutiTahunan = True Or (f.LiburNasional = True AndAlso f.CutiBersama = True)) And f.tr_ijin.tm_ijin_master.KodeIjin <> "CM" AndAlso f.Tanggal >= startDateCuti And f.Tanggal < tgl2 AndAlso f.Tanggal >= tglCutOff).Count

    '    Dim sal = saCuti - abs
    '    If sal < 0 Then
    '        sal = 0
    '    End If
    '    Return sal
    'End Function


    Public Shared Function CekHariLibur(area_id As String, kar As tm_karyawan, tgl As Date, ByRef ket As String, Optional ByRef cutiBersama As Boolean = False) As Boolean
        Dim daoLN = daoFactory.GetLiburNasionalDao

        Dim ln = daoLN.FindBy(Function(f) f.Area_id = area_id AndAlso tgl >= f.StartDate And tgl <= f.EndDate).FromCache(absoluteExpiration:=Now.AddMinutes(1)).FirstOrDefault

        If ln IsNot Nothing Then
            If kar.TipeAbsensiOFF = 0 Then
                cutiBersama = ln.CutiBersama
                ket = ln.NamaHariLibur
                Return True
            ElseIf kar.TipeAbsensiOFF = 1 Then
                If ln.CutiBersama = True Then
                    Return CekHariLiburOFF(kar.Id, tgl, ket)
                Else
                    ket = ln.NamaHariLibur
                    Return True
                End If
            Else
                If ln.CutiBersama Then

                    Return False
                Else
                    ket = ln.NamaHariLibur
                    Return True
                End If
            End If
        Else
            If kar.TipeAbsensiOFF = 0 Then
                If CekLiburSabtuMinggu(kar.Cabang_id, tgl, ket) Then
                    Return True
                End If
            ElseIf kar.TipeAbsensiOFF = 1 Then
                Return CekHariLiburOFF(kar.Id, tgl, ket)
            End If

        End If

        Return False
    End Function
    Public Shared Function CekLiburSabtuMinggu(cabang_id As String, tgl As Date, ByRef ket As String) As Boolean
        Dim _cab = myFunction.tm_cabang(cabang_id)

        If Weekday(tgl) = 1 Then
            If _cab.MingguLibur Then
                ket = $"Libur Minggu:{ket}"
                Return True
            End If
        End If
        If Weekday(tgl) = 7 Then
            If _cab.SabtuLibur Then
                ket = $"Libur Sabtu:{ket}"
                Return True
            End If
        End If
        Return False
    End Function
    Public Shared Function CekHariJumat(tgl As Date) As Boolean
        Return tgl.DayOfWeek = DayOfWeek.Friday
    End Function
    Public Shared Function CekHariSabtu(tgl As Date) As Boolean
        Return tgl.DayOfWeek = DayOfWeek.Saturday
    End Function
    Public Shared Function CekHariMinggu(tgl As Date) As Boolean
        Return tgl.DayOfWeek = DayOfWeek.Sunday
    End Function

    Public Shared Function GetJumlahHariLiburNasionals(cabang_id As String, startDate As Date, endDate As Date) As Integer
        Dim mgr = daoFactory.GetLiburNasionalDao
        Dim cab = myFunction.tm_cabang(cabang_id)

        ' Ambil daftar libur nasional yang berada dalam rentang tanggal yang diberikan
        Dim os = mgr.FindBy(Function(f) f.Area_id = cab.Area_id AndAlso f.StartDate <= endDate And f.EndDate >= startDate AndAlso f.CutiBersama <> True).FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)})

        ' Gabungkan periode libur yang overlapping
        Dim mergedPeriods = MergeOverlappingPeriods(os, startDate, endDate)

        ' Hitung total hari libur, tidak termasuk hari Sabtu dan Minggu jika berlaku
        Dim totalHariLibur As Integer = 0
        For Each period In mergedPeriods
            Dim totalDays As Integer = DateDiff(DateInterval.Day, period.StartDate, period.EndDate) + 1
            Dim fullWeeks As Integer = totalDays \ 7
            Dim remainingDays As Integer = totalDays Mod 7
            Dim startDayOfWeek As Integer = period.StartDate.DayOfWeek
            Dim endDayOfWeek As Integer = period.EndDate.DayOfWeek

            ' Hitung jumlah hari akhir pekan
            Dim weekendDays As Integer = 0
            If cab.SabtuLibur = True And cab.MingguLibur = True Then
                weekendDays = fullWeeks * 2
                If remainingDays > 0 Then
                    If startDayOfWeek = 0 Then
                        weekendDays += 1
                    ElseIf endDayOfWeek = 6 Then
                        weekendDays += 1
                    ElseIf endDayOfWeek < startDayOfWeek Then
                        weekendDays += 2
                    End If
                End If
            ElseIf cab.SabtuLibur <> True And cab.MingguLibur = True Then
                weekendDays = fullWeeks * 1
                If remainingDays > 0 Then
                    If startDayOfWeek = 0 Then
                        weekendDays += 1
                    ElseIf endDayOfWeek = 6 Then
                        ' weekendDays += 1
                    ElseIf endDayOfWeek < startDayOfWeek Then
                        weekendDays += 1
                    End If
                End If
            End If

            ' Hitung jumlah hari libur
            totalHariLibur += totalDays - weekendDays
        Next

        Return totalHariLibur
    End Function

    Private Shared Function MergeOverlappingPeriods(os As IEnumerable(Of tm_libur_nasional), startDate As Date, endDate As Date) As List(Of (StartDate As Date, EndDate As Date))
        Dim periods As New List(Of (StartDate As Date, EndDate As Date))

        For Each o In os
            Dim periodStart As Date = If(o.StartDate < startDate, startDate, o.StartDate)
            Dim periodEnd As Date = If(o.EndDate > endDate, endDate, o.EndDate)

            periods.Add((periodStart, periodEnd))
        Next

        periods.Sort(Function(a, b) a.StartDate.CompareTo(b.StartDate))

        Dim mergedPeriods As New List(Of (StartDate As Date, EndDate As Date))
        If periods.Count = 0 Then
            Return mergedPeriods
        End If
        Dim currentPeriod = periods(0)

        For i As Integer = 1 To periods.Count - 1
            Dim nextPeriod = periods(i)
            If currentPeriod.EndDate >= nextPeriod.StartDate Then
                currentPeriod.EndDate = If(currentPeriod.EndDate > nextPeriod.EndDate, currentPeriod.EndDate, nextPeriod.EndDate)
            Else
                mergedPeriods.Add(currentPeriod)
                currentPeriod = nextPeriod
            End If
        Next

        mergedPeriods.Add(currentPeriod)
        Return mergedPeriods
    End Function

    Public Shared Function GetJumlahHariLiburNasionalsWithWeekEnd(cabang_id As String, startDate As Date, endDate As Date) As Integer
        Dim mgr = daoFactory.GetLiburNasionalDao
        Dim cab = myFunction.tm_cabang(cabang_id)

        ' Ambil daftar libur nasional yang berada dalam rentang tanggal yang diberikan
        Dim os = mgr.FindBy(Function(f) f.StartDate <= endDate And f.EndDate >= startDate AndAlso f.CutiBersama <> True).FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)})

        ' Gabungkan periode libur yang overlapping
        Dim mergedPeriods = MergeOverlappingPeriods(os, startDate, endDate)

        ' Hitung total hari libur, tidak termasuk hari Sabtu dan Minggu jika berlaku
        Dim totalHariLibur As Integer = 0
        For Each period In mergedPeriods
            Dim totalDays As Integer = DateDiff(DateInterval.Day, period.StartDate, period.EndDate) + 1

            ' Hitung jumlah hari libur
            totalHariLibur += totalDays
        Next

        Return totalHariLibur
    End Function

    Public Shared Function CekHariLiburOFF(kar_id As String, tgl As Date, ByRef ket As String) As Boolean
        Dim daoLN = daoFactory.GetOffKaryawanLineDao



        Dim tgl2 As Date = DateAdd(DateInterval.Day, 1, tgl)

        Dim bLibur As Boolean = False

        Dim ln = daoLN.FindBy(Function(f) f.tm_off_karyawan.TglBerlaku <= tgl And f.Karyawan_id = kar_id).OrderByDescending(Function(ob) ob.tm_off_karyawan.TglBerlaku).Take(1).FromCache(absoluteExpiration:=Now.AddMinutes(1)).FirstOrDefault
        If ln IsNot Nothing Then
            Select Case Weekday(tgl)
                Case 1
                    bLibur = ln.tm_shift_absensi_minggu.IsOff
                Case 2
                    bLibur = ln.tm_shift_absensi_senin.IsOff
                Case 3
                    bLibur = ln.tm_shift_absensi_selasa.IsOff
                Case 4
                    bLibur = ln.tm_shift_absensi_rabu.IsOff
                Case 5
                    bLibur = ln.tm_shift_absensi_kamis.IsOff
                Case 6
                    bLibur = ln.tm_shift_absensi_jumat.IsOff
                Case 7
                    bLibur = ln.tm_shift_absensi_sabtu.IsOff
                Case Else
                    bLibur = False
            End Select
        End If
        If bLibur Then
            ket = "OFF/Libur"
        Else
            ket = Nothing
        End If
        Return bLibur
    End Function
    Public Shared Function CekSPKLembur(tgl As Date, kar_id As String, ByRef ket As String, ByRef jamMasuk As DateTime, ByRef jamKeluar As DateTime) As tr_spk_lembur_line
        Dim daoLine = daoFactory.GetSPKLemburLineDao

        Dim oSPK = daoLine.FindBy(Function(f) f.Karyawan_id = kar_id And f.tr_spk_lembur.Posted = True And f.tr_spk_lembur.Tanggal.Year = tgl.Year And f.tr_spk_lembur.Tanggal.Month = tgl.Month And f.tr_spk_lembur.Tanggal.Day = tgl.Day).FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)}).FirstOrDefault
        If oSPK IsNot Nothing Then
            ket &= oSPK.tr_spk_lembur.Keterangan & " "
            jamMasuk = oSPK.tr_spk_lembur.StartTime
            jamKeluar = oSPK.tr_spk_lembur.EndTime
            If jamKeluar < jamMasuk Then
                jamKeluar = jamMasuk.AddDays(1).Date.Add(jamKeluar.TimeOfDay)
            End If
            Return oSPK
        End If

        Return Nothing
    End Function
    Public Shared Function UpdateAbsensiDataLembur(absensi As tr_absensi, oKar As tm_karyawan) As Boolean
        Try
            Dim _jamMasukLembur As DateTime
            Dim _jamkeluarLembur As DateTime
            Dim _ketLembur As String = Nothing
            Dim spkLine = CekSPKLembur(absensi.Tanggal, absensi.karyawan_id, _ketLembur, _jamMasukLembur, _jamkeluarLembur)
            If spkLine IsNot Nothing Then
                absensi.LemburLine_id = spkLine.Id
                absensi.Keterangan = _ketLembur
                If CekHariLibur(spkLine.tr_spk_lembur.Area_id, oKar, absensi.Tanggal, _ketLembur) Then
                    absensi.Terlambat = 0
                    absensi.Overtime = 0
                    absensi.Lembur_HKerja_1 = 0
                    absensi.Lembur_HKerja_2 = 0
                    absensi.JamMasuk = spkLine.tr_spk_lembur.StartTime
                    absensi.JamKeluar = spkLine.tr_spk_lembur.EndTime
                    absensi.OverTime_HLibur = DateDiff(DateInterval.Minute, _jamMasukLembur, _jamkeluarLembur)
                    If absensi.OverTime_HLibur < 0 Then
                        absensi.OverTime_HLibur = 0
                    End If
                    If spkLine.tr_spk_lembur.PotJamIstirahat Then
                        absensi.potong1Jam = 60
                    Else
                        absensi.potong1Jam = 0
                    End If
                    If oKar.tm_cabang.SabtuLibur And oKar.tm_cabang.MingguLibur Then
                        'absensi.Lembur_HLibur_1_7 = absensi.JmlJamLemburHLibur5_1SD8
                        'absensi.Lembur_HLibur_8 = absensi.JmlJamLemburHLibur5_9
                        'absensi.Lembur_HLibur_9_10 = absensi.JmlJamLemburHLibur5_10SD11
                        absensi.Lembur_HLibur_1_7 = absensi.JmlJamLemburHLibur6_1SD7
                        absensi.Lembur_HLibur_8 = absensi.JmlJamLemburHLibur6_8
                        absensi.Lembur_HLibur_9_10 = absensi.JmlJamLemburHLibur6_10SD11
                    Else
                        absensi.Lembur_HLibur_1_7 = absensi.JmlJamLemburHLibur6_1SD7
                        absensi.Lembur_HLibur_8 = absensi.JmlJamLemburHLibur6_8
                        absensi.Lembur_HLibur_9_10 = absensi.JmlJamLemburHLibur6_10SD11
                    End If


                Else
                    absensi.OverTime_HLibur = 0
                    absensi.Lembur_HLibur_1_7 = 0
                    absensi.Lembur_HLibur_8 = 0
                    absensi.Lembur_HLibur_9_10 = 0

                    absensi.Overtime = DateDiff(DateInterval.Minute, _jamMasukLembur, _jamkeluarLembur)
                    If absensi.Overtime < 0 Then
                        absensi.Overtime = 0
                    End If

                    If spkLine.tr_spk_lembur.PotJamIstirahat Then
                        absensi.potong1Jam = 60
                    Else
                        absensi.potong1Jam = 0
                    End If

                    absensi.Lembur_HKerja_1 = absensi.JmlJamLemburHBiasa_1
                    absensi.Lembur_HKerja_2 = absensi.JmlJamLemburHBiasa_2
                End If

                Return True
            End If

            Return False
        Catch ex As Exception
            Return False

        End Try

    End Function

    Public Shared Function CalculateWeekdays(cabang_id As String, startDate As Date, endDate As Date, ByRef allDay As Integer) As Integer

        Dim cab = myFunction.tm_cabang(cabang_id)

        Dim totalDays As Integer = DateDiff(DateInterval.Day, startDate, endDate) + 1
        Dim fullWeeks As Integer = totalDays \ 7
        Dim remainingDays As Integer = totalDays Mod 7
        Dim startDayOfWeek As Integer = startDate.DayOfWeek
        Dim endDayOfWeek As Integer = endDate.DayOfWeek

        ' Calculate the number of weekend days
        Dim weekendDays As Integer = 0
        If cab.SabtuLibur = True And cab.MingguLibur = True Then
            weekendDays = fullWeeks * 2
            If remainingDays > 0 Then
                If startDayOfWeek = 0 Then
                    weekendDays += 1
                ElseIf endDayOfWeek = 6 Then
                    weekendDays += 1
                ElseIf endDayOfWeek < startDayOfWeek Then
                    weekendDays += 2
                End If
            End If
        ElseIf cab.MingguLibur = True And cab.SabtuLibur = False Then
            weekendDays = fullWeeks * 1
            If remainingDays > 0 Then
                If startDayOfWeek = 0 Then
                    weekendDays += 1
                ElseIf endDayOfWeek = 6 Then
                    'weekendDays += 1
                ElseIf endDayOfWeek < startDayOfWeek Then
                    weekendDays += 1
                End If
            End If
        End If


        ' Calculate the number of holidays
        Dim holidays As Integer = GetJumlahHariLiburNasionals(cabang_id, startDate, endDate)
        'Dim holidaysWeekEnd As Integer = GetJumlahHariLiburNasionalsWithWeekEnd(cabang_id, startDate, endDate)

        ' Calculate the number of weekdays
        Dim weekdays As Integer = totalDays - weekendDays - holidays

        allDay = totalDays '- holidaysWeekEnd
        Return weekdays


    End Function
    Public Shared Function CalculateAssuransi(gajiPokok As Decimal, id_kar As String, cabang_id As String) As Object
        Dim pend_jkk As Decimal = 0
        Dim pend_jkm As Decimal = 0
        Dim pend_jht As Decimal = 0
        Dim pend_jpk As Decimal = 0
        Dim pend_jp As Decimal = 0

        Dim pot_jht_kar As Decimal = 0
        Dim pot_jpk_kar As Decimal = 0
        Dim pot_jp_kar As Decimal = 0

        Dim pot_jpk_mandiri As Decimal = 0

        Dim upahminimum_tk As Decimal = 0
        Dim upahminimum_kes As Decimal = 0
        Dim flatratebpjs As Boolean = False
        Dim bBPJS_TK As Boolean = False
        Dim bBPJS_Kes As Boolean = False

        If cabang_id <> "null" Then
            Dim kantor = myFunction.tm_cabang(cabang_id)
            upahminimum_tk = kantor.UpahMinimum
            upahminimum_kes = kantor.UpahMinimum_Kes
            flatratebpjs = kantor.tm_area.FlatRateBPJS
        End If

        Dim okar = myFunction.tm_karyawan(id_kar)
        If okar IsNot Nothing Then
            bBPJS_TK = okar.IsBPJS_TK
            bBPJS_Kes = okar.IsBPJS_Kes
        End If
        If flatratebpjs Then
            pend_jkk = 26746D
            pend_jkm = 14859D
            pend_jht = 183261D
            pend_jp = 99060D

            pot_jht_kar = 99060D
            pot_jp_kar = 49530D

            pend_jpk = 189000D
            pot_jpk_kar = 47250D
        Else
            If gajiPokok > upahminimum_tk Then
                If gajiPokok > 12000000 Then
                    pend_jkk = If(bBPJS_TK, (0.24 / 100) * gajiPokok, 0)
                    pend_jkm = If(bBPJS_TK, (0.3 / 100) * gajiPokok, 0)
                    pend_jht = If(bBPJS_TK, (3.7 / 100) * gajiPokok, 0)
                    pend_jp = If(bBPJS_TK, (2 / 100) * 10042300, 0)

                    pot_jht_kar = If(bBPJS_TK, (2 / 100) * gajiPokok, 0)
                    pot_jp_kar = If(bBPJS_TK, (1 / 100) * 10042300, 0)
                Else
                    pend_jkk = If(bBPJS_TK, (0.24 / 100) * gajiPokok, 0)
                    pend_jkm = If(bBPJS_TK, (0.3 / 100) * gajiPokok, 0)
                    pend_jht = If(bBPJS_TK, (3.7 / 100) * gajiPokok, 0)
                    pend_jp = If(bBPJS_TK, (2 / 100) * gajiPokok, 0)

                    pot_jht_kar = If(bBPJS_TK, (2 / 100) * gajiPokok, 0)
                    pot_jp_kar = If(bBPJS_TK, (1 / 100) * gajiPokok, 0)
                End If

            Else
                pend_jkk = If(bBPJS_TK, (0.24 / 100) * upahminimum_tk, 0)
                pend_jkm = If(bBPJS_TK, (0.3 / 100) * upahminimum_tk, 0)
                pend_jht = If(bBPJS_TK, (3.7 / 100) * upahminimum_tk, 0)
                pend_jp = If(bBPJS_TK, (2 / 100) * upahminimum_tk, 0)

                pot_jht_kar = If(bBPJS_TK, (2 / 100) * upahminimum_tk, 0)
                pot_jp_kar = If(bBPJS_TK, (1 / 100) * upahminimum_tk, 0)
            End If
            If gajiPokok > upahminimum_kes Then
                If gajiPokok > 12000000 Then
                    pend_jpk = If(bBPJS_Kes, (4 / 100) * 12000000, 0)
                    pot_jpk_kar = If(bBPJS_Kes, (1 / 100) * 12000000, 0)
                Else
                    pend_jpk = If(bBPJS_Kes, (4 / 100) * gajiPokok, 0)
                    pot_jpk_kar = If(bBPJS_Kes, (1 / 100) * gajiPokok, 0)
                End If


            Else
                pend_jpk = If(bBPJS_Kes, (4 / 100) * upahminimum_kes, 0)
                pot_jpk_kar = If(bBPJS_Kes, (1 / 100) * upahminimum_kes, 0)
            End If

            If id_kar = "null" Then
                id_kar = 0
            End If

            If okar IsNot Nothing Then
                If okar.StatusPerkawinan_id.HasValue Then
                    If okar.tm_status_PTKP.KodeStatus.Trim.Substring(0, 2).ToLower = "tk" Then
                        pot_jpk_mandiri = (2 / 100) * gajiPokok
                    Else
                        pot_jpk_mandiri = (4 / 100) * gajiPokok
                    End If
                Else
                    pot_jpk_mandiri = (4 / 100) * gajiPokok
                End If

            Else
                pot_jpk_mandiri = (4 / 100) * gajiPokok
            End If
        End If


        Return New With {.Pd_JKK = pend_jkk, .Pd_JKM = pend_jkm, .Pd_JHT = pend_jht, .Pd_JPK = pend_jpk, .Pd_JP = pend_jp _
            , .Pt_K_JHT = pot_jht_kar, .Pt_K_JPK = pot_jpk_kar, .Pt_K_JP = pot_jp_kar, .Pt_K_Jpk_Mandiri = pot_jpk_mandiri, .Pt_P_JHT = pend_jht _
            , .Pt_P_JKK = pend_jkk, .Pt_P_JKM = pend_jkm, .Pt_P_JP = pend_jp, .Pt_P_JPK = pend_jpk}

    End Function

    Public Shared Function GetPPH21(totalPendapatan As Decimal, id_kar As String, zakat As Decimal) As Decimal
        Dim pend1Tahun As Decimal = 12 * totalPendapatan
        Dim BiayaJamsostek As Decimal = 0
        If pend1Tahun >= 48000000 Then
            BiayaJamsostek = 2400000
        Else
            BiayaJamsostek = (5 / 100) * pend1Tahun
        End If
        Dim BiayaJabatan As Decimal = 0
        If pend1Tahun >= 120000000 Then
            BiayaJabatan = 6000000
        Else
            BiayaJabatan = (5 / 100) * pend1Tahun
        End If
        Dim PTKP As Decimal = 0

        Dim oKar = myFunction.tm_karyawan(id_kar)
        If oKar IsNot Nothing Then
            Dim o = myFunction.tm_status_PTKP(oKar.StatusPerkawinan_id)
            PTKP = o.PTKP
        Else
            Dim o = myFunction.tm_status_PTKP_byCode("K/1")
            PTKP = o.PTKP
        End If

        Dim totalPotongan = BiayaJamsostek + BiayaJabatan + zakat + PTKP
        Dim PKP = pend1Tahun - totalPotongan
        Dim pph As Decimal = 0

        If PKP > 0 And PKP < 60000000 Then
            pph = (5 / 100) * PKP
        ElseIf PKP >= 60000000 Then
            'pph = 2500000
            pph = 3000000
        End If

        If PKP >= 250000000 Then
            pph += 28500000
        ElseIf PKP > 60000000 And PKP < 250000000 Then
            pph += (15 / 100) * (PKP - 60000000)
        End If

        If PKP >= 500000000 Then
            pph += 62500000
        ElseIf PKP > 250000000 And PKP < 500000000 Then
            pph += (25 / 100) * (PKP - 250000000)
        End If

        If PKP > 500000000 Then
            pph += (30 / 100) * (PKP - 500000000)
        End If
        Return pph / 12
    End Function
    Public Shared Function PPh21CalculatorTahunan(totalPendapatanBrutoTahunan As Decimal, zakat As Decimal, kode_statusPTKP As String) As Decimal

        Dim BiayaJamsostek As Decimal = 0
        If totalPendapatanBrutoTahunan >= 60000000 Then
            BiayaJamsostek = 2400000
        Else
            BiayaJamsostek = (5 / 100) * totalPendapatanBrutoTahunan
        End If
        Dim BiayaJabatan As Decimal = 0
        If totalPendapatanBrutoTahunan >= 120000000 Then
            BiayaJabatan = 6000000
        Else
            BiayaJabatan = (5 / 100) * totalPendapatanBrutoTahunan
        End If
        Dim PTKP As Decimal = 0

        Dim oPTKP = myFunction.tm_status_PTKP_byCode(kode_statusPTKP)
        If oPTKP IsNot Nothing Then
            PTKP = oPTKP.PTKP
        End If

        Dim totalPotongan = BiayaJamsostek + BiayaJabatan + zakat + PTKP
        Dim PKP = totalPendapatanBrutoTahunan - totalPotongan
        Dim pph As Decimal = 0

        If PKP > 0 And PKP < 60000000 Then
            pph = (5 / 100) * PKP
        ElseIf PKP >= 60000000 Then
            'pph = 2500000
            pph = 3000000
        End If

        If PKP >= 250000000 Then
            pph += 28500000
        ElseIf PKP > 60000000 And PKP < 250000000 Then
            pph += (15 / 100) * (PKP - 60000000)
        End If

        If PKP >= 500000000 Then
            pph += 62500000
        ElseIf PKP > 250000000 And PKP < 500000000 Then
            pph += (25 / 100) * (PKP - 250000000)
        End If

        If PKP > 500000000 Then
            pph += (30 / 100) * (PKP - 500000000)
        End If
        Return pph
    End Function

    Public Shared Function PPh21CalculatorBulanan(totalPendapatanBrutoBulanan As Decimal, kode_statusPTKP As String) As (ter_pajak_id As String, tarifPercent As Decimal, nilaiPajak As Decimal)
        Dim daoterPajak = daoFactory.GetTerPajakDao


        Select Case kode_statusPTKP.ToUpper
            Case "TK/0", "TK/1", "K/0"
                Return daoterPajak.GetTER_PAJAK("A", totalPendapatanBrutoBulanan)
            Case "TK/2", "TK/3", "K/1", "K/2"
                Return daoterPajak.GetTER_PAJAK("B", totalPendapatanBrutoBulanan)
            Case "K/3"
                Return daoterPajak.GetTER_PAJAK("C", totalPendapatanBrutoBulanan)
            Case Else
                Return daoterPajak.GetTER_PAJAK("H", totalPendapatanBrutoBulanan)
        End Select
    End Function

    Public Shared Function GetTotalPendapatanTahunan(kar_id As String, tahun As Integer) As Decimal
        Dim mgr = daoFactory.GetGajiLineDao
        Dim os = mgr.FindBy(Function(f) f.tr_gaji.Posted = True And f.tr_gaji.Tanggal.Year = tahun And f.tr_gaji.Tanggal.Month <= 11 And f.Karyawan_id = kar_id)
        Return If(os.Sum(Function(f) CType(f.TotalPendapatan, Decimal?)), 0)
    End Function
    Public Shared Function GetTotalPph21Tahunan(kar_id As String, tahun As Integer) As Decimal
        Dim mgr = daoFactory.GetGajiLineDao
        Dim os = mgr.FindBy(Function(f) f.tr_gaji.Posted = True And f.tr_gaji.Tanggal.Year = tahun And f.tr_gaji.Tanggal.Month <= 11 And f.Karyawan_id = kar_id)
        Return If(os.Sum(Function(f) CType(f.Pt_PPH21, Decimal?)), 0)
    End Function
    Public Shared Function GeneratePayrollSlip(report As XtraReport) As Byte()
        ' Konfigurasi laporan Anda di sini, misalnya menggunakan DevExpress XtraReports
        'Dim report As New XtraReport() ' Atur laporan Anda di sini

        ' Filter laporan berdasarkan ID karyawan
        'report.FilterString = $"[EmployeeID] = {employeeId}"

        ' Export laporan ke PDF
        Using ms As New MemoryStream()
            report.ExportToPdf(ms)
            Return ms.ToArray()
        End Using
    End Function
    Public Shared Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr = New MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function
    Public Shared Function HitungJumlahHariKerja(cabang_id As String, tipeAbsen As Integer, ByVal startDate As Date, ByVal endDate As Date) As Integer
        Dim cab = myFunction.tm_cabang(cabang_id)
        Dim tgl1 As Date = startDate.Date
        Dim totalWeekdays As Integer = 0

        While tgl1 <= endDate
            If tipeAbsen = 0 Or tipeAbsen = 3 Then
                If tgl1.DayOfWeek = DayOfWeek.Saturday Then
                    If cab.SabtuLibur <> True Then
                        If CekHariLiburNasional(tgl1, cab.Area_id) = False Then
                            totalWeekdays += 1
                        End If
                    End If
                ElseIf tgl1.DayOfWeek = DayOfWeek.Sunday Then
                    If cab.MingguLibur <> True Then
                        If CekHariLiburNasional(tgl1, cab.Area_id) = False Then
                            totalWeekdays += 1
                        End If
                    End If
                Else
                    If CekHariLiburNasional(tgl1, cab.Area_id) = False Then
                        totalWeekdays += 1
                    End If
                End If

            Else
                totalWeekdays += 1
            End If


            tgl1 = tgl1.AddDays(1)
        End While
        If tipeAbsen = 0 Or tipeAbsen = 3 Then
            Return totalWeekdays
        Else
            Return totalWeekdays - HitungJumlahMinggu(startDate, endDate)
        End If

    End Function
    Public Shared Function HitungJumlahMinggu(startDate As Date, endDate As Date) As Integer
        Dim totalWeeks As Integer = DatePart(DateInterval.WeekOfYear, endDate) - DatePart(DateInterval.WeekOfYear, startDate) + 1
        Return totalWeeks
    End Function
    Public Shared Function HitungGantiHari(karyawan_id As String, startDate As Date, endDate As Date) As Integer
        Dim os = GetAbsensis(karyawan_id, startDate, endDate)
        os = os.Where(Function(f) f.GantiHari = True)
        Return os.Count
    End Function
    Public Shared Function CekHariLiburNasional(ByVal tanggal As Date, ByVal area_id As String) As Boolean
        Dim hariLibur As Boolean = False
        Dim mgr = daoFactory.GetLiburNasionalDao
        Dim o = mgr.FindBy(Function(f) f.Area_id = area_id AndAlso tanggal >= f.StartDate AndAlso tanggal <= f.EndDate AndAlso f.CutiBersama <> True).FromCache(New Runtime.Caching.CacheItemPolicy With {.AbsoluteExpiration = Now.AddMinutes(1)}).FirstOrDefault
        If o IsNot Nothing Then
            hariLibur = True
        End If

        Return hariLibur
    End Function


End Class
