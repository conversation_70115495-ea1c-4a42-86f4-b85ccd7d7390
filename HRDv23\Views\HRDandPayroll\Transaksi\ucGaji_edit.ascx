﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucGaji_edit.ascx.vb" Inherits="HRDv23.ucGaji_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Perusahaan" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn Caption="Kode Perusahaan" FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn Caption="Nama Perusahaan" FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" NullText="ALL">
                                        <ClientSideEvents Init="onInitCB" />
                                        <Columns>
                                            <dx:ListBoxColumn Caption="Kode Cabang" FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn Caption="Nama Cabang" FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ClearButton DisplayMode="Always">
                                        </ClearButton>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" />
                                        </ValidationSettings>
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" Caption="Periode">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="txt_periode" runat="server" Width="170px" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Tanggal">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E6" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Keterangan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxMemo ID="ASPxFormLayout1_E22" runat="server">
                                    </dx:ASPxMemo>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="Detil Gaji" ColSpan="2" ColumnSpan="2">
                            <Items>
                                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id">
                                                <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_department.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_department.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}

}" ToolbarItemClick="function(s, e) {
	switch (e.item.name) { 
case 'btn_new':
cp_department.PerformCallback('new'); 
break;
}

}" />
                                                <SettingsAdaptivity AdaptivityMode="HideDataCells">
                                                </SettingsAdaptivity>
                                                <SettingsPager AlwaysShowPager="True">
                                                    <AllButton Visible="True">
                                                    </AllButton>
                                                    <PageSizeItemSettings Visible="True">
                                                    </PageSizeItemSettings>
                                                </SettingsPager>
                                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" HorizontalScrollBarMode="Visible" ShowFooter="True" VerticalScrollBarMode="Auto" />
                                                <SettingsBehavior AllowFocusedRow="True" />
                                                <SettingsPopup>
                                                    <FilterControl AutoUpdatePosition="False">
                                                    </FilterControl>
                                                </SettingsPopup>
                                                <SettingsSearchPanel Visible="True" />
                                                <SettingsExport EnableClientSideExportAPI="True">
                                                </SettingsExport>
                                                <Columns>
                                                    <dx:GridViewDataTextColumn FieldName="tm_karyawan.NIK" ShowInCustomizationForm="True" VisibleIndex="0" Caption="NIK">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="THP" ShowInCustomizationForm="True" VisibleIndex="18">
                                                        <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                        </PropertiesTextEdit>
                                                        <HeaderStyle Font-Bold="True" ForeColor="Green" />
                                                        <CellStyle Font-Bold="True" ForeColor="Green">
                                                        </CellStyle>
                                                        <FooterCellStyle Font-Bold="True" ForeColor="Green">
                                                        </FooterCellStyle>
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="tm_karyawan.Nama" ShowInCustomizationForm="True" VisibleIndex="1" Caption="Nama">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewBandColumn Caption="PENDAPATAN" VisibleIndex="16" ShowInCustomizationForm="True">
                                                        <HeaderStyle Font-Bold="True" ForeColor="Blue" HorizontalAlign="Center" />
                                                        <Columns>
                                                            <dx:GridViewDataTextColumn Caption="Gaji Pokok" FieldName="Pd_GajiPokok" ShowInCustomizationForm="True" VisibleIndex="0">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Jabatan" FieldName="Pd_T_Jabatan" ShowInCustomizationForm="True" VisibleIndex="1">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Makan" FieldName="Pd_T_Makan" ShowInCustomizationForm="True" VisibleIndex="3">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Premi Hdr" FieldName="Pd_T_PremiHadir" ShowInCustomizationForm="True" VisibleIndex="4">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Susu" FieldName="Pd_T_Susu" ShowInCustomizationForm="True" VisibleIndex="5">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. JKK" FieldName="Pd_T_JKK" ShowInCustomizationForm="True" VisibleIndex="10">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. JKM" FieldName="Pd_T_JKM" ShowInCustomizationForm="True" VisibleIndex="11">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. JHT" FieldName="Pd_T_JHT" ShowInCustomizationForm="True" VisibleIndex="12">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. JPK" FieldName="Pd_T_JPK" ShowInCustomizationForm="True" VisibleIndex="13">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. JP" FieldName="Pd_T_JP" ShowInCustomizationForm="True" VisibleIndex="14">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="TotalPendapatan" ShowInCustomizationForm="True" VisibleIndex="18">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                                <HeaderStyle Font-Bold="True" ForeColor="Blue" />
                                                                <CellStyle Font-Bold="True" ForeColor="Blue">
                                                                </CellStyle>
                                                                <FooterCellStyle Font-Bold="True" ForeColor="Blue">
                                                                </FooterCellStyle>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Kontrak" FieldName="Pd_T_Kontrak" ShowInCustomizationForm="True" VisibleIndex="6">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Probation" FieldName="Pd_T_Probation" ShowInCustomizationForm="True" VisibleIndex="7">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. PremiAss" FieldName="Pd_T_PremiAss" ShowInCustomizationForm="True" VisibleIndex="8">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Pajak" FieldName="Pd_T_Pajak" ShowInCustomizationForm="True" VisibleIndex="9">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="Upah Lembur" FieldName="Pd_Lembur" ShowInCustomizationForm="True" VisibleIndex="17">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Transport" FieldName="Pd_T_Transport" ShowInCustomizationForm="True" VisibleIndex="2">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Insentif" FieldName="Pd_Insentif" ShowInCustomizationForm="True" VisibleIndex="15">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n0">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="T. Lain" FieldName="Pd_T_Lain" ShowInCustomizationForm="True" VisibleIndex="16">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n0">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                        </Columns>
                                                    </dx:GridViewBandColumn>
                                                    <dx:GridViewBandColumn Caption="POTONGAN" VisibleIndex="17" ShowInCustomizationForm="True">
                                                        <HeaderStyle Font-Bold="True" ForeColor="Red" HorizontalAlign="Center" />
                                                        <Columns>
                                                            <dx:GridViewDataTextColumn Caption="P. Prs. JKK" FieldName="Pt_P_JKK" ShowInCustomizationForm="True" VisibleIndex="0">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Prs. JKM" FieldName="Pt_P_JKM" ShowInCustomizationForm="True" VisibleIndex="1">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Prs. JHT" FieldName="Pt_P_JHT" ShowInCustomizationForm="True" VisibleIndex="2">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Prs. JPK" FieldName="Pt_P_JPK" ShowInCustomizationForm="True" VisibleIndex="3">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Prs. JP" FieldName="Pt_P_JP" ShowInCustomizationForm="True" VisibleIndex="4">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Kar. JHT" FieldName="Pt_K_JHT" ShowInCustomizationForm="True" VisibleIndex="5">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Kar. JPK" FieldName="Pt_K_JPK" ShowInCustomizationForm="True" VisibleIndex="6">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Kar. JP" FieldName="Pt_K_JP" ShowInCustomizationForm="True" VisibleIndex="7">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. PPH 21" VisibleIndex="8" FieldName="Pt_PPH21" ShowInCustomizationForm="True">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. SP" VisibleIndex="9" FieldName="Pt_SP" ShowInCustomizationForm="True">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="P. Kasbon" VisibleIndex="10" FieldName="Pt_Kasbon" ShowInCustomizationForm="True">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="TotalPotongan" ShowInCustomizationForm="True" VisibleIndex="13">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                                <HeaderStyle Font-Bold="True" ForeColor="Red" />
                                                                <CellStyle Font-Bold="True" ForeColor="Red">
                                                                </CellStyle>
                                                                <FooterCellStyle Font-Bold="True" ForeColor="Red">
                                                                </FooterCellStyle>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="Pt_SerPekerja" ShowInCustomizationForm="True" Caption="P._Ser. Pekerja" VisibleIndex="11">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2"></PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="Pot_Alpha" ShowInCustomizationForm="True" Caption="Pot. Alpha" VisibleIndex="12">
                                                                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                                                                </PropertiesTextEdit>
                                                            </dx:GridViewDataTextColumn>
                                                        </Columns>
                                                    </dx:GridViewBandColumn>
                                                    <dx:GridViewDataTextColumn FieldName="HariKerja" ShowInCustomizationForm="True" VisibleIndex="19"></dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="JumlahAlpha" ShowInCustomizationForm="True" Caption="J. Alpha" VisibleIndex="20">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="JumlahIjin" ShowInCustomizationForm="True" Caption="J. Ijin" VisibleIndex="21">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="JumlahIjinDispensasi" ShowInCustomizationForm="True" Caption="J. Ijin Dispensasi" VisibleIndex="22">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="JumlahCuti" ShowInCustomizationForm="True" Caption="J. Cuti" VisibleIndex="23">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn FieldName="JumlahPotongGaji" Caption="J. Pot. Gaji (Ijin)" VisibleIndex="24">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewBandColumn Caption="BANK ACCOUNT" ShowInCustomizationForm="True" VisibleIndex="25">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <Columns>
                                                            <dx:GridViewDataTextColumn Caption="No Rekening" FieldName="tm_karyawan.NoRekening" ShowInCustomizationForm="True" VisibleIndex="1">
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="Nama Bank" FieldName="tm_karyawan.tm_namabank.KodeBank" ShowInCustomizationForm="True" VisibleIndex="0">
                                                            </dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Caption="Nama Akun" FieldName="tm_karyawan.NamaAccount" ShowInCustomizationForm="True" VisibleIndex="2">
                                                            </dx:GridViewDataTextColumn>
                                                        </Columns>
                                                    </dx:GridViewBandColumn>
                                                </Columns>
                                                <Toolbars>
                                                    <dx:GridViewToolbar>
                                                        <Items>
                                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                                                            </dx:GridViewToolbarItem>
                                                            <dx:GridViewToolbarItem Command="ShowFilterRow">
                                                            </dx:GridViewToolbarItem>
                                                            <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                                                            </dx:GridViewToolbarItem>
                                                        </Items>
                                                    </dx:GridViewToolbar>
                                                </Toolbars>
                                                <TotalSummary>
                                                    <dx:ASPxSummaryItem DisplayFormat="{0:n2}" FieldName="TotalPendapatan" ShowInColumn="Total Pendapatan" SummaryType="Sum" />
                                                    <dx:ASPxSummaryItem DisplayFormat="{0:n2}" FieldName="TotalPotongan" ShowInColumn="Total Potongan" SummaryType="Sum" />
                                                    <dx:ASPxSummaryItem DisplayFormat="{0:n2}" FieldName="THP" ShowInColumn="THP" SummaryType="Sum" />
                                                </TotalSummary>
                                            </dx:ASPxGridView>
                                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_gaji_line" />
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E13" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E14" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E15" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E16" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E18" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E19" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="NoVoucher">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E20" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>
        <dx:LayoutGroup Caption="Action" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Proses" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_area.PerformCallback('save');
		s.SetEnabled(false);
	};

}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Back" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_area.PerformCallback('back');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>

    

