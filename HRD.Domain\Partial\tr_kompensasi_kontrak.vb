﻿Partial Public Class tr_kompensasi_kontrak
    Public Shared Function Create(TEntity As tr_kompensasi_kontrak) As tr_kompensasi_kontrak
        Dim o As New tr_kompensasi_kontrak With {
            .Id = TEntity.Id,
            .NoKompensasi = TEntity.NoKompensasi,
            .Periode = TEntity.Periode,
            .PeriodeDate = TEntity.PeriodeDate,
            .Status = TEntity.Status,
            .CreatedBy = TEntity.CreatedBy,
            .CreatedDate = TEntity.CreatedDate,
            .Area_id = TEntity.Area_id,
            .Cabang_id = TEntity.Cabang_id,
            .Keterangan = TEntity.Keterangan,
            .ModifiedBy = TEntity.ModifiedBy,
            .ModifiedDate = TEntity.ModifiedDate,
            .Posted = TEntity.Posted,
            .PostedBy = TEntity.PostedBy,
            .PostedDate = TEntity.PostedDate,
            .Tanggal = TEntity.Tanggal,
            .Total = TEntity.Total
        }
        Return o
    End Function

End Class
