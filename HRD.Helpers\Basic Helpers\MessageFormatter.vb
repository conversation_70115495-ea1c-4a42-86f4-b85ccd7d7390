﻿Public Class MessageFormatter
    Public Shared Function GetFormattedSuccessMessage(ByVal message As String) As String
        Return GetFormattedMessage(message, MessageType.Success)
    End Function

    Public Shared Function GetFormattedErrorMessage(ByVal message As String) As String
        Return GetFormattedMessage(message, MessageType.Error)
    End Function

    Public Shared Function GetFormattedNoticeMessage(ByVal message As String) As String
        Return GetFormattedMessage(message, MessageType.Notice)
    End Function

    Public Shared Function GetFormattedMessage(ByVal message As String, Optional ByVal messageType As MessageType = MessageType.Notice) As String
        Select Case messageType
            Case MessageType.Success
                Return "<div class='success'>" & message & "</div>"
            Case MessageType.Error
                Return "<div class='error'>" & message & "</div>"
            Case Else
                Return "<div class='notice'>" & message & "</div>"
        End Select
    End Function

End Class
Public Enum MessageType
    Success
    [Error]
    Notice
End Enum

