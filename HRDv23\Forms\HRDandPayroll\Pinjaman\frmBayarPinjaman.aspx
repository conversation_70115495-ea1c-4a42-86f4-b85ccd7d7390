﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmBayarPinjaman.aspx.vb" Inherits="HRDv23.frmBayarPinjaman" %>

<%@ Register Src="~/Views/HRDandPayroll/Pinjaman/ucBayarPinjaman_list.ascx" TagPrefix="uc1" TagName="ucBayarPinjaman_list" %>
<%@ Register Src="~/Views/HRDandPayroll/Pinjaman/ucBayarPinjaman_edit.ascx" TagPrefix="uc1" TagName="ucBayarPinjaman_edit" %>


<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_bayar_pinjaman" Width="100%">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Caption="List" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucBayarPinjaman_list runat="server" id="ucBayarPinjaman_list" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Edit" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucBayarPinjaman_edit runat="server" id="ucBayarPinjaman_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>
