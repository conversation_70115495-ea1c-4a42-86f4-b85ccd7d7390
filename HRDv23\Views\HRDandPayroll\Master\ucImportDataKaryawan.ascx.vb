﻿Imports DevExpress.Web
Imports HRD.Helpers
Imports HRD.Controller
Imports ILS.MVVM
Imports HRD.Domain
Public Class ucImportDataKaryawan
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IKaryawanListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
        Else
            Me.ASPxGridView1.DataBind()
        End If
    End Sub

    Private Sub ASPxUploadControl1_FileUploadComplete(sender As Object, e As FileUploadCompleteEventArgs) Handles ASPxUploadControl1.FileUploadComplete
        Dim fileName As String = e.UploadedFile.FileName
        Dim fileExtension As String = System.IO.Path.GetExtension(fileName)
        Dim savePath As String = Server.MapPath("~/Content/Uploads/")
        Dim fileSavePath As String = savePath & fileName
        If Not System.IO.Directory.Exists(savePath) Then
            System.IO.Directory.CreateDirectory(savePath)
        End If
        e.UploadedFile.SaveAs(fileSavePath)
        Dim dt As DataTable = MyMethod.ReadExcelFile(fileSavePath)
        Session("_data") = dt
    End Sub
    Private Sub ASPxGridView1_CustomCallback(sender As Object, e As ASPxGridViewCustomCallbackEventArgs) Handles ASPxGridView1.CustomCallback
        Dim sMessage As New HashSet(Of String)()
        Dim i As Integer = 0

        Try
            If String.IsNullOrEmpty(e.Parameters) Then
                Return
            End If

            Controller.DataListImport.Clear()



            If e.Parameters = "import" Then
                Dim dt = Session("_data")

                Dim bNew As Boolean = False

                Dim _user = AuthHelper.GetLoggedInUserInfo

                For Each dr As DataRow In dt.Rows
                    If i = 0 Then
                        i = i + 1
                        Continue For
                    End If
                    i = i + 1
                    If i = 228 Then
                        Dim test As String
                        test = ""
                    End If
                    If Controller.CekExistKaryawans(dr(0), dr(1), dr(2)) Then
                        Continue For
                    End If

                    Dim o As New tm_karyawan
                    Dim area = myFunction.tm_areaByKode(dr(0).ToString().Trim)
                    Dim cabang = myFunction.tm_cabangByKode(dr(1).ToString().Trim, area.Id)
                    Dim jk = myFunction.tm_jenis_kelaminByNama(dr(6).ToString().Trim)
                    Dim agama = myFunction.tm_agamaByNama(dr(7).ToString().Trim)
                    Dim statusNikah = myFunction.tm_status_perkawinanByKode(dr(11).ToString().Trim)
                    If statusNikah Is Nothing Then
                        sMessage.Add($"Kode Status Nikah : {dr(11).ToString()} Belum ada di master")
                    End If
                    Dim pendidikan = myFunction.tm_pendidikanByKode(dr(12).ToString().Trim)
                    If pendidikan Is Nothing Then
                        'sMessage.Add($"Kode Pendidikan : {dr(12).ToString()} Belum ada di master")
                    End If
                    Dim jurusan = myFunction.tm_jurusanByKode(dr(13).ToString().Trim)
                    If jurusan Is Nothing Then
                        'sMessage.Add($"Kode Jurusan : {dr(13).ToString()} Belum ada di master")
                    End If
                    Dim statusNikahPTKP = myFunction.tm_status_PTKP_byCode(dr(17).ToString().Trim)
                    If statusNikahPTKP Is Nothing Then
                        sMessage.Add($"Kode Status Nikah PTKP : {dr(17).ToString()} Belum ada di master")
                    End If
                    Dim namaBank = myFunction.tm_namabankByKode(dr(19).ToString().Trim)
                    If namaBank Is Nothing Then
                        sMessage.Add($"Kode Bank : {dr(19).ToString()} Belum ada di master")
                    End If
                    Dim department = myFunction.tm_departmentByNama(dr(24).ToString().Trim)
                    If department Is Nothing Then
                        sMessage.Add($"Department : {dr(24).ToString()} Belum ada di master")
                    End If
                    Dim jabatan = myFunction.tm_jabatanByNama(dr(25).ToString().Trim)
                    If jabatan Is Nothing Then
                        sMessage.Add($"Jabatan : {dr(25).ToString()} Belum ada di master")
                    End If
                    With o
                        .Area_id = area.Id
                        .tm_area = area
                        .Cabang_id = cabang.Id
                        .tm_cabang = cabang
                        .NIK = dr(2).ToString()
                        .Nama = dr(3).ToString()
                        .Tempat = dr(4).ToString()
                        .TglLahir = dr(5).ToString()
                        .JenisKelamin_id = jk.Id
                        .tm_jenis_kelamin = jk
                        If dr(7).ToString <> "" Then
                            .Agama_id = agama.id
                            .tm_agama = agama
                        End If

                        .Alamat = dr(8).ToString()
                        .Kota = dr(9).ToString()
                        .Telp = dr(10).ToString()
                        .StatusPerkawinan_id = statusNikah.Id
                        .tm_status_perkawinan = statusNikah
                        If pendidikan IsNot Nothing Then
                            .Pendidikan_id = pendidikan.Id
                            .tm_pendidikan = pendidikan
                        End If
                        If jurusan IsNot Nothing Then
                            .Jurusan_id = jurusan.id
                            .tm_jurusan = jurusan
                        End If
                        .Alumni = dr(14).ToString()
                        .TahunLulus = dr(15).ToString()
                        If Not String.IsNullOrEmpty(dr(16).ToString()) Then
                            .TglMasuk = dr(16).ToString()
                        End If

                        .StatusPerkawinan_NonPTKP_id = statusNikahPTKP.Id
                        .tm_status_PTKP = statusNikahPTKP
                        .Email = dr(18).ToString()
                        If namaBank IsNot Nothing Then
                            .NamaBank_id = namaBank.Id
                            .tm_namabank = namaBank
                        End If
                        .NoRekening = dr(20).ToString()
                        .NamaAccount = dr(21).ToString()
                        .NoKTP = dr(22).ToString()
                        .NPWP = dr(23).ToString()

                        .IsBPJS_Kes = True
                        .IsBPJS_TK = True

                        .Createdby = _user.UserName
                        .CreatedDate = DateTime.Now

                        Dim dn As New tm_karyawan_datadinas

                        dn.Department_id = department.Id
                        dn.tm_department = department
                        dn.Jabatan_id = jabatan.Id
                        dn.tm_jabatan = jabatan

                        dn.Tetap = dr(26).ToString

                        If dn.Tetap Then
                            .KaryawanTetap = True
                            If dr(27).ToString <> "" Then
                                dn.TglTetap = dr(27).ToString
                            End If
                        Else
                            dn.NoKontrakKerja = dr(28).ToString
                            If dr(29).ToString <> "" Then
                                dn.TglAwalKontrak = dr(29).ToString
                            End If
                            If dr(30).ToString <> "" Then
                                dn.TglAkhirKontrak = dr(30).ToString
                            End If
                        End If

                        o.tm_karyawan_datadinas.Add(dn)



                    End With

                    Controller.DataListImport.Add(o)
                Next

                Me.ASPxGridView1.DataBind()
                If sMessage.Count > 0 Then
                    Me.ASPxGridView1.JSProperties("cpMsg") = String.Join(", ", sMessage)
                End If
            End If
        Catch ex As Exception
            ' Menangani kesalahan dan menampilkan pesan error
            sMessage.Add($"Row {i} Error: {ex.Message}")
            Me.ASPxGridView1.JSProperties("cpMsg") = String.Join(", ", sMessage)
        End Try

    End Sub

    Private Sub ASPxGridView1_DataBinding(sender As Object, e As EventArgs) Handles ASPxGridView1.DataBinding
        If Controller Is Nothing Then
            Return
        End If
        Dim grd = CType(sender, ASPxGridView)

        grd.DataSource = Controller.DataListImport
    End Sub
End Class