﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucJadwalStore_edit.ascx.vb" Inherits="HRDv23.ucJadwalStore_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Perusahaan" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" ClientInstanceName="cb_area">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	//cb_karyawan.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="TglBerlaku">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E4" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Keterangan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxMemo ID="ASPxFormLayout1_E12" runat="server">
                                    </dx:ASPxMemo>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutGroup Caption="Daftar Jadwal Karyawan Store" ColSpan="2" ColumnSpan="2">
                            <Items>
                                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                                    <LayoutItemNestedControlCollection>
                                        <dx:LayoutItemNestedControlContainer runat="server">
                                            <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id;RowGuid">
                                                <ClientSideEvents EndCallback="function(s, e) {
	cb_area.SetEnabled(!s.cpCount);
	cb_cabang.SetEnabled(!s.cpCount);
}" />
                                                <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
                                                </SettingsAdaptivity>
                                                <SettingsEditing EditFormColumnCount="1" Mode="PopupEditForm">
                                                </SettingsEditing>
                                                <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
                                                <SettingsPopup>
                                                    <EditForm AllowResize="True" HorizontalAlign="WindowCenter" Modal="True" VerticalAlign="WindowCenter">
                                                        <SettingsAdaptivity Mode="OnWindowInnerWidth" VerticalAlign="WindowCenter" />
                                                    </EditForm>
                                                    <FilterControl AutoUpdatePosition="False">
                                                    </FilterControl>
                                                </SettingsPopup>
                                                <Columns>
                                                    <dx:GridViewCommandColumn ShowDeleteButton="True" ShowEditButton="True" ShowInCustomizationForm="True" ShowNewButtonInHeader="True" VisibleIndex="0" ShowClearFilterButton="True">
                                                    </dx:GridViewCommandColumn>
                                                    <dx:GridViewDataComboBoxColumn Caption="Karyawan" FieldName="Karyawan_id" ShowInCustomizationForm="True" VisibleIndex="1">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_karyawan_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_karyawan_ItemsRequestedByFilterCondition" >
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="NIK">
                                                                </dx:ListBoxColumn>
                                                                <dx:ListBoxColumn FieldName="Nama">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Minggu" ShowInCustomizationForm="True" VisibleIndex="9">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Senin" ShowInCustomizationForm="True" VisibleIndex="2">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Selasa" ShowInCustomizationForm="True" VisibleIndex="3">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Rabu" ShowInCustomizationForm="True" VisibleIndex="4">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Kamis" ShowInCustomizationForm="True" VisibleIndex="5">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Jumat" ShowInCustomizationForm="True" VisibleIndex="7">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                    <dx:GridViewDataComboBoxColumn FieldName="Sabtu" ShowInCustomizationForm="True" VisibleIndex="8">
                                                        <PropertiesComboBox CallbackPageSize="10" EnableCallbackMode="True" TextFormatString="{0}" ValueField="Id" ValueType="System.Int32" OnItemRequestedByValue="cb_minggu_ItemRequestedByValue" OnItemsRequestedByFilterCondition="cb_minggu_ItemsRequestedByFilterCondition">
                                                            <Columns>
                                                                <dx:ListBoxColumn FieldName="Deskripsi">
                                                                </dx:ListBoxColumn>
                                                            </Columns>
                                                            <ValidationSettings Display="Dynamic" SetFocusOnError="True">
                                                                <RequiredField ErrorText="Required" IsRequired="True" />
                                                            </ValidationSettings>
                                                        </PropertiesComboBox>
                                                    </dx:GridViewDataComboBoxColumn>
                                                </Columns>
                                            </dx:ASPxGridView>
                                            <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tm_off_karyawan_line" />
                                        </dx:LayoutItemNestedControlContainer>
                                    </LayoutItemNestedControlCollection>
                                </dx:LayoutItem>
                            </Items>
                        </dx:LayoutGroup>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="Createdby">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E6" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E7" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E8" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E9" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E13" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="PostedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E14" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>
        <dx:LayoutGroup Caption="Action" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Save" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_area.PerformCallback('save');
		s.SetEnabled(false);
	};

}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Back" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_area.PerformCallback('back');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>



