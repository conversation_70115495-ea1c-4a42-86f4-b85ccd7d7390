﻿Imports HRD.Application
Imports HRD.Domain

Public Class KaryawanService
    Implements IKaryawanService

#Region "Properties"
    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog
    Private ReadOnly _myFunction As IMyFunction
#End Region

#Region "Ctor"
    Public Sub New(ByVal unitOfWork As IUnitOfWork, ByVal errorMessageLog As IErrorMessageLog, ByVal myFunction As IMyFunction)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
        _myFunction = myFunction
    End Sub
#End Region

#Region "Error"
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Department Service", method, msg)
    End Sub
#End Region


    Public Async Function GetKaryawansAsync() As Task(Of ResponseModel) Implements IKaryawanService.GetKaryawansAsync
        Try
            Dim karyawans = _unitOfWork.Repository(Of tm_karyawan)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, karyawans)
        Catch ex As Exception
            Log(NameOf(Me.GetKaryawansAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetKaryawanByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IKaryawanService.GetKaryawanByIdAsync
        Try
            Dim kar = Await _unitOfWork.Repository(Of tm_karyawan)().Get(Id)

            If kar IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, kar)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetKaryawanByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_karyawan) As Task(Of ResponseModel) Implements IKaryawanService.UpsertAsync
        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tm_karyawan)().Update(o)
            Else
                If String.IsNullOrEmpty(o.NIK) Then
                    o.NIK = _myFunction.GetKodeNIK(o)
                End If

                Await _unitOfWork.Repository(Of tm_karyawan)().AddAsync(o)

            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IKaryawanService.DeleteAsync
        Try
            Dim kar = Await _unitOfWork.Repository(Of tm_karyawan)().Get(Id)
            If kar IsNot Nothing Then
                Await _unitOfWork.Repository(Of tm_karyawan).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function ImportAsync(dataListImport As IList(Of tm_karyawan)) As Task(Of ResponseModel)
        Try
            For Each x In dataListImport
                x.tm_agama = Nothing
                x.tm_area = Nothing
                x.tm_cabang = Nothing
                x.tm_jenis_kelamin = Nothing
                x.tm_jurusan = Nothing
                x.tm_namabank = Nothing
                x.tm_off_karyawan_line = Nothing
                x.tm_pendidikan = Nothing
                x.tm_status_perkawinan = Nothing
                x.tm_status_PTKP = Nothing
                For Each y In x.tm_karyawan_datadinas
                    y.Tanggal = Now
                    y.Area_id = x.Area_id
                    y.Cabang_id = x.Cabang_id
                    y.tm_area = Nothing
                    y.tm_cabang = Nothing
                    y.tm_department = Nothing
                    y.tm_jabatan = Nothing
                Next
            Next
            _unitOfWork.Repository(Of tm_karyawan)().AddRange(dataListImport)
            _unitOfWork.Save()
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
        Catch ex As Exception
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
    'Private Function GetKodeNIK(o As tm_karyawan) As String
    '    Dim area = _myFunction.tm_area(o.Area_id)
    '    Dim kode As String = If(o.KaryawanTetap, area.KodeNIK_Tetap, area.KodeNIK_Kontrak)
    '    Dim sFormat As String = Format(o.TglMasuk, "MMyy")
    '    Dim k = _unitOfWork.Repository(Of tm_karyawan).TableNoTracking.Where(Function(f) f.NIK.Substring(0, kode.Length) = kode And f.NIK.Substring(f.NIK.Length - 4) = sFormat).OrderByDescending(Function(od) od.NIK).Take(1).FirstOrDefault

    '    Dim i As Integer = 0
    '    If k IsNot Nothing Then
    '        Dim lastDigits = Integer.Parse(k.NIK.Substring(kode.Length, 3))
    '        i = lastDigits
    '    End If

    '    i += 1
    '    Return $"{kode}{i.ToString("000")}{sFormat}"
    'End Function


End Class
