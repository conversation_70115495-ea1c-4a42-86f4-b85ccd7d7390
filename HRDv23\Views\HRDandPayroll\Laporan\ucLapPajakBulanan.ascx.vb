﻿Imports DevExpress.Web
Imports HRD.Helpers
Imports HRD.Reports
Public Class ucLapPajakBulanan
    Inherits System.Web.UI.UserControl

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            txt_tahun.Value = Now



            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = AuthHelper.GetLoggedInUserInfo.Cabang_id
        End If
    End Sub
    Sub ShowRPT()
        Dim cls As New clsRPT
        Dim rpt = cls.GetRpt_LapPajakBulanan(cb_cabang.Value, txt_tahun.Value)
        Me.ASPxWebDocumentViewer1.OpenReport(rpt)
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As DevExpress.Web.ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub


End Class