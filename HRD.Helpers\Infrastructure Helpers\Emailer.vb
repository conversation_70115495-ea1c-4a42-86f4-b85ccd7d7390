﻿Imports System.Text
Imports System.Net.Mail
Imports System.IO


Public NotInheritable Class Emailer
    Private Sub New()
    End Sub

    Public Shared Sub SendMail(ByVal subject As String, ByVal [to] As String, Optional ByVal from As String = Nothing, Optional ByVal body As String = Nothing, Optional ByVal attachment As Stream = Nothing, Optional ByVal port As Integer = 25, Optional ByVal host As String = "localhost", Optional ByVal isBodyHtml As Boolean = True)
        Dim mailMsg As New MailMessage()
        mailMsg.From = New MailAddress(from)
        mailMsg.To.Add([to])
        mailMsg.Subject = subject
        mailMsg.IsBodyHtml = isBodyHtml
        mailMsg.BodyEncoding = Encoding.UTF8
        mailMsg.Body = body
        mailMsg.Priority = MailPriority.Normal

        'Message attahment
        If attachment IsNot Nothing Then
            mailMsg.Attachments.Add(New Attachment(attachment, "my.text"))
        End If

        ' Smtp configuration
        Dim client As New SmtpClient()
        'client.Credentials = new NetworkCredential("test", "test");
        client.UseDefaultCredentials = True
        client.DeliveryMethod = SmtpDeliveryMethod.Network
        client.Port = port 'use 465 or 587 for gmail
        client.Host = host 'for gmail "smtp.gmail.com";
        client.EnableSsl = False

        Dim message As MailMessage = mailMsg

        client.Send(message)

    End Sub

End Class
