﻿Imports DevExpress.Web
Imports HRD.Helpers
Imports HRD.Controller
Imports ILS.MVVM
Imports HRD.Domain
Public Class ucImportDataKontrak
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IDataDinasListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
        Else
            Me.ASPxGridView1.DataBind()
        End If
    End Sub

    Private Sub ASPxUploadControl1_FileUploadComplete(sender As Object, e As FileUploadCompleteEventArgs) Handles ASPxUploadControl1.FileUploadComplete
        Dim fileName As String = e.UploadedFile.FileName
        Dim fileExtension As String = System.IO.Path.GetExtension(fileName)
        Dim savePath As String = Server.MapPath("~/Content/Uploads/")
        Dim fileSavePath As String = savePath & fileName
        If Not System.IO.Directory.Exists(savePath) Then
            System.IO.Directory.CreateDirectory(savePath)
        End If
        e.UploadedFile.SaveAs(fileSavePath)
        Dim dt As DataTable = MyMethod.ReadExcelFile(fileSavePath)
        Session("_data") = dt
    End Sub
    Private Sub ASPxGridView1_CustomCallback(sender As Object, e As ASPxGridViewCustomCallbackEventArgs) Handles ASPxGridView1.CustomCallback
        If String.IsNullOrEmpty(e.Parameters) Then
            Return
        End If

        Dim _user = AuthHelper.GetLoggedInUserInfo

        Controller.DataListImport.Clear()

        Dim sMessage As New HashSet(Of String)()

        If e.Parameters = "import" Then
            Dim dt = Session("_data")
            Dim i As Integer = 0
            Dim bNew As Boolean = False

            For Each dr As DataRow In dt.Rows
                If i = 0 Then
                    i = i + 1
                    Continue For
                End If

                If myFunction.CekExistKaryawans(dr(0)) = False Then
                    Continue For
                End If


                'Dim area = myFunction.tm_areaByKode(dr(0).ToString())
                'Dim cabang = myFunction.tm_cabangByKode(dr(1).ToString())
                'Dim karyawan = myFunction.tm_karyawanByNIK(dr(2).ToString())
                'Dim department = myFunction.tm_departmentByNama(dr(4).ToString())
                'Dim jabatan = myFunction.tm_jabatanByNama(dr(5).ToString())

                Dim o = Controller.GetDataDinasByNIK(dr(0))

                If o Is Nothing Then
                    Continue For
                End If
                With o
                    '.Area_id = o.Area_id
                    '.tm_area = myFunction.tm_area(.Area_id)
                    '.Cabang_id = o.Cabang_id
                    '.tm_cabang = myFunction.tm_cabang(.Cabang_id)
                    ''.Tanggal = dr(2).ToString
                    '.Karyawan_id = o.Karyawan_id
                    '.tm_karyawan = karyawan
                    '.Department_id = department.Id
                    '.tm_department = department
                    '.Jabatan_id = jabatan.Id
                    '.tm_jabatan = jabatan
                    '.Tetap = dr(7).ToString
                    'If .Tetap Then
                    '    If dr(8).ToString <> "" Then
                    '        .TglTetap = dr(8).ToString
                    '    End If
                    'Else
                    .NoKontrakKerja = dr(2).ToString
                    If dr(4).ToString <> "" Then
                        .TglAwalKontrak = dr(4).ToString
                    Else
                        sMessage.Add($"{ .tm_karyawan.NIK} - { .tm_karyawan.Nama} : Tgl Awal Kontrak Kosong")
                    End If
                    If dr(5).ToString <> "" Then
                        .TglAkhirKontrak = dr(5).ToString
                    Else
                        sMessage.Add($"{ .tm_karyawan.NIK} - { .tm_karyawan.Nama} : Tgl Akhir Kontrak Kosong")
                    End If
                    .Tanggal = Now
                    'End If
                    'If dr(4).ToString <> "" Then
                    '    .Pd_GajiPokok = dr(4).ToString
                    'End If
                    'If dr(5).ToString <> "" Then
                    '    .Pd_T_Jabatan = dr(5).ToString
                    'End If
                    'If dr(6).ToString <> "" Then
                    '    .Pd_T_Makan = dr(6).ToString
                    'End If
                    'If dr(7).ToString <> "" Then
                    '    .Pd_T_Transport = dr(7).ToString
                    'End If
                    'If dr(8).ToString <> "" Then
                    '    .Pd_T_Kontrak = dr(8).ToString
                    'End If
                    'If dr(9).ToString <> "" Then
                    '    .Pd_T_Susu = dr(9).ToString
                    'End If
                    'If dr(10).ToString <> "" Then
                    '    .Pd_T_Probation = dr(10).ToString
                    'End If
                    'If dr(11).ToString <> "" Then
                    '    .Pd_T_PremiAss = dr(11).ToString
                    'End If
                    'If dr(12).ToString <> "" Then
                    '    .Pd_T_PremiHadir = dr(12).ToString
                    'End If
                    'Dim calAss = myFunction.CalculateAssuransi(.Pd_GajiPokok, .Karyawan_id, .Cabang_id)
                    '.Pd_T_JKK = calAss.Pd_JKK
                    '.Pd_T_JKM = calAss.Pd_JKM
                    '.Pd_T_JHT = calAss.Pd_JHT
                    '.Pd_T_JPK = calAss.Pd_JPK
                    '.Pd_T_JP = calAss.Pd_JP

                    '.TotalPendapatan = .Pd_GajiPokok + .Pd_T_Jabatan + .Pd_T_Makan + .Pd_T_Transport + .Pd_T_Kontrak + .Pd_T_Susu + .Pd_T_Probation + .Pd_T_PremiAss + .Pd_T_PremiHadir + .Pd_T_JKK + .Pd_T_JKM + .Pd_T_JHT + .Pd_T_JPK + .Pd_T_JP

                    '.Pt_P_JKK = calAss.Pt_P_JKK
                    '.Pt_P_JKM = calAss.Pt_P_JKM
                    '.Pt_P_JHT = calAss.Pt_P_JHT
                    '.Pt_P_JPK = calAss.Pt_P_JPK
                    '.Pt_P_JP = calAss.Pt_P_JP

                    '.Pt_K_JHT = calAss.Pt_K_JHT
                    '.Pt_K_JPK = calAss.Pt_K_JPK
                    '.Pt_K_JP = calAss.Pt_K_JP

                    'If dr(13).ToString <> "" Then
                    '    .Pt_SP = dr(13).ToString
                    'End If
                    'If dr(14).ToString <> "" Then
                    '    .Pt_SerPekerja = dr(14).ToString
                    'End If

                    '.TotalPotongan = .Pt_P_JKK + .Pt_P_JKM + .Pt_P_JHT + .Pt_P_JPK + .Pt_P_JP + .Pt_K_JHT + .Pt_K_JPK + .Pt_K_JP + .Pt_SP + .Pt_SerPekerja

                    '.THP = .TotalPendapatan - .TotalPotongan

                    .CreatedBy = _user.UserName
                    .CreatedDate = DateTime.Now
                End With

                Controller.DataListImport.Add(o)
            Next

            Me.ASPxGridView1.DataBind()
            If sMessage.Count > 0 Then
                Me.ASPxGridView1.JSProperties("cpMsg") = MessageFormatter.GetFormattedErrorMessage(String.Join("</BR>", sMessage))
            End If
        End If
    End Sub

    Private Sub ASPxGridView1_DataBinding(sender As Object, e As EventArgs) Handles ASPxGridView1.DataBinding
        If Controller Is Nothing Then
            Return
        End If
        Dim grd = CType(sender, ASPxGridView)

        grd.DataSource = Controller.DataListImport
    End Sub
End Class