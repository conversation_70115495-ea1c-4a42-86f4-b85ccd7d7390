﻿Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Imports HRD.Application
Imports HRD.Controller

Public Class KompensasiKontrakController
    Inherits Controller(Of tr_kompensasi_kontrak)
    Implements IKompensasiListKontrakController, IKompensasiEditKontrakController

    Public ReadOnly Property DataList(area_id As String, posted As Boolean) As IQueryable(Of tr_kompensasi_kontrak) Implements IKompensasiListKontrakController.DataList
        Get
            Dim serv = ObjectFactory.GetInstance(Of KompensasiKontrakService)
            Dim r = serv.GetQueryableAsync().GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_kompensasi_kontrak) = r.Output
                os = os.Where(Function(f) f.Area_id = area_id AndAlso f.Posted = posted)
                Return os
            Else
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property DataListKompensiKontrakLine(kompensasi_id As String) As IQueryable(Of tr_kompensasi_kontrak_line) Implements IKompensasiEditKontrakController.DataListKompensiKontrakLine
        Get
            Throw New NotImplementedException()
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Dim _user = AuthHelper.GetLoggedInUserInfo
        Action = Action.AddNew
        SelectedItem = New tr_kompensasi_kontrak With {.Area_id = _user.Area_id, .Tanggal = Now, .PeriodeDate = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim serv = ObjectFactory.GetInstance(Of KompensasiKontrakService)

        Dim r = serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If

    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Try
            Dim serv = ObjectFactory.GetInstance(Of KompensasiKontrakService)

            Dim r = serv.DeleteAsync(id).GetAwaiter.GetResult
            If r.Success Then
                sMsg = MessageFormatter.GetFormattedSuccessMessage(r.Message)
                Reset()
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            End If
        Catch ex As Exception
            sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
        End Try

    End Sub

    Public Overrides Sub Saving()
        Throw New NotImplementedException()
    End Sub

    Public Overrides Sub Saving(TEntity As tr_kompensasi_kontrak)
        Dim serv = ObjectFactory.GetInstance(Of KompensasiKontrakService)
        If TEntity.Id <= 0 Then
            TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        Else
            TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        End If
        Dim r = serv.UpsertAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True

        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If

    End Sub

    Public Sub Posting(TEntity As tr_kompensasi_kontrak) Implements IKompensasiEditKontrakController.Posting
        Dim serv = ObjectFactory.GetInstance(Of KompensasiKontrakService)

        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        TEntity.PostedDate = Now

        Dim r = serv.PostingAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            Saved = True

        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
            Saved = False
        End If
    End Sub
End Class
Public Interface IKompensasiListKontrakController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(area_id As String, posted As Boolean) As IQueryable(Of tr_kompensasi_kontrak)
End Interface
Public Interface IKompensasiEditKontrakController
    Inherits IControllerMain, IControllerEdit(Of tr_kompensasi_kontrak)

    ReadOnly Property DataListKompensiKontrakLine(kompensasi_id As String) As IQueryable(Of tr_kompensasi_kontrak_line)
    Sub Posting(oSelectItem As tr_kompensasi_kontrak)
End Interface