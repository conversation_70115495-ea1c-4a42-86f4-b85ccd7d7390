﻿<?xml version="1.0" encoding="utf-8" ?>
<siteMap xmlns="http://schemas.microsoft.com/AspNet/SiteMap-File-1.0" >
  <siteMapNode url="~/Default.aspx" title="HOME"  description="">
    <siteMapNode title="Setting">
      <siteMapNode title="Role" url="Setting/Role"></siteMapNode>
      <siteMapNode title="Perusahaan" url="Setting/Area"></siteMapNode>
      <siteMapNode title="Cabang" url="Setting/Cabang"></siteMapNode>
      <siteMapNode title="User" url="Setting/User"></siteMapNode>
    </siteMapNode>
    <siteMapNode title="Profil">
      <siteMapNode title="Ganti Password" url="GantiPassword"></siteMapNode>
    </siteMapNode>
    <siteMapNode title="Hrd and Payroll">
      <siteMapNode title="Master">
        <siteMapNode title="Pendidikan" url="HRD/Master/Pendidikan"></siteMapNode>
        <siteMapNode title="Department" url="HRD/Master/Department"></siteMapNode>
        <siteMapNode title="Jabatan" url="HRD/Master/Jabatan"></siteMapNode>
        <siteMapNode title="Karyawan" url="HRD/Master/Karyawan"></siteMapNode>
        <siteMapNode title="Setting Absen" url="HRD/Master/SettingAbsen"></siteMapNode>
        <siteMapNode title="Status Perkawinan" url="HRD/Master/StatusPerkawinan"></siteMapNode>
        <siteMapNode title="Hari Libur Nasional" url="HRD/Master/HLibur"></siteMapNode>
        <siteMapNode title="Jadwal Store" url="HRD/Master/JadwalStore"></siteMapNode>
        <siteMapNode title="Jadwal Store Release" url="HRD/Master/JadwalStore/Release"></siteMapNode>
        <siteMapNode title="Salary Entry" url="HRD/Master/SalaryEntry"></siteMapNode>
        <siteMapNode title="Tunjangan Tambahan (One Time)" url="HRD/Master/TunjanganOneTime"></siteMapNode>
        <siteMapNode title="Saldo Awal Cuti" url="HRD/Master/SACuti"></siteMapNode>
        <siteMapNode title="Import Data Karyawan" url="HRD/Master/ImportDataKaryawan"></siteMapNode>
        <siteMapNode title="Import Data Dinas" url="HRD/Master/ImportDataDinas"></siteMapNode>
        <siteMapNode title="Event Setting" url="HRD/Master/EventSetting"></siteMapNode>
        <siteMapNode title="Release Event Setting" url="HRD/Master/EventSetting/Release"></siteMapNode>
        <siteMapNode title="Shift Absensi" url="HRD/Master/ShiftAbsensi"></siteMapNode>
        <siteMapNode title="Master Bonus THR" url="HRD/Master/BonusTHR"></siteMapNode>
        <siteMapNode title="Import Data Kontrak" url="HRD/Master/ImportKontrak"></siteMapNode>
        <siteMapNode title="Setting Absen Hari Khusus" url="HRD/Master/SettingAbsenHariKhusus"></siteMapNode>
      </siteMapNode>
      <siteMapNode title="Transaksi">
        <siteMapNode url="HRD/Transaksi/Absensi" title="Entry Absensi"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/Ijin" title="Entri Ijin"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/IjinRelease" title="Ijin Release"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/Gaji" title="Proses Gaji"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/Gaji/Release" title="Gaji Release"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/ImportAbsensi" title="Import Absensi"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/SPKLembur" title="SPL Lembur"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/SPKLembur/Release" title="SPK Lembur Release"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/KaryawanBerhenti" title="Karyawan Berhenti"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/THR" title="THR"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/THR/Release" title="THR Release"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/UbahJamMasuk" title="Ubah Jam Masuk"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/KompensasiKontrak" title="Kompensasi Kontrak"></siteMapNode>
        <siteMapNode url="HRD/Transaksi/KompensasiKontrak/Release" title="Release Kompensasi Kontrak"></siteMapNode>
      </siteMapNode>
      <siteMapNode title="Mutasi">
        <siteMapNode url="HRD/Mutasi/DataDinas" title="Mutasi Data Dinas"></siteMapNode>
      </siteMapNode>
      <siteMapNode title="Pinjaman">
        <siteMapNode title="Register Pinjaman" url="HRD/Pinjaman/RegPinjaman"></siteMapNode>
        <siteMapNode title="Register Pinjaman Release" url="HRD/Pinjaman/RegPinjaman/Release"></siteMapNode>
        <siteMapNode title="Bayar Pinjaman" url="HRD/Pinjaman/BayarPinjaman"></siteMapNode>
        <siteMapNode title="Release Bayar Pinjaman" url="HRD/Pinjaman/BayarPinjaman/Release"></siteMapNode>
      </siteMapNode>
      <siteMapNode title="Laporan">
        <siteMapNode title="Absensi">
          <siteMapNode title="Rekap Absensi" url="HRD/Laporan/Absensi/Rekap"></siteMapNode>
          <siteMapNode title="Absensi Perkaryawan" url="HRD/Laporan/Absensi/PerKaryawan"></siteMapNode>
          <siteMapNode title="Cuti Perkaryawan" url="HRD/Laporan/Absensi/CutiPerKaryawan"></siteMapNode>
          <siteMapNode title="Laporan Rekap Lembur" url="HRD/Laporan/Absensi/RekapLembur"></siteMapNode>
        </siteMapNode>
        <siteMapNode title="Gaji">
          <siteMapNode url="HRD/Laporan/Gaji/SlipGaji" title="Slip Gaji"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/RekapGajiBank" title="Rekap Gaji Ke Bank"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/RekapThrBank" title="Rekap THR Ke Bank"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/PajakPerKaryawan" title="Laporan Pajak Per Karyawan"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/PajakBulanan" title="Laporan Pajak Bulanan"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/PajakTahunan" title="Laporan Pajak Tahunan"></siteMapNode>
          <siteMapNode url="HRD/Laporan/Gaji/SlipThr" title="Slip THR"></siteMapNode>
        </siteMapNode>
        <siteMapNode title="Pinjaman">
          <siteMapNode url="HRD/Laporan/Pinjaman/Rekap" title="Rekap Pinjaman"></siteMapNode>
        </siteMapNode>
      </siteMapNode>
    </siteMapNode>
  </siteMapNode>
</siteMap>
