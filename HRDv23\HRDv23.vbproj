﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>
    </SchemaVersion>
    <ProjectGuid>{B253465F-E89C-4D81-A800-46B7C54C9DE1}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{F184B08F-C81C-45F6-A57F-5ABD9991F28F}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>HRDv23</RootNamespace>
    <AssemblyName>HRDv23</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MyType>Custom</MyType>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>HRDv23.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>HRDv23.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Dashboard.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Dashboard.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Dashboard.v23.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Map.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.ASPxTreeList.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v23.1.Presets, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v23.1.Win, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraMap.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraTreeList.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus">
      <HintPath>..\..\..\..\..\Sample\EPPlus-master\EPPlus-master\EPPlus\bin\Debug\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="ExpressMapper, Version=1.9.1.0, Culture=neutral, PublicKeyToken=ac363faa09311ba0, processorArchitecture=MSIL">
      <HintPath>..\packages\Expressmapper.1.9.1\lib\net46\ExpressMapper.dll</HintPath>
    </Reference>
    <Reference Include="ILS.MVVM">
      <HintPath>..\..\..\..\..\IndoPrimaTrans\SourceCode\ILS_1908\ILS\ILS.MVVM\bin\Release\ILS.MVVM.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.3\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.3\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.3\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.3.0.0\lib\netstandard2.0\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.0.0\lib\net451\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.0.0\lib\net451\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.0.0\lib\net451\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.0.0\lib\net451\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Facebook, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Facebook.4.0.0\lib\net451\Microsoft.Owin.Security.Facebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Google.4.0.0\lib\net451\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.MicrosoftAccount, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.MicrosoftAccount.4.0.0\lib\net451\Microsoft.Owin.Security.MicrosoftAccount.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.0.0\lib\net451\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Twitter, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Twitter.4.0.0\lib\net451\Microsoft.Owin.Security.Twitter.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.9.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\EBook\Professional-ASP.NET-Design-Patterns-master\Professional-ASP.NET-Design-Patterns-master\ASPPatterns.Chap8.MVP\Lib\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.ASPxThemes.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v23.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Printing.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraScheduler.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraScheduler.v23.1.Core.Desktop, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.ASPxScheduler.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v23.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Pdf.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.Resources.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Charts.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.CodeParser.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.PivotGrid.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Sparkline.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v23.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="Z.EntityFramework.Extensions, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Extensions.7.100.0.4\lib\net45\Z.EntityFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Z.EntityFramework.Plus.EF6, Version=7.100.0.4, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Plus.EF6.7.100.0.4\lib\net45\Z.EntityFramework.Plus.EF6.dll</HintPath>
    </Reference>
    <Reference Include="Z.Expressions.Eval, Version=5.0.11.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.Expressions.Eval.5.0.11\lib\net45\Z.Expressions.Eval.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Collections.Specialized" />
    <Import Include="System.Configuration" />
    <Import Include="System.Text" />
    <Import Include="System.Text.RegularExpressions" />
    <Import Include="System.Web" />
    <Import Include="System.Web.Caching" />
    <Import Include="System.Web.SessionState" />
    <Import Include="System.Web.Security" />
    <Import Include="System.Web.Profile" />
    <Import Include="System.Web.UI" />
    <Import Include="System.Web.UI.WebControls" />
    <Import Include="System.Web.UI.WebControls.WebParts" />
    <Import Include="System.Web.UI.HtmlControls" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Account\frmGantiPassword.aspx.designer.vb">
      <DependentUpon>frmGantiPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="Account\frmGantiPassword.aspx.vb">
      <DependentUpon>frmGantiPassword.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Account\Register.aspx.designer.vb">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>
    <Compile Include="Account\Register.aspx.vb">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Account\SignIn.aspx.designer.vb">
      <DependentUpon>SignIn.aspx</DependentUpon>
    </Compile>
    <Compile Include="Account\SignIn.aspx.vb">
      <DependentUpon>SignIn.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="App_Start\IdentityConfig.vb" />
    <Compile Include="App_Start\Startup.Auth.vb" />
    <Compile Include="Article.aspx.designer.vb">
      <DependentUpon>Article.aspx</DependentUpon>
    </Compile>
    <Compile Include="Article.aspx.vb">
      <DependentUpon>Article.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BootStrapper.vb" />
    <Compile Include="Code\DBContext.vb" />
    <Compile Include="Controls\ctrl_area.ascx.designer.vb">
      <DependentUpon>ctrl_area.ascx</DependentUpon>
    </Compile>
    <Compile Include="Controls\ctrl_area.ascx.vb">
      <DependentUpon>ctrl_area.ascx</DependentUpon>
    </Compile>
    <Compile Include="Controls\ctrl_karyawan_datadinas.ascx.designer.vb">
      <DependentUpon>ctrl_karyawan_datadinas.ascx</DependentUpon>
    </Compile>
    <Compile Include="Controls\ctrl_karyawan_datadinas.ascx.vb">
      <DependentUpon>ctrl_karyawan_datadinas.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.vb">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.vb">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Default1.aspx.designer.vb">
      <DependentUpon>Default1.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default1.aspx.vb">
      <DependentUpon>Default1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Extension\ExcelPackageExtensions.vb" />
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapAbsensiPerKaryawan.aspx.designer.vb">
      <DependentUpon>frmLapAbsensiPerKaryawan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapAbsensiPerKaryawan.aspx.vb">
      <DependentUpon>frmLapAbsensiPerKaryawan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapCutiPerKaryawan.aspx.designer.vb">
      <DependentUpon>frmLapCutiPerKaryawan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapCutiPerKaryawan.aspx.vb">
      <DependentUpon>frmLapCutiPerKaryawan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapRekapAbsen.aspx.designer.vb">
      <DependentUpon>frmLapRekapAbsen.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapRekapAbsen.aspx.vb">
      <DependentUpon>frmLapRekapAbsen.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakBulanan.aspx.designer.vb">
      <DependentUpon>frmLapPajakBulanan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakBulanan.aspx.vb">
      <DependentUpon>frmLapPajakBulanan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakPerKaryawan.aspx.designer.vb">
      <DependentUpon>frmLapPajakPerKaryawan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakPerKaryawan.aspx.vb">
      <DependentUpon>frmLapPajakPerKaryawan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakTahunan.aspx.designer.vb">
      <DependentUpon>frmLapPajakTahunan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakTahunan.aspx.vb">
      <DependentUpon>frmLapPajakTahunan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapGajiKeBank.aspx.designer.vb">
      <DependentUpon>frmRekapGajiKeBank.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapGajiKeBank.aspx.vb">
      <DependentUpon>frmRekapGajiKeBank.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapThrKeBank.aspx.designer.vb">
      <DependentUpon>frmRekapThrKeBank.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapThrKeBank.aspx.vb">
      <DependentUpon>frmRekapThrKeBank.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipThr.aspx.designer.vb">
      <DependentUpon>frmSlipThr.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipThr.aspx.vb">
      <DependentUpon>frmSlipThr.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Pinjaman\frmLapRekapPinjaman.aspx.designer.vb">
      <DependentUpon>frmLapRekapPinjaman.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Pinjaman\frmLapRekapPinjaman.aspx.vb">
      <DependentUpon>frmLapRekapPinjaman.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmEventSetting.aspx.designer.vb">
      <DependentUpon>frmEventSetting.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmEventSetting.aspx.vb">
      <DependentUpon>frmEventSetting.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmEventSettingRelease.aspx.designer.vb">
      <DependentUpon>frmEventSettingRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmEventSettingRelease.aspx.vb">
      <DependentUpon>frmEventSettingRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataDinas.aspx.designer.vb">
      <DependentUpon>frmImportDataDinas.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataDinas.aspx.vb">
      <DependentUpon>frmImportDataDinas.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataKaryawan.aspx.designer.vb">
      <DependentUpon>frmImportDataKaryawan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataKaryawan.aspx.vb">
      <DependentUpon>frmImportDataKaryawan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataKontrak.aspx.designer.vb">
      <DependentUpon>frmImportDataKontrak.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmImportDataKontrak.aspx.vb">
      <DependentUpon>frmImportDataKontrak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJadwalStore.aspx.designer.vb">
      <DependentUpon>frmJadwalStore.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJadwalStore.aspx.vb">
      <DependentUpon>frmJadwalStore.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJadwalStoreRelease.aspx.designer.vb">
      <DependentUpon>frmJadwalStoreRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJadwalStoreRelease.aspx.vb">
      <DependentUpon>frmJadwalStoreRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmMasterBonusTHR.aspx.designer.vb">
      <DependentUpon>frmMasterBonusTHR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmMasterBonusTHR.aspx.vb">
      <DependentUpon>frmMasterBonusTHR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSACuti.aspx.designer.vb">
      <DependentUpon>frmSACuti.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSACuti.aspx.vb">
      <DependentUpon>frmSACuti.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSalaryEntry.aspx.designer.vb">
      <DependentUpon>frmSalaryEntry.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSalaryEntry.aspx.vb">
      <DependentUpon>frmSalaryEntry.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSettingAbsenHariKhusus.aspx.designer.vb">
      <DependentUpon>frmSettingAbsenHariKhusus.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSettingAbsenHariKhusus.aspx.vb">
      <DependentUpon>frmSettingAbsenHariKhusus.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmShiftAbsensi.aspx.designer.vb">
      <DependentUpon>frmShiftAbsensi.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmShiftAbsensi.aspx.vb">
      <DependentUpon>frmShiftAbsensi.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmTunjanganOneTime.aspx.designer.vb">
      <DependentUpon>frmTunjanganOneTime.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmTunjanganOneTime.aspx.vb">
      <DependentUpon>frmTunjanganOneTime.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjaman.aspx.designer.vb">
      <DependentUpon>frmBayarPinjaman.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjaman.aspx.vb">
      <DependentUpon>frmBayarPinjaman.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjamanRelease.aspx.designer.vb">
      <DependentUpon>frmBayarPinjamanRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjamanRelease.aspx.vb">
      <DependentUpon>frmBayarPinjamanRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjaman.aspx.designer.vb">
      <DependentUpon>frmRegPinjaman.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjaman.aspx.vb">
      <DependentUpon>frmRegPinjaman.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipGaji.aspx.designer.vb">
      <DependentUpon>frmSlipGaji.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipGaji.aspx.vb">
      <DependentUpon>frmSlipGaji.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmDepartment.aspx.designer.vb">
      <DependentUpon>frmDepartment.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmDepartment.aspx.vb">
      <DependentUpon>frmDepartment.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJabatan.aspx.designer.vb">
      <DependentUpon>frmJabatan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmJabatan.aspx.vb">
      <DependentUpon>frmJabatan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmKaryawan.aspx.designer.vb">
      <DependentUpon>frmKaryawan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmKaryawan.aspx.vb">
      <DependentUpon>frmKaryawan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmLiburNasional.aspx.designer.vb">
      <DependentUpon>frmLiburNasional.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmLiburNasional.aspx.vb">
      <DependentUpon>frmLiburNasional.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmPendidikan.aspx.designer.vb">
      <DependentUpon>frmPendidikan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmPendidikan.aspx.vb">
      <DependentUpon>frmPendidikan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSettingAbsen.aspx.designer.vb">
      <DependentUpon>frmSettingAbsen.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmSettingAbsen.aspx.vb">
      <DependentUpon>frmSettingAbsen.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmStatusPerkawinan.aspx.designer.vb">
      <DependentUpon>frmStatusPerkawinan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Master\frmStatusPerkawinan.aspx.vb">
      <DependentUpon>frmStatusPerkawinan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Mutasi\frmMutasiDataDinas.aspx.designer.vb">
      <DependentUpon>frmMutasiDataDinas.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Mutasi\frmMutasiDataDinas.aspx.vb">
      <DependentUpon>frmMutasiDataDinas.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjamanRelease.aspx.designer.vb">
      <DependentUpon>frmRegPinjamanRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjamanRelease.aspx.vb">
      <DependentUpon>frmRegPinjamanRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmAbsensi.aspx.designer.vb">
      <DependentUpon>frmAbsensi.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmAbsensi.aspx.vb">
      <DependentUpon>frmAbsensi.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmGaji.aspx.designer.vb">
      <DependentUpon>frmGaji.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmGaji.aspx.vb">
      <DependentUpon>frmGaji.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmGajiRelease.aspx.designer.vb">
      <DependentUpon>frmGajiRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmGajiRelease.aspx.vb">
      <DependentUpon>frmGajiRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmIjin.aspx.designer.vb">
      <DependentUpon>frmIjin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmIjin.aspx.vb">
      <DependentUpon>frmIjin.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmIjinRelease.aspx.designer.vb">
      <DependentUpon>frmIjinRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmIjinRelease.aspx.vb">
      <DependentUpon>frmIjinRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmImportAbsen.aspx.designer.vb">
      <DependentUpon>frmImportAbsen.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmImportAbsen.aspx.vb">
      <DependentUpon>frmImportAbsen.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKaryawanBerhenti.aspx.designer.vb">
      <DependentUpon>frmKaryawanBerhenti.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKaryawanBerhenti.aspx.vb">
      <DependentUpon>frmKaryawanBerhenti.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrak.aspx.designer.vb">
      <DependentUpon>frmKompensasiKontrak.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrak.aspx.vb">
      <DependentUpon>frmKompensasiKontrak.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrakRelease.aspx.designer.vb">
      <DependentUpon>frmKompensasiKontrakRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrakRelease.aspx.vb">
      <DependentUpon>frmKompensasiKontrakRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmSPKLembur.aspx.designer.vb">
      <DependentUpon>frmSPKLembur.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmSPKLembur.aspx.vb">
      <DependentUpon>frmSPKLembur.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmSPKLemburRelease.aspx.designer.vb">
      <DependentUpon>frmSPKLemburRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmSPKLemburRelease.aspx.vb">
      <DependentUpon>frmSPKLemburRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmThr.aspx.designer.vb">
      <DependentUpon>frmThr.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmThr.aspx.vb">
      <DependentUpon>frmThr.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmThrRelease.aspx.designer.vb">
      <DependentUpon>frmThrRelease.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmThrRelease.aspx.vb">
      <DependentUpon>frmThrRelease.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmUbahJamMasuk.aspx.designer.vb">
      <DependentUpon>frmUbahJamMasuk.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\HRDandPayroll\Transaksi\frmUbahJamMasuk.aspx.vb">
      <DependentUpon>frmUbahJamMasuk.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\Setting\frmArea.aspx.designer.vb">
      <DependentUpon>frmArea.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\Setting\frmArea.aspx.vb">
      <DependentUpon>frmArea.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\Setting\frmCabang.aspx.designer.vb">
      <DependentUpon>frmCabang.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\Setting\frmCabang.aspx.vb">
      <DependentUpon>frmCabang.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\Setting\frmRole.aspx.designer.vb">
      <DependentUpon>frmRole.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\Setting\frmRole.aspx.vb">
      <DependentUpon>frmRole.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Forms\Setting\frmUser.aspx.designer.vb">
      <DependentUpon>frmUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="Forms\Setting\frmUser.aspx.vb">
      <DependentUpon>frmUser.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Global.asax.vb">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="GridView.aspx.designer.vb">
      <DependentUpon>GridView.aspx</DependentUpon>
    </Compile>
    <Compile Include="GridView.aspx.vb">
      <DependentUpon>GridView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="GridViewDetailsPage.aspx.designer.vb">
      <DependentUpon>GridViewDetailsPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="GridViewDetailsPage.aspx.vb">
      <DependentUpon>GridViewDetailsPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MapperConfig.vb" />
    <Compile Include="Models\IdentityModels.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="PageBase.vb">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Root.master.designer.vb">
      <DependentUpon>Root.master</DependentUpon>
    </Compile>
    <Compile Include="Root.master.vb">
      <DependentUpon>Root.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Scheduler.aspx.designer.vb">
      <DependentUpon>Scheduler.aspx</DependentUpon>
    </Compile>
    <Compile Include="Scheduler.aspx.vb">
      <DependentUpon>Scheduler.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Service\HRDWebService.asmx.vb">
      <DependentUpon>HRDWebService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Startup.vb" />
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapAbsenPerkaryawan.ascx.designer.vb">
      <DependentUpon>ucLapAbsenPerkaryawan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapAbsenPerkaryawan.ascx.vb">
      <DependentUpon>ucLapAbsenPerkaryawan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapCutiPerkaryawan.ascx.designer.vb">
      <DependentUpon>ucLapCutiPerkaryawan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapCutiPerkaryawan.ascx.vb">
      <DependentUpon>ucLapCutiPerkaryawan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakBulanan.ascx.designer.vb">
      <DependentUpon>ucLapPajakBulanan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakBulanan.ascx.vb">
      <DependentUpon>ucLapPajakBulanan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakPerKaryawan.ascx.designer.vb">
      <DependentUpon>ucLapPajakPerKaryawan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakPerKaryawan.ascx.vb">
      <DependentUpon>ucLapPajakPerKaryawan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakTahunan.ascx.designer.vb">
      <DependentUpon>ucLapPajakTahunan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapPajakTahunan.ascx.vb">
      <DependentUpon>ucLapPajakTahunan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapRekapPinjaman.ascx.designer.vb">
      <DependentUpon>ucLapRekapPinjaman.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucLapRekapPinjaman.ascx.vb">
      <DependentUpon>ucLapRekapPinjaman.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapAbsen.ascx.designer.vb">
      <DependentUpon>ucRekapAbsen.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapAbsen.ascx.vb">
      <DependentUpon>ucRekapAbsen.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapGajiBank.ascx.designer.vb">
      <DependentUpon>ucRekapGajiBank.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapGajiBank.ascx.vb">
      <DependentUpon>ucRekapGajiBank.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapThrBank.ascx.designer.vb">
      <DependentUpon>ucRekapThrBank.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucRekapThrBank.ascx.vb">
      <DependentUpon>ucRekapThrBank.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucSlipGaji.ascx.designer.vb">
      <DependentUpon>ucSlipGaji.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucSlipGaji.ascx.vb">
      <DependentUpon>ucSlipGaji.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucSlipThr.ascx.designer.vb">
      <DependentUpon>ucSlipThr.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Laporan\ucSlipThr.ascx.vb">
      <DependentUpon>ucSlipThr.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucDepartment_edit.ascx.designer.vb">
      <DependentUpon>ucDepartment_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucDepartment_edit.ascx.vb">
      <DependentUpon>ucDepartment_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucDepartment_list.ascx.designer.vb">
      <DependentUpon>ucDepartment_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucDepartment_list.ascx.vb">
      <DependentUpon>ucDepartment_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_edit.ascx.designer.vb">
      <DependentUpon>ucEventSetting_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_edit.ascx.vb">
      <DependentUpon>ucEventSetting_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_list.ascx.designer.vb">
      <DependentUpon>ucEventSetting_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_list.ascx.vb">
      <DependentUpon>ucEventSetting_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_release.ascx.designer.vb">
      <DependentUpon>ucEventSetting_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucEventSetting_release.ascx.vb">
      <DependentUpon>ucEventSetting_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucHariLibur_edit.ascx.designer.vb">
      <DependentUpon>ucHariLibur_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucHariLibur_edit.ascx.vb">
      <DependentUpon>ucHariLibur_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucHariLibur_list.ascx.designer.vb">
      <DependentUpon>ucHariLibur_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucHariLibur_list.ascx.vb">
      <DependentUpon>ucHariLibur_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataDinas.ascx.designer.vb">
      <DependentUpon>ucImportDataDinas.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataDinas.ascx.vb">
      <DependentUpon>ucImportDataDinas.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataKaryawan.ascx.designer.vb">
      <DependentUpon>ucImportDataKaryawan.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataKaryawan.ascx.vb">
      <DependentUpon>ucImportDataKaryawan.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataKontrak.ascx.designer.vb">
      <DependentUpon>ucImportDataKontrak.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucImportDataKontrak.ascx.vb">
      <DependentUpon>ucImportDataKontrak.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJabatan_edit.ascx.designer.vb">
      <DependentUpon>ucJabatan_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJabatan_edit.ascx.vb">
      <DependentUpon>ucJabatan_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJabatan_list.ascx.designer.vb">
      <DependentUpon>ucJabatan_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJabatan_list.ascx.vb">
      <DependentUpon>ucJabatan_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_edit.ascx.designer.vb">
      <DependentUpon>ucJadwalStore_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_edit.ascx.vb">
      <DependentUpon>ucJadwalStore_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_list.ascx.designer.vb">
      <DependentUpon>ucJadwalStore_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_list.ascx.vb">
      <DependentUpon>ucJadwalStore_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_release.ascx.designer.vb">
      <DependentUpon>ucJadwalStore_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucJadwalStore_release.ascx.vb">
      <DependentUpon>ucJadwalStore_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucKaryawan_edit.ascx.designer.vb">
      <DependentUpon>ucKaryawan_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucKaryawan_edit.ascx.vb">
      <DependentUpon>ucKaryawan_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucKaryawan_list.ascx.designer.vb">
      <DependentUpon>ucKaryawan_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucKaryawan_list.ascx.vb">
      <DependentUpon>ucKaryawan_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucMasterBonus_edit.ascx.designer.vb">
      <DependentUpon>ucMasterBonus_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucMasterBonus_edit.ascx.vb">
      <DependentUpon>ucMasterBonus_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucMasterBonus_list.ascx.designer.vb">
      <DependentUpon>ucMasterBonus_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucMasterBonus_list.ascx.vb">
      <DependentUpon>ucMasterBonus_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucPendidikan_edit.ascx.designer.vb">
      <DependentUpon>ucPendidikan_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucPendidikan_edit.ascx.vb">
      <DependentUpon>ucPendidikan_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucPendidikan_list.ascx.designer.vb">
      <DependentUpon>ucPendidikan_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucPendidikan_list.ascx.vb">
      <DependentUpon>ucPendidikan_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSACuti_list.ascx.designer.vb">
      <DependentUpon>ucSACuti_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSACuti_list.ascx.vb">
      <DependentUpon>ucSACuti_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSalaryEntry_edit.ascx.designer.vb">
      <DependentUpon>ucSalaryEntry_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSalaryEntry_edit.ascx.vb">
      <DependentUpon>ucSalaryEntry_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSalaryEntry_list.ascx.designer.vb">
      <DependentUpon>ucSalaryEntry_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSalaryEntry_list.ascx.vb">
      <DependentUpon>ucSalaryEntry_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_edit.ascx.designer.vb">
      <DependentUpon>ucSettingAbsenHariKhusus_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_edit.ascx.vb">
      <DependentUpon>ucSettingAbsenHariKhusus_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_list.ascx.designer.vb">
      <DependentUpon>ucSettingAbsenHariKhusus_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_list.ascx.vb">
      <DependentUpon>ucSettingAbsenHariKhusus_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsen_edit.ascx.designer.vb">
      <DependentUpon>ucSettingAbsen_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsen_edit.ascx.vb">
      <DependentUpon>ucSettingAbsen_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsen_list.ascx.designer.vb">
      <DependentUpon>ucSettingAbsen_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucSettingAbsen_list.ascx.vb">
      <DependentUpon>ucSettingAbsen_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucShiftAbsensi.ascx.designer.vb">
      <DependentUpon>ucShiftAbsensi.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucShiftAbsensi.ascx.vb">
      <DependentUpon>ucShiftAbsensi.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_edit.ascx.designer.vb">
      <DependentUpon>ucStatusPerkawinan_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_edit.ascx.vb">
      <DependentUpon>ucStatusPerkawinan_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_list.ascx.designer.vb">
      <DependentUpon>ucStatusPerkawinan_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_list.ascx.vb">
      <DependentUpon>ucStatusPerkawinan_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucTunjangamOT_edit.ascx.designer.vb">
      <DependentUpon>ucTunjangamOT_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucTunjangamOT_edit.ascx.vb">
      <DependentUpon>ucTunjangamOT_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucTunjangamOT_list.ascx.designer.vb">
      <DependentUpon>ucTunjangamOT_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Master\ucTunjangamOT_list.ascx.vb">
      <DependentUpon>ucTunjangamOT_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Mutasi\ucDataDinas_edit.ascx.designer.vb">
      <DependentUpon>ucDataDinas_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Mutasi\ucDataDinas_edit.ascx.vb">
      <DependentUpon>ucDataDinas_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Mutasi\ucDataDinas_list.ascx.designer.vb">
      <DependentUpon>ucDataDinas_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Mutasi\ucDataDinas_list.ascx.vb">
      <DependentUpon>ucDataDinas_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_edit.ascx.designer.vb">
      <DependentUpon>ucBayarPinjaman_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_edit.ascx.vb">
      <DependentUpon>ucBayarPinjaman_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_list.ascx.designer.vb">
      <DependentUpon>ucBayarPinjaman_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_list.ascx.vb">
      <DependentUpon>ucBayarPinjaman_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_release.ascx.designer.vb">
      <DependentUpon>ucBayarPinjaman_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_release.ascx.vb">
      <DependentUpon>ucBayarPinjaman_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_edit.ascx.designer.vb">
      <DependentUpon>ucRegPinjaman_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_edit.ascx.vb">
      <DependentUpon>ucRegPinjaman_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_list.ascx.designer.vb">
      <DependentUpon>ucRegPinjaman_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_list.ascx.vb">
      <DependentUpon>ucRegPinjaman_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_release.ascx.designer.vb">
      <DependentUpon>ucRegPinjaman_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_release.ascx.vb">
      <DependentUpon>ucRegPinjaman_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucAbsensi_edit.ascx.designer.vb">
      <DependentUpon>ucAbsensi_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucAbsensi_edit.ascx.vb">
      <DependentUpon>ucAbsensi_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucAbsensi_list.ascx.designer.vb">
      <DependentUpon>ucAbsensi_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucAbsensi_list.ascx.vb">
      <DependentUpon>ucAbsensi_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_edit.ascx.designer.vb">
      <DependentUpon>ucGaji_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_edit.ascx.vb">
      <DependentUpon>ucGaji_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_list.ascx.designer.vb">
      <DependentUpon>ucGaji_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_list.ascx.vb">
      <DependentUpon>ucGaji_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_release.ascx.designer.vb">
      <DependentUpon>ucGaji_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucGaji_release.ascx.vb">
      <DependentUpon>ucGaji_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_edit.ascx.designer.vb">
      <DependentUpon>ucIjin_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_edit.ascx.vb">
      <DependentUpon>ucIjin_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_list.ascx.designer.vb">
      <DependentUpon>ucIjin_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_list.ascx.vb">
      <DependentUpon>ucIjin_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_release.ascx.designer.vb">
      <DependentUpon>ucIjin_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucIjin_release.ascx.vb">
      <DependentUpon>ucIjin_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucImportAbsensi_list.ascx.designer.vb">
      <DependentUpon>ucImportAbsensi_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucImportAbsensi_list.ascx.vb">
      <DependentUpon>ucImportAbsensi_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_edit.ascx.designer.vb">
      <DependentUpon>ucKaryawanBerhenti_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_edit.ascx.vb">
      <DependentUpon>ucKaryawanBerhenti_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_list.ascx.designer.vb">
      <DependentUpon>ucKaryawanBerhenti_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_list.ascx.vb">
      <DependentUpon>ucKaryawanBerhenti_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_edit.ascx.designer.vb">
      <DependentUpon>ucKompensasiKontrak_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_edit.ascx.vb">
      <DependentUpon>ucKompensasiKontrak_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_list.ascx.designer.vb">
      <DependentUpon>ucKompensasiKontrak_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_list.ascx.vb">
      <DependentUpon>ucKompensasiKontrak_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_release.ascx.designer.vb">
      <DependentUpon>ucKompensasiKontrak_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_release.ascx.vb">
      <DependentUpon>ucKompensasiKontrak_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_edit.ascx.designer.vb">
      <DependentUpon>ucSPKLembur_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_edit.ascx.vb">
      <DependentUpon>ucSPKLembur_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_list.ascx.designer.vb">
      <DependentUpon>ucSPKLembur_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_list.ascx.vb">
      <DependentUpon>ucSPKLembur_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_release.ascx.designer.vb">
      <DependentUpon>ucSPKLembur_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_release.ascx.vb">
      <DependentUpon>ucSPKLembur_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_edit.ascx.designer.vb">
      <DependentUpon>ucThr_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_edit.ascx.vb">
      <DependentUpon>ucThr_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_list.ascx.designer.vb">
      <DependentUpon>ucThr_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_list.ascx.vb">
      <DependentUpon>ucThr_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_release.ascx.designer.vb">
      <DependentUpon>ucThr_release.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucThr_release.ascx.vb">
      <DependentUpon>ucThr_release.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucUbahJamMasuk.ascx.designer.vb">
      <DependentUpon>ucUbahJamMasuk.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\Transaksi\ucUbahJamMasuk.ascx.vb">
      <DependentUpon>ucUbahJamMasuk.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\HRDandPayroll\ucDashboards.ascx.designer.vb">
      <DependentUpon>ucDashboards.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\HRDandPayroll\ucDashboards.ascx.vb">
      <DependentUpon>ucDashboards.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Profil\ucGantiPassword_edit.ascx.designer.vb">
      <DependentUpon>ucGantiPassword_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Profil\ucGantiPassword_edit.ascx.vb">
      <DependentUpon>ucGantiPassword_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Profil\ucGantiPassword_list.ascx.designer.vb">
      <DependentUpon>ucGantiPassword_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Profil\ucGantiPassword_list.ascx.vb">
      <DependentUpon>ucGantiPassword_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucArea_edit.ascx.designer.vb">
      <DependentUpon>ucArea_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucArea_edit.ascx.vb">
      <DependentUpon>ucArea_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucArea_list.ascx.designer.vb">
      <DependentUpon>ucArea_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucArea_list.ascx.vb">
      <DependentUpon>ucArea_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucCabang_edit.ascx.designer.vb">
      <DependentUpon>ucCabang_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucCabang_edit.ascx.vb">
      <DependentUpon>ucCabang_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucCabang_list.ascx.designer.vb">
      <DependentUpon>ucCabang_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucCabang_list.ascx.vb">
      <DependentUpon>ucCabang_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucRole_edit.ascx.designer.vb">
      <DependentUpon>ucRole_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucRole_edit.ascx.vb">
      <DependentUpon>ucRole_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucRole_list.ascx.designer.vb">
      <DependentUpon>ucRole_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucRole_list.ascx.vb">
      <DependentUpon>ucRole_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucUser_edit.ascx.designer.vb">
      <DependentUpon>ucUser_edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucUser_edit.ascx.vb">
      <DependentUpon>ucUser_edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\Setting\ucUser_list.ascx.designer.vb">
      <DependentUpon>ucUser_list.ascx</DependentUpon>
    </Compile>
    <Compile Include="Views\Setting\ucUser_list.ascx.vb">
      <DependentUpon>ucUser_list.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Account\frmGantiPassword.aspx" />
    <Content Include="Account\Register.aspx" />
    <Content Include="Account\SignIn.aspx" />
    <Content Include="App_Data\ApplicationMenuDataSource.sitemap" />
    <Content Include="App_Data\Article.html" />
    <Content Include="App_Data\ArticleContents.xml" />
    <Content Include="App_Data\Overview.html" />
    <Content Include="App_Data\OverviewContents.xml" />
    <Content Include="Article.aspx" />
    <Content Include="Content\Content.css" />
    <Content Include="Content\GridView.css" />
    <Content Include="Content\GridView.js" />
    <Content Include="Content\Images\adaptive-menu.svg" />
    <Content Include="Content\Images\add.svg" />
    <Content Include="Content\Images\application.svg" />
    <Content Include="Content\Images\Article-white.svg" />
    <Content Include="Content\Images\Article.svg" />
    <Content Include="Content\Images\back.svg" />
    <Content Include="Content\Images\delete.svg" />
    <Content Include="Content\Images\double-close.svg" />
    <Content Include="Content\Images\double.svg" />
    <Content Include="Content\Images\edit.svg" />
    <Content Include="Content\Images\export.svg" />
    <Content Include="Content\Images\eye-button-hide.svg" />
    <Content Include="Content\Images\eye-button.svg" />
    <Content Include="Content\Images\GridView-white.svg" />
    <Content Include="Content\Images\GridView.svg" />
    <Content Include="Content\Images\Home-white.svg" />
    <Content Include="Content\Images\Home.svg" />
    <Content Include="Content\Images\image-sample.png" />
    <Content Include="Content\Images\kind1-white.svg" />
    <Content Include="Content\Images\kind1.svg" />
    <Content Include="Content\Images\kind2-white.svg" />
    <Content Include="Content\Images\kind2.svg" />
    <Content Include="Content\Images\logo.svg" />
    <Content Include="Content\Images\logo-small.svg" />
    <Content Include="Content\Images\logo_2.svg" />
    <Content Include="Content\Images\logo_bak.svg" />
    <Content Include="Content\Images\menu.svg" />
    <Content Include="Content\Images\logo_1.svg" />
    <Content Include="Content\Images\Overview\article.png" />
    <Content Include="Content\Images\Overview\demo.png" />
    <Content Include="Content\Images\Overview\grid.png" />
    <Content Include="Content\Images\Overview\login.gif" />
    <Content Include="Content\Images\Overview\login.png" />
    <Content Include="Content\Images\Overview\menu.gif" />
    <Content Include="Content\Images\Overview\scheduler.png" />
    <Content Include="Content\Images\Overview\sidebar.gif" />
    <Content Include="Content\Images\Overview\sticky-footer.gif" />
    <Content Include="Content\Images\Overview\toolbar.gif" />
    <Content Include="Content\Images\print.svg" />
    <Content Include="Content\Images\priority1-white.svg" />
    <Content Include="Content\Images\priority1.svg" />
    <Content Include="Content\Images\priority2-white.svg" />
    <Content Include="Content\Images\priority2.svg" />
    <Content Include="Content\Images\priority3-white.svg" />
    <Content Include="Content\Images\priority3.svg" />
    <Content Include="Content\Images\Scheduler-white.svg" />
    <Content Include="Content\Images\Scheduler.svg" />
    <Content Include="Content\Images\search-selected.svg" />
    <Content Include="Content\Images\search.svg" />
    <Content Include="Content\Images\sign-out.svg" />
    <Content Include="Content\Images\user.svg" />
    <Content Include="Content\Layout.css" />
    <Content Include="Content\Photo\Alberto_Alonso.jpg" />
    <Content Include="Content\Photo\Alfredo_Gomez.jpg" />
    <Content Include="Content\Photo\Andrew_Lee.jpg" />
    <Content Include="Content\Photo\Angela_Murphy.jpg" />
    <Content Include="Content\Photo\Clarence_Nath.jpg" />
    <Content Include="Content\Photo\Colin_He.jpg" />
    <Content Include="Content\Photo\Connor_Jenkins.jpg" />
    <Content Include="Content\Photo\Connor_Lopez.jpg" />
    <Content Include="Content\Photo\Destiny_Clark.jpg" />
    <Content Include="Content\Photo\Diana_Martin.jpg" />
    <Content Include="Content\Photo\Gary_Rubio.jpg" />
    <Content Include="Content\Photo\Heidi_Lopez.jpg" />
    <Content Include="Content\Photo\Jesse_Gonzalez.jpg" />
    <Content Include="Content\Photo\Jessie_She.jpg" />
    <Content Include="Content\Photo\Julia_Bell.jpg" />
    <Content Include="Content\Photo\Julia_Evans.jpg" />
    <Content Include="Content\Photo\Katelyn_Lopez.jpg" />
    <Content Include="Content\Photo\Kevin_Collins.jpg" />
    <Content Include="Content\Photo\Logan_Hernandez.jpg" />
    <Content Include="Content\Photo\Maria_Hernandez.jpg" />
    <Content Include="Content\Photo\Martha_Gao.jpg" />
    <Content Include="Content\Photo\Megan_Sanchez.jpg" />
    <Content Include="Content\Photo\Miguel_Jones.jpg" />
    <Content Include="Content\Photo\Naomi_Moreno.jpg" />
    <Content Include="Content\Photo\Nathan_Bryant.jpg" />
    <Content Include="Content\Photo\Rafael_Raje.jpg" />
    <Content Include="Content\Photo\Rebekah_Raman.jpg" />
    <Content Include="Content\Photo\Shannon_Sanz.jpg" />
    <Content Include="Content\Photo\User.png" />
    <Content Include="Content\Photo\Xavier_Richardson.jpg" />
    <Content Include="Content\Scheduler.css" />
    <Content Include="Content\Scheduler.js" />
    <Content Include="Content\Script.js" />
    <Content Include="Content\SignInRegister.css" />
    <Content Include="Content\SignInRegister.js" />
    <Content Include="Controls\ctrl_area.ascx" />
    <Content Include="Controls\ctrl_karyawan_datadinas.ascx" />
    <Content Include="Default.aspx" />
    <Content Include="Default1.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapAbsensiPerKaryawan.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapCutiPerKaryawan.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Absensi\frmLapRekapAbsen.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakBulanan.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakPerKaryawan.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmLapPajakTahunan.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapGajiKeBank.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmRekapThrKeBank.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipThr.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Pinjaman\frmLapRekapPinjaman.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmEventSetting.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmEventSettingRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmImportDataDinas.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmImportDataKaryawan.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmImportDataKontrak.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmJadwalStore.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmJadwalStoreRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmMasterBonusTHR.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmSACuti.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmSalaryEntry.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmSettingAbsenHariKhusus.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmShiftAbsensi.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmTunjanganOneTime.aspx" />
    <Content Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjaman.aspx" />
    <Content Include="Forms\HRDandPayroll\Pinjaman\frmBayarPinjamanRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjaman.aspx" />
    <Content Include="Forms\HRDandPayroll\Laporan\Gaji\frmSlipGaji.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmDepartment.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmJabatan.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmKaryawan.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmLiburNasional.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmPendidikan.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmSettingAbsen.aspx" />
    <Content Include="Forms\HRDandPayroll\Master\frmStatusPerkawinan.aspx" />
    <Content Include="Forms\HRDandPayroll\Mutasi\frmMutasiDataDinas.aspx" />
    <Content Include="Forms\HRDandPayroll\Pinjaman\frmRegPinjamanRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmAbsensi.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmGaji.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmGajiRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmIjin.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmIjinRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmImportAbsen.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmKaryawanBerhenti.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrak.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmKompensasiKontrakRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmSPKLembur.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmSPKLemburRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmThr.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmThrRelease.aspx" />
    <Content Include="Forms\HRDandPayroll\Transaksi\frmUbahJamMasuk.aspx" />
    <Content Include="Forms\Setting\frmArea.aspx" />
    <Content Include="Forms\Setting\frmCabang.aspx" />
    <Content Include="Forms\Setting\frmRole.aspx" />
    <Content Include="Forms\Setting\frmUser.aspx" />
    <Content Include="Global.asax" />
    <Content Include="GridView.aspx" />
    <Content Include="GridViewDetailsPage.aspx" />
    <Content Include="Scheduler.aspx" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\PublishProfiles\FolderProfile.pubxml" />
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <Content Include="Root.master" />
    <None Include="Scripts\jquery-3.7.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.7.1.js" />
    <Content Include="Scripts\jquery-3.7.1.min.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.min.js" />
    <Content Include="Service\HRDWebService.asmx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapAbsenPerkaryawan.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapCutiPerkaryawan.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapPajakBulanan.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapPajakPerKaryawan.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapPajakTahunan.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucLapRekapPinjaman.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucRekapAbsen.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucRekapGajiBank.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucRekapThrBank.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucSlipGaji.ascx" />
    <Content Include="Views\HRDandPayroll\Laporan\ucSlipThr.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucDepartment_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucDepartment_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucEventSetting_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucEventSetting_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucEventSetting_release.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucHariLibur_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucHariLibur_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucImportDataDinas.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucImportDataKaryawan.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucImportDataKontrak.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucJabatan_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucJabatan_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucJadwalStore_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucJadwalStore_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucJadwalStore_release.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucKaryawan_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucKaryawan_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucMasterBonus_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucMasterBonus_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucPendidikan_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucPendidikan_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSACuti_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSalaryEntry_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSalaryEntry_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSettingAbsenHariKhusus_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSettingAbsen_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucSettingAbsen_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucShiftAbsensi.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucStatusPerkawinan_list.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucTunjangamOT_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Master\ucTunjangamOT_list.ascx" />
    <Content Include="Views\HRDandPayroll\Mutasi\ucDataDinas_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Mutasi\ucDataDinas_list.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_list.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucBayarPinjaman_release.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_list.ascx" />
    <Content Include="Views\HRDandPayroll\Pinjaman\ucRegPinjaman_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucAbsensi_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucAbsensi_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucGaji_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucGaji_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucGaji_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucIjin_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucIjin_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucIjin_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucImportAbsensi_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucKaryawanBerhenti_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucKompensasiKontrak_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucSPKLembur_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucThr_edit.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucThr_list.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucThr_release.ascx" />
    <Content Include="Views\HRDandPayroll\Transaksi\ucUbahJamMasuk.ascx" />
    <Content Include="Views\HRDandPayroll\ucDashboards.ascx" />
    <Content Include="Views\Profil\ucGantiPassword_edit.ascx" />
    <Content Include="Views\Profil\ucGantiPassword_list.ascx" />
    <Content Include="Views\Setting\ucArea_edit.ascx" />
    <Content Include="Views\Setting\ucArea_list.ascx" />
    <Content Include="Views\Setting\ucCabang_edit.ascx" />
    <Content Include="Views\Setting\ucCabang_list.ascx" />
    <Content Include="Views\Setting\ucRole_edit.ascx" />
    <Content Include="Views\Setting\ucRole_list.ascx" />
    <Content Include="Views\Setting\ucUser_edit.ascx" />
    <Content Include="Views\Setting\ucUser_list.ascx" />
    <Content Include="Web.config" />
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.7.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.7.1.min.map" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="Web.sitemap" />
    <!--<Content Include="Site.Master" />-->
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HRD.Application\HRD.Application.vbproj">
      <Project>{6DB5B377-9F8E-4758-8A53-4BF13AAF4DCF}</Project>
      <Name>HRD.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Controller\HRD.Controller.vbproj">
      <Project>{1C9DDDEA-22BA-4F3F-8248-6016A9452DB7}</Project>
      <Name>HRD.Controller</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Dao\HRD.Dao.vbproj">
      <Project>{c2185db9-aecc-41b7-af08-fc7bdd4091b2}</Project>
      <Name>HRD.Dao</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Domain\HRD.Domain.vbproj">
      <Project>{93a2fb6b-4503-4da1-80f4-ba3ca6dbb7d8}</Project>
      <Name>HRD.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Helpers\HRD.Helpers.vbproj">
      <Project>{A149668F-9D7F-44E3-981B-56661A2D8D77}</Project>
      <Name>HRD.Helpers</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Infrastructure\HRD.Infrastructure.vbproj">
      <Project>{902D25F8-78E1-4E7A-8578-4D15DD9801D9}</Project>
      <Name>HRD.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\HRD.Reports\HRD.Reports.vbproj">
      <Project>{27ae5973-d179-4406-87cb-544350a354d0}</Project>
      <Name>HRD.Reports</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\Dashboards\" />
    <Folder Include="App_Resources\system\log\" />
  </ItemGroup>
  <!--<ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>-->
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>20882</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:20882/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\EmptyLicensesLicx.3.0.0\build\EmptyLicensesLicx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EmptyLicensesLicx.3.0.0\build\EmptyLicensesLicx.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\EmptyLicensesLicx.3.0.0\build\EmptyLicensesLicx.targets" Condition="Exists('..\packages\EmptyLicensesLicx.3.0.0\build\EmptyLicensesLicx.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>