﻿Imports System.Web.UI.HtmlControls
Imports System.Web
Imports System.IO
Imports System
Public NotInheritable Class Filer
    Public Shared Function ReadFromFile(ByVal filePath As String) As String
        Dim fStream As FileStream

        ' Reading the file content.
        fStream = New FileStream(filePath, FileMode.OpenOrCreate, FileAccess.Read)
        Dim sReader As New StreamReader(fStream)
        Dim line As String = sReader.ReadToEnd()
        sReader.Close()

        Return line
    End Function

    Public Shared Function GetBytesFromStream(ByVal stream As Stream) As Byte()
        Dim size As Integer = Convert.ToInt32(stream.Length)
        Dim bytes(size - 1) As Byte
        stream.Read(bytes, 0, size)
        Return bytes
    End Function
End Class
