﻿Imports HRD.Domain
Public Class EventSettingService
    Implements IEventSettingService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly errorMessageLog As IErrorMessageLog

    Public Sub New(unitOfWork As IUnitOfWork, errorMessageLog As IErrorMessageLog)
        _unitOfWork = unitOfWork
        Me.errorMessageLog = errorMessageLog
    End Sub
    Private Sub Log(ByVal method As String, ByVal msg As String)
        errorMessageLog.LogError("Application", "Event Setting Service", method, msg)
    End Sub

    Public Async Function GetEventSettingsAsync() As Task(Of ResponseModel) Implements IEventSettingService.GetEventSettingsAsync
        Try
            Dim os = _unitOfWork.Repository(Of tm_event_setting)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetEventSettingsAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try


    End Function

    Public Async Function GetEventSettingByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements IEventSettingService.GetEventSettingByIdAsync
        Try
            Dim os = _unitOfWork.Repository(Of tm_event_setting)().TableNoTracking.Where(Function(f) f.Id = Id).FirstOrDefault

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, os)
        Catch ex As Exception
            Log(NameOf(Me.GetEventSettingByIdAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tm_event_setting) As Task(Of ResponseModel) Implements IEventSettingService.UpsertAsync
        Try
            Dim oSave = New tm_event_setting With {
                .Id = o.Id,
                .NamaEvent = o.NamaEvent,
            .Area_Id = o.Area_Id,
            .Cabang_id = o.Cabang_id,
            .CreatedBy = o.CreatedBy,
            .CreatedDate = o.CreatedDate,
            .EndDate = o.EndDate,
            .Keterangan = o.Keterangan,
            .ModifiedBy = o.ModifiedBy,
            .ModifiedDate = o.CreatedDate,
            .MsKeluar = o.MsKeluar,
            .MsMasuk = o.MsMasuk,
            .Posted = o.Posted,
            .PostedBy = o.PostedBy,
            .PostedDate = o.PostedDate,
            .StartDate = o.StartDate
            }

            If oSave.Id > 0 Then
                _unitOfWork.Repository(Of tm_event_setting)().Update(oSave)
            Else
                Await _unitOfWork.Repository(Of tm_event_setting)().AddAsync(oSave)
            End If

            For Each x In o.tm_event_setting_line
                Dim y As New tm_event_setting_line With {.Deleting = x.Deleting, .Id = x.Id, .Event_id = x.Event_id, .Karyawan_id = x.Karyawan_id, .RowGuid = x.RowGuid}
                If y.Deleting Then
                    If y.Id > 0 Then
                        Await _unitOfWork.Repository(Of tm_event_setting_line).Delete(y)
                    End If
                Else
                    If y.Id <= 0 Then
                        y.tm_event_setting = oSave
                        _unitOfWork.Repository(Of tm_event_setting_line).AddAsync(y).GetAwaiter.GetResult()
                    Else
                        _unitOfWork.Repository(Of tm_event_setting_line).Update(y)
                    End If
                End If
            Next

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements IEventSettingService.DeleteAsync
        Try

            Dim o = Await _unitOfWork.Repository(Of tm_event_setting)().Get(Id)
            If o IsNot Nothing Then
                _unitOfWork.Repository(Of tm_event_setting_line).removeRange(o.tm_event_setting_line)
                Await _unitOfWork.Repository(Of tm_event_setting).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function Posting(entity As tm_event_setting) As Task(Of ResponseModel)
        Try
            Dim o = Await _unitOfWork.Repository(Of tm_event_setting).Get(entity.Id)
            If o Is Nothing Then
                Return ResponseModel.FailureResponse("Not Found")
            End If
            o.Posted = True
            o.PostedBy = entity.PostedBy
            o.PostedDate = Now

            _unitOfWork.Repository(Of tm_event_setting).Update(o)

            UpdateAbsensi(entity)

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.Posting), ex.Message)

            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try


    End Function
    Private Function UpdateAbsensi(entity As tm_event_setting)

        For Each x In entity.tm_event_setting_line

            Dim abs = _unitOfWork.Repository(Of tr_absensi)().GetAll().Where(Function(a) a.karyawan_id = x.Karyawan_id And a.Tanggal >= entity.StartDate And a.Tanggal <= entity.EndDate).ToList()
            For Each a In abs
                Dim settingMasuk As DateTime = String.Format("{0} {1}", Format(a.Tanggal, "yyyy-MM-dd"), Format(entity.MsMasuk, "HH:mm:ss"))
                If a.Masuk_b Then
                    If a.Terlambat > 0 Then
                        a.Keterangan = "Event : " & entity.NamaEvent
                        a.Terlambat = DateDiff(DateInterval.Minute, settingMasuk, a.JamMasuk.Value)
                        If a.Terlambat < 0 Then
                            a.Terlambat = 0
                        End If
                    End If
                End If

                _unitOfWork.Repository(Of tr_absensi).Update(a)

            Next

        Next
    End Function
    Public Async Function Unposting(entity As tm_event_setting) As Task(Of ResponseModel)
        Try
            ' Mengambil data berdasarkan ID entity
            Dim o = Await _unitOfWork.Repository(Of tm_event_setting).Get(entity.Id)
            If o Is Nothing Then
                Return ResponseModel.FailureResponse("Not Found")
            End If

            ' Mengubah status "posting" menjadi tidak diposting
            o.Posted = False
            o.ModifiedBy = entity.ModifiedBy
            o.PostedDate = Now

            ' Memperbarui data di repository
            _unitOfWork.Repository(Of tm_event_setting).Update(o)

            ' Memperbarui absensi terkait jika diperlukan (disesuaikan dengan logika Anda)
            'UpdateAbsensi(entity)

            ' Menyimpan perubahan ke database
            _unitOfWork.Save()

            ' Mengembalikan respons berhasil
            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            ' Logging error jika terjadi
            Log(NameOf(Me.Unposting), ex.Message)

            ' Mengembalikan respons gagal
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

End Class
