﻿Imports HRD.Application
Imports HRD.Controller
Imports HRD.Domain
Imports HRD.Helpers
Imports StructureMap
Public Class BayarPinjamanController
    Inherits Controller(Of tr_pinjaman_bayar)
    Implements IBayarPinjamanListController, IBayarPinjamanEditController

    Public ReadOnly Property DataList(area_id As String, checked As Boolean) As IQueryable(Of tr_pinjaman_bayar) Implements IBayarPinjamanListController.DataList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of BayarPinjamanService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_pinjaman_bayar) = r.Output
                Dim _user = AuthHelper.GetLoggedInUserInfo
                os = os.Where(Function(f) f.Area_id = area_id)
                If checked Then
                    os = os.Where(Function(f) f.Posted = True)
                Else
                    os = os.Where(Function(f) f.Posted = False)
                End If

                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public ReadOnly Property PinjamanList(kar_id As String) As IEnumerable(Of tr_register_pinjaman) Implements IBayarPinjamanEditController.PinjamanList
        Get
            Dim Serv = ObjectFactory.GetInstance(Of RegisterPinjamanService)()
            Dim r = Serv.GetQueryableAsync.GetAwaiter.GetResult
            If r.Success Then
                Dim os As IQueryable(Of tr_register_pinjaman) = r.Output
                os = os.Where(Function(f) f.Karyawan_id = kar_id AndAlso f.Posted = True AndAlso f.tr_pinjaman_move.Sum(Function(s) s.Amount) > 0)
                Return os
            Else
                sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
                Return Nothing
            End If
        End Get
    End Property

    Public Overrides Sub AddNewItem()
        Action = Action.AddNew
        Dim _user = AuthHelper.GetLoggedInUserInfo
        SelectedItem = New tr_pinjaman_bayar With {.Area_id = _user.Area_id, .Cabang_id = _user.Cabang_id, .Tanggal = Now}
    End Sub

    Public Overrides Sub SelectItem(id As String)

        Dim Serv = ObjectFactory.GetInstance(Of BayarPinjamanService)()
        Dim r = Serv.GetByIdAsync(id).GetAwaiter.GetResult
        If r.Success Then
            SelectedItem = r.Output
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If

    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Dim Serv = ObjectFactory.GetInstance(Of BayarPinjamanService)()
        Dim r = Serv.DeleteAsync(id).GetAwaiter.GetResult
        If r.Success Then
            sMsg = MessageFormatter.GetFormattedSuccessMessage(r.Message)
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Overrides Sub Saving()

    End Sub

    Public Overrides Sub Saving(TEntity As tr_pinjaman_bayar)
        If Action = Action.AddNew Then
            TEntity.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.CreatedDate = Now
        ElseIf Action = Action.Posting Then

        Else
            TEntity.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
            TEntity.ModifiedDate = Now
        End If



        Dim Serv = ObjectFactory.GetInstance(Of BayarPinjamanService)()
        Dim r = Serv.UpsertAsync(TEntity).GetAwaiter.GetResult
        If r.Success Then
            sMsg = MessageFormatter.GetFormattedSuccessMessage(r.Message)
            Saved = True
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub

    Public Sub Posting(TEntity As tr_pinjaman_bayar) Implements IBayarPinjamanEditController.Posting
        TEntity.Posted = True
        TEntity.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
        TEntity.PostedDate = Now

        Dim Serv = ObjectFactory.GetInstance(Of BayarPinjamanService)()
        Dim r = Serv.Posting(TEntity).GetAwaiter.GetResult
        If r.Success Then
            sMsg = MessageFormatter.GetFormattedSuccessMessage(r.Message)
            Saved = True
            Reset()
        Else
            sMsg = MessageFormatter.GetFormattedErrorMessage(r.Message)
        End If
    End Sub
End Class
Public Interface IBayarPinjamanListController
    Inherits IControllerMain, IControllerList

    ReadOnly Property DataList(area_id As String, checked As Boolean) As IQueryable(Of tr_pinjaman_bayar)
End Interface
Public Interface IBayarPinjamanEditController
    Inherits IControllerMain, IControllerEdit(Of tr_pinjaman_bayar)

    ReadOnly Property PinjamanList(kar_id As String) As IEnumerable(Of tr_register_pinjaman)
    Sub Posting(oSelectItem As tr_pinjaman_bayar)
End Interface