﻿Imports HRD.Domain
Public Class TunjanganOnetimeService
    Implements ITunjanganOnetimeService

    Private ReadOnly _unitOfWork As IUnitOfWork
    Private ReadOnly _errorMessageLog As IErrorMessageLog

    Public Sub New(ByVal unitOfWork As IUnitOfWork, ByVal errorMessageLog As IErrorMessageLog)

        _unitOfWork = unitOfWork
        _errorMessageLog = errorMessageLog
    End Sub

    Private Sub Log(ByVal method As String, ByVal msg As String)
        _errorMessageLog.LogError("Application", "TunjanganOnetime Service", method, msg)
    End Sub

    Public Async Function GetTunjanganOnetimesAsync() As Task(Of ResponseModel) Implements ITunjanganOnetimeService.GetTunjanganOnetimesAsync

        Try
            Dim tunjanganOnetimes = _unitOfWork.Repository(Of tr_tunjangan_onetime)().TableNoTracking.OrderBy(Function(t) t.Id)

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, tunjanganOnetimes)
        Catch ex As Exception
            Log(NameOf(Me.GetTunjanganOnetimesAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function GetTunjanganOnetimeByIdAsync(Id As Integer) As Task(Of ResponseModel) Implements ITunjanganOnetimeService.GetTunjanganOnetimeByIdAsync

        Try
            Dim tunjanganOnetime = Await _unitOfWork.Repository(Of tr_tunjangan_onetime)().Get(Id)
            If tunjanganOnetime IsNot Nothing Then
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, tunjanganOnetime)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.GetTunjanganOnetimeByIdAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function UpsertAsync(o As tr_tunjangan_onetime) As Task(Of ResponseModel) Implements ITunjanganOnetimeService.UpsertAsync

        Try
            If o.Id > 0 Then
                _unitOfWork.Repository(Of tr_tunjangan_onetime)().Update(o)
            Else
                Await _unitOfWork.Repository(Of tr_tunjangan_onetime)().AddAsync(o)
            End If

            _unitOfWork.Save()

            Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, o)
        Catch ex As Exception
            Log(NameOf(Me.UpsertAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function

    Public Async Function DeleteAsync(Id As Integer) As Task(Of ResponseModel) Implements ITunjanganOnetimeService.DeleteAsync

        Try
            Dim tunjanganOnetime = _unitOfWork.Repository(Of tr_tunjangan_onetime)().Get(Id)
            If tunjanganOnetime IsNot Nothing Then
                Await _unitOfWork.Repository(Of tr_tunjangan_onetime).DeleteAsync(Id)
                _unitOfWork.Save()
                Return ResponseModel.SuccessResponse(GlobalDeclaration._successResponse, Nothing)
            Else
                Return ResponseModel.FailureResponse("Not Found")
            End If
        Catch ex As Exception
            Log(NameOf(Me.DeleteAsync), ex.Message)
            Return ResponseModel.FailureResponse(GlobalDeclaration._internalServerError)
        End Try
    End Function
End Class
