﻿Imports System.IO
Imports System.Threading.Tasks
Imports DevExpress.Web
Imports DevExpress.XtraReports.UI
Imports HRD.Application
Imports HRD.Domain
Imports HRD.Helpers
Imports HRD.Reports
Imports StructureMap
Public Class ucRekapThrBank
    Inherits System.Web.UI.UserControl

    Sub ShowRPT()
        Dim cls As New clsRPT
        Dim rpt = cls.GetRpt_RekapTHRBank(txt_periode.Value, cb_area.Value, cb_cabang.Value)
        Me.ASPxWebDocumentViewer1.OpenReport(rpt)


    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
            cb_cabang.Value = AuthHelper.GetLoggedInUserInfo.Cabang_id

            txt_periode.Value = Now.Date
        End If
    End Sub
    Private Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        MyMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        MyMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value)
    End Sub
End Class