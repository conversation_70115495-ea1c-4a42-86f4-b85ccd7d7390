﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucMasterBonus_edit.ascx.vb" Inherits="HRDv23.ucMasterBonus_edit" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>
<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <Items>
        <dx:TabbedLayoutGroup ColSpan="1">
            <Items>
                <dx:LayoutGroup Caption="General" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem Caption="Area" ColSpan="1" FieldName="Area_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_area" runat="server" CallbackPageSize="10" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_cabang.PerformCallback();
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeArea">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaArea">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Cabang" ColSpan="1" FieldName="Cabang_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_cabang" runat="server" CallbackPageSize="10" ClientInstanceName="cb_cabang" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" SelectedIndexChanged="function(s, e) {
	cb_karyawan.PerformCallback();
	//mycb.PerformCallback('get_setting');
}" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="KodeCabang">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="NamaCabang">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Karyawan" ColSpan="1" FieldName="Karyawan_id">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxComboBox ID="cb_karyawan" runat="server" CallbackPageSize="10" ClientInstanceName="cb_karyawan" EnableCallbackMode="True" NullValueItemDisplayText="{0} - {1}" TextFormatString="{0} - {1}" ValueField="Id" ValueType="System.Int32">
                                        <ClientSideEvents Init="onInitCB" />
                                        <Columns>
                                            <dx:ListBoxColumn FieldName="NIK">
                                            </dx:ListBoxColumn>
                                            <dx:ListBoxColumn FieldName="Nama">
                                            </dx:ListBoxColumn>
                                        </Columns>
                                        <ValidationSettings SetFocusOnError="True">
                                            <RequiredField ErrorText="Required" IsRequired="True" />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Periode" Caption="Periode THR">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E5" runat="server" DisplayFormatString="MMM yyyy" EditFormat="Custom" EditFormatString="MM-yyyy">
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Bonus" Caption="Bonus">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxSpinEdit ID="txt_insentif" runat="server" AllowMouseWheel="False" DisplayFormatString="n2" Increment="0" Number="0">
                                        <SpinButtons ShowIncrementButtons="False">
                                        </SpinButtons>
                                        <ClientSideEvents ValueChanged="function(s, e) {
	CalculateAssuransi(s.GetValue(),cb_karyawan.GetValue(),cb_cabang.GetValue());
}" />
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxSpinEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="Keterangan">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxMemo ID="ASPxFormLayout1_E17" runat="server">
                                    </dx:ASPxMemo>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
                <dx:LayoutGroup Caption="Audit Info" ColCount="2" ColSpan="1" ColumnCount="2">
                    <Items>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E11" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="CreatedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E12" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedBy">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxTextBox ID="ASPxFormLayout1_E13" runat="server" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" FieldName="ModifiedDate">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <dx:ASPxDateEdit ID="ASPxFormLayout1_E14" runat="server" DisplayFormatString="dd MMM yyyy HH:mm" EditFormat="Custom" EditFormatString="dd MMM yyyy HH:mm" ReadOnly="True">
                                        <ReadOnlyStyle BackColor="#CCCCCC">
                                        </ReadOnlyStyle>
                                    </dx:ASPxDateEdit>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:LayoutGroup>
            </Items>
        </dx:TabbedLayoutGroup>
        <dx:LayoutGroup Caption="Action" ColCount="2" ColSpan="1" ColumnCount="2">
            <Items>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_save" runat="server" AutoPostBack="False" Text="Save" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	if (ASPxClientEdit.ValidateGroup(null) == true) { 	
		cp_area.PerformCallback('save');
		s.SetEnabled(false);
	};

}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
                <dx:LayoutItem ColSpan="1" ShowCaption="False">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer runat="server">
                            <dx:ASPxButton ID="btn_back" runat="server" AutoPostBack="False" CausesValidation="False" Text="Back" UseSubmitBehavior="False">
                                <ClientSideEvents Click="function(s, e) {
	cp_area.PerformCallback('back');
}" />
                            </dx:ASPxButton>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>
    </Items>
</dx:ASPxFormLayout>
