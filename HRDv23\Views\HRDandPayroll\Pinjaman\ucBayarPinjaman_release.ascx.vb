﻿Imports ILS.MVVM
Imports HRD.Controller
Imports DevExpress.Data.Linq
Imports DevExpress.Web
Imports HRD.Helpers

Public Class ucBayarPinjaman_release
    Inherits System.Web.UI.UserControl

    <Inject>
    Property Controller As IBayarPinjamanListController


    <EventSubscription>
    Public Sub OnListChanged(sender As Object, e As EventArgs)
        ASPxGridView1.DataBind()

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            cb_area.Value = AuthHelper.GetLoggedInUserInfo.Area_id
        End If
    End Sub

    Private Sub EntityServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles EntityServerModeDataSource1.Selecting
        If Controller Is Nothing Then
            Return
        End If

        e.DefaultSorting = "Id Desc"

        e.QueryableSource = Controller.DataList(cb_area.Value, False).AsQueryable
    End Sub

    Private Sub ASPxGridView1_CustomButtonInitialize(sender As Object, e As ASPxGridViewCustomButtonEventArgs) Handles ASPxGridView1.CustomButtonInitialize
        If e.CellType = GridViewTableCommandCellType.Filter Then
            Return
        End If
        If e.VisibleIndex = -1 Then
            Return
        End If
        Dim b As Boolean = CType(sender, ASPxGridView).GetRowValues(e.VisibleIndex, "Posted")

        Select Case e.ButtonID
            Case "btn_edit", "btn_delete"
                e.Visible = If(b, DevExpress.Utils.DefaultBoolean.False, DevExpress.Utils.DefaultBoolean.True)
            Case "btn_view"
                e.Visible = If(b, DevExpress.Utils.DefaultBoolean.True, DevExpress.Utils.DefaultBoolean.False)


        End Select
    End Sub

    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        MyMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        MyMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub
End Class