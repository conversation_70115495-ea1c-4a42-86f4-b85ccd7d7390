﻿Partial Public Class tm_bonus_thr
    Public Shared Function Create(TEntity As tm_bonus_thr) As tm_bonus_thr
        Dim o As New tm_bonus_thr With {
              .Id = TEntity.Id,
                .Area_id = TEntity.Area_id,
                .Keterangan = TEntity.Keterangan,
                .Bonus = TEntity.Bonus,
                .CreatedBy = TEntity.CreatedBy,
                .CreatedDate = TEntity.CreatedDate,
                .ModifiedBy = TEntity.ModifiedBy,
                .ModifiedDate = TEntity.ModifiedDate,
                .Bulan = TEntity.Bulan,
                .Cabang_id = TEntity.Cabang_id,
                .Karyawan_id = TEntity.Karyawan_id,
                .Periode = TEntity.Periode,
                .Posted = TEntity.Posted,
                .PostedBy = TEntity.PostedBy,
                .PostedDate = TEntity.PostedDate,
                .Tahun = TEntity.Tahun
    }
        Return o
    End Function

    Public Function AsQueryable() As IQueryable
        Throw New NotImplementedException()
    End Function
End Class
