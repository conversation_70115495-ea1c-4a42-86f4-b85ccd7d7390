﻿Imports System.Data.Entity.Core.Common.CommandTrees
Imports System.IO
Imports System.Xml
Imports DevExpress.DashboardCommon
Imports DevExpress.DashboardWeb
Imports DevExpress.Data.Filtering
Imports DevExpress.Data.Linq
Imports DevExpress.DataAccess.EntityFramework
Imports HRD.Controller
Imports HRD.Dao
Imports HRD.Domain
Imports HRD.Helpers
Public Class ucDashboards
    Inherits System.Web.UI.UserControl

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim dashboardFileStorage As New DashboardFileStorage("~/App_Data/Dashboards")
        'ASPxDashboard1.SetDashboardStorage(dashboardFileStorage)

        'ASPxDashboard1.SetDataSourceStorage(CreateDataSourceStorage())


    End Sub

    Private Sub LinqServerModeDataSource1_Selecting(sender As Object, e As LinqServerModeDataSourceSelectEventArgs) Handles LinqServerModeDataSource1.Selecting

        Dim ctx As New HRDEntities
        Dim tgl As Date = Now.AddMonths(1)
        Dim os = ctx.vw_KaryawanAkanHabisKontrak.AsNoTracking.Where(Function(f) f.TglAkhirKontrak <= tgl)
        Dim spec As New HabisKontrakSpec
        os = spec.SatisfyingEntitiesInQuery(os)

        e.DefaultSorting = "TglAkhirKontrak Asc"
        e.QueryableSource = os.AsQueryable
    End Sub

    Public Function CreateDataSourceStorage() As DataSourceInMemoryStorage
        Dim dataSourceStorage As New DataSourceInMemoryStorage()

        Dim efDataSource As New DashboardEFDataSource("EF Core Data Source")
        efDataSource.ConnectionParameters = New EFConnectionParameters(GetType(HRDEntities))

        Dim area_id = GetUserPermission()

        'efDataSource.Filters.FirstOrDefault(Function(x) x.Parameters.FirstOrDefault(Function(y) y.Name = "Area_id").Value = area_id)

        dataSourceStorage.RegisterDataSource("efDataSource", efDataSource.SaveToXml())

        Return dataSourceStorage
    End Function

    Private Function GetUserPermission() As String
        ' Mendapatkan permission user dari session, database, atau sumber lain
        If AuthHelper.GetLoggedInUserInfo Is Nothing Then
            Return 0
        End If
        Return AuthHelper.GetLoggedInUserInfo.Area_id
    End Function
End Class