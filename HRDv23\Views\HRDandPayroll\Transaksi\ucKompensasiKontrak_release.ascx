﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucKompensasiKontrak_release.ascx.vb" Inherits="HRDv23.ucKompensasiKontrak_release" %>
<%@ Register assembly="DevExpress.Web.v23.1, Version=23.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<table style="width: 100%;">
        <tr>
            <td style="width: 150px; font-weight: bold">Perusahaan</td>
            <td style="width: 350px">
                <dx:ASPxComboBox runat="server" ValueType="System.Int32" NullValueItemDisplayText="{0} - {1}" CallbackPageSize="10" EnableCallbackMode="True" ValueField="Id" TextFormatString="{0} - {1}" ID="cb_area" Width="100%">
                    <ClientSideEvents SelectedIndexChanged="function(s, e) {
	grd_kompensasi_kontrak_release.Refresh();
}" Init="onInitCB">
                    </ClientSideEvents>
                    <Columns>
                        <dx:ListBoxColumn FieldName="KodeArea" Caption="Kode Perusahaan">
                        </dx:ListBoxColumn>
                        <dx:ListBoxColumn FieldName="NamaArea" Caption="Nama Perusahaan">
                        </dx:ListBoxColumn>
                    </Columns>
                    <ValidationSettings SetFocusOnError="True">
                        <RequiredField IsRequired="True" ErrorText="Required">
                        </RequiredField>
                    </ValidationSettings>
                </dx:ASPxComboBox>
            </td>
            <td>&nbsp;</td>
        </tr>
        </table>    
    <dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" ClientInstanceName="grd_kompensasi_kontrak_release">
        <ClientSideEvents CustomButtonClick="function(s, e) {
	    var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	    if(e.buttonID!='btn_print'){
		    if(e.buttonID=='btn_delete'){
			    var b=confirm('Are you sure to delete this item?');
			    if(b){
				    cp_kompensasi_kontrak_release.PerformCallback(e.buttonID+';'+rowKey);
			    }
		    }else{cp_kompensasi_kontrak_release.PerformCallback(e.buttonID+';'+rowKey);}
	    }else{wd1.Show();}

    }" ToolbarItemClick="function(s, e) {
	    switch (e.item.name) { 
    case 'btn_new':
    cp_kompensasi_kontrak_release.PerformCallback('new'); 
    break;
    }

    }" />
        <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
        </SettingsAdaptivity>
        <SettingsPager AlwaysShowPager="True">
            <AllButton Visible="True">
            </AllButton>
            <PageSizeItemSettings Visible="True">
            </PageSizeItemSettings>
        </SettingsPager>
        <Settings ShowFilterRow="True" ShowFilterRowMenu="True" />
        <SettingsBehavior AllowFocusedRow="True" />
    <SettingsPopup>
    <FilterControl AutoUpdatePosition="False"></FilterControl>
    </SettingsPopup>
        <SettingsSearchPanel Visible="True" />
        <SettingsExport EnableClientSideExportAPI="True">
        </SettingsExport>
        <Columns>
            <dx:GridViewCommandColumn ShowClearFilterButton="True" VisibleIndex="0">
                <CustomButtons>
                    <dx:GridViewCommandColumnCustomButton ID="btn_release" Text="Release">
                        <Image IconID="iconbuilder_security_key_svg_16x16">
                        </Image>
                    </dx:GridViewCommandColumnCustomButton>
                </CustomButtons>
            </dx:GridViewCommandColumn>
            <dx:GridViewDataTextColumn FieldName="tm_cabang.NamaCabang" VisibleIndex="2" Caption="Cabang">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="NoKompensasi" VisibleIndex="3">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="Periode" VisibleIndex="4">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataDateColumn FieldName="PeriodeDate" VisibleIndex="5" Caption="Tanggal">
                <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
                </PropertiesDateEdit>
            </dx:GridViewDataDateColumn>
            <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="7">
            </dx:GridViewDataTextColumn>
            <dx:GridViewDataTextColumn FieldName="Total" VisibleIndex="6">
                <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
                </PropertiesTextEdit>
            </dx:GridViewDataTextColumn>
        </Columns>
        <Toolbars>
            <dx:GridViewToolbar>
                <Items>
                    <dx:GridViewToolbarItem Name="btn_new" Text="New">
                        <Image IconID="actions_new_svg_16x16">
                        </Image>
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ShowSearchPanel">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Command="ShowFilterRow">
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem BeginGroup="True" Command="ExportToXlsx">
                    </dx:GridViewToolbarItem>
                </Items>
            </dx:GridViewToolbar>
        </Toolbars>
    </dx:ASPxGridView>

    <dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="HRD.Domain.HRDEntities" EnableDelete="True" EnableInsert="True" EnableUpdate="True" TableName="tr_kompensasi_kontrak">
    </dx:EntityServerModeDataSource>
