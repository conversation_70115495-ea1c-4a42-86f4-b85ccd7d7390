﻿Imports DevExpress.XtraReports.UI
Imports HRD.Domain
Imports HRD.Helpers
Public Class rpt_absensi_perkaryawan
    Dim i As Integer
    Public pAlldays As Integer
    Public phariKerja As Integer

    Public startDate As Date
    Public endDate As Date

    Dim jmlHariKerja As Integer
    Dim jmlOff As Integer
    Dim tipeAbsensi As Integer

    Dim kar_id As String
    Private Sub Detail_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles Detail.BeforePrint


        i += 1
        cell_no.Text = $"{i}."

        Dim o = CType(Me.GetCurrentRow(), tr_absensi)
        If o Is Nothing Then
            Exit Sub
        End If
        ' Contoh kondisi untuk akumulasi
        If o.Off_b AndAlso o.CutiBersama <> True Then
            jmlOff += 1
        End If
        'If o.Alpha_b Or o.Masuk_b Or o.Ijin_b Or o.Cuti_b Then
        '    jmlHariKerja += 1
        'End If
        tipeAbsensi = o.tm_karyawan.TipeAbsensiOFF
        kar_id = o.karyawan_id
    End Sub

    Private Sub GroupHeader1_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles GroupHeader1.BeforePrint
        i = 0

        jmlHariKerja = 0
        jmlOff = 0
    End Sub

    Private Sub fHariKerja_GetValue(sender As Object, e As GetValueEventArgs) Handles fHariKerja.GetValue
        'Dim o = CType(e.Row, tr_absensi)

        'If o.tm_karyawan.TipeAbsensiOFF = 0 Then
        '    e.Value = phariKerja
        'Else
        '    'e.Value = pAlldays
        '    If o.Alpha_b Or o.Masuk_b Or o.Ijin_b Or o.Cuti_b Then
        '        jmlHariKerja += 1
        '    Else
        '        'e.Value = 0
        '    End If
        '    e.Value = jmlHariKerja
        'End If



    End Sub

    Private Sub fIjin_GetValue(sender As Object, e As GetValueEventArgs) Handles fIjin.GetValue
        Dim o = CType(e.Row, tr_absensi)
        If o.Ijin_b Then
            If o.tr_ijin.tm_ijin_master.KodeIjin <> "IP" Then
                e.Value = 1
            Else
                e.Value = 0
            End If
        Else
            e.Value = 0
        End If
    End Sub

    Private Sub fIMP_GetValue(sender As Object, e As GetValueEventArgs) Handles fIMP.GetValue
        Dim o = CType(e.Row, tr_absensi)
        If o.Ijin_b Then
            If o.tr_ijin.tm_ijin_master.KodeIjin = "IP" Then
                e.Value = 1
            Else
                e.Value = 0
            End If
        Else
            e.Value = 0
        End If
    End Sub

    Private Sub GroupFooter1_BeforePrint(sender As Object, e As ComponentModel.CancelEventArgs) Handles GroupFooter1.BeforePrint
        ' Misalnya, menampilkan nilai jmlHariKerja pada kontrol tertentu di footer
        Dim lblJmlHariKerja As XRLabel = CType(Me.FindControl("txt_jumlahharikerja", True), XRLabel)
        If lblJmlHariKerja IsNot Nothing Then
            If tipeAbsensi = 0 Or tipeAbsensi = 3 Then
                Dim gantiHari = myFunction.HitungGantiHari(kar_id, startDate, endDate)

                lblJmlHariKerja.Text = phariKerja + gantiHari
            Else

                lblJmlHariKerja.Text = pAlldays - jmlOff
            End If

        End If

        txt_saldoCuti.Text = myFunction.GetSaldoCuti(kar_id, endDate)

    End Sub

    Private Sub fLembur_GetValue(sender As Object, e As GetValueEventArgs) Handles fLembur.GetValue
        Dim o = CType(e.Row, tr_absensi)
        If o.OverTime_HLibur > 0 Then
            e.Value = 1
        Else
            e.Value = 0
        End If
    End Sub

    Private Sub fMasuk_GetValue(sender As Object, e As GetValueEventArgs) Handles fMasuk.GetValue
        Dim o = CType(e.Row, tr_absensi)
        If o.Masuk_b = True And o.OverTime_HLibur <= 0 Then
            e.Value = 1
        ElseIf o.Ijin_b Then
            If o.tr_ijin.tm_ijin_master.KodeIjin = "IP" Then
                e.Value = 1
            End If
        Else
            e.Value = 0
        End If
    End Sub


End Class