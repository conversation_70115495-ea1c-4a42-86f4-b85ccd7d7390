'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Collections.Generic

Partial Public Class tm_karyawan
    Public Property Id As Integer
    Public Property Area_id As Integer
    Public Property Cabang_id As Integer
    Public Property NIK As String
    Public Property Nama As String
    Public Property Tempat As String
    Public Property TglLahir As Nullable(Of Date)
    Public Property Umur As Nullable(Of Integer)
    Public Property JenisKelamin_id As Nullable(Of Integer)
    Public Property Agama_id As Nullable(Of Integer)
    Public Property Alamat As String
    Public Property Kota As String
    Public Property Telp As String
    Public Property StatusPerkawinan_id As Nullable(Of Integer)
    Public Property Pendidikan_id As Nullable(Of Integer)
    Public Property Jurusan_id As Nullable(Of Integer)
    Public Property Alumni As String
    Public Property TahunLulus As String
    Public Property TglMasuk As Date
    Public Property Berhenti_b As Boolean
    Public Property Tgl_Berhenti As Nullable(Of Date)
    Public Property AlasanBerhenti As String
    Public Property NamaBank_id As Nullable(Of Integer)
    Public Property NoRekening As String
    Public Property NamaAccount As String
    Public Property UserName As String
    Public Property Password As String
    Public Property Photo As Byte()
    Public Property Token As Nullable(Of System.Guid)
    Public Property TokenExpired As Nullable(Of Date)
    Public Property NoKTP As String
    Public Property Ship As Nullable(Of Integer)
    Public Property Createdby As String
    Public Property CreatedDate As Nullable(Of Date)
    Public Property ModifiedBy As String
    Public Property ModifiedDate As Nullable(Of Date)
    Public Property Version As Byte()
    Public Property NPWP As String
    Public Property IsBPJS_Kes As Boolean
    Public Property IsBPJS_TK As Boolean
    Public Property StatusPerkawinan_NonPTKP_id As Nullable(Of Integer)
    Public Property KaryawanTetap As Boolean
    Public Property TipeAbsensiOFF As Short
    Public Property Email As String
    Public Property TglReloadCuti As Nullable(Of Date)

    Public Overridable Property tm_agama As tm_agama
    Public Overridable Property tm_cabang As tm_cabang
    Public Overridable Property tm_event_setting_line As ICollection(Of tm_event_setting_line) = New HashSet(Of tm_event_setting_line)
    Public Overridable Property tm_jenis_kelamin As tm_jenis_kelamin
    Public Overridable Property tm_jurusan As tm_jurusan
    Public Overridable Property tm_karyawan_datadinas As ICollection(Of tm_karyawan_datadinas) = New HashSet(Of tm_karyawan_datadinas)
    Public Overridable Property tm_namabank As tm_namabank
    Public Overridable Property tm_pendidikan As tm_pendidikan
    Public Overridable Property tm_status_perkawinan As tm_status_perkawinan
    Public Overridable Property tm_status_PTKP As tm_status_PTKP
    Public Overridable Property tm_off_karyawan_line As ICollection(Of tm_off_karyawan_line) = New HashSet(Of tm_off_karyawan_line)
    Public Overridable Property tr_absensi As ICollection(Of tr_absensi) = New HashSet(Of tr_absensi)
    Public Overridable Property tr_gaji_line As ICollection(Of tr_gaji_line) = New HashSet(Of tr_gaji_line)
    Public Overridable Property tr_ijin As ICollection(Of tr_ijin) = New HashSet(Of tr_ijin)
    Public Overridable Property tr_kompensasi_kontrak_line As ICollection(Of tr_kompensasi_kontrak_line) = New HashSet(Of tr_kompensasi_kontrak_line)
    Public Overridable Property tr_pinjaman_bayar As ICollection(Of tr_pinjaman_bayar) = New HashSet(Of tr_pinjaman_bayar)
    Public Overridable Property tr_register_pinjaman As ICollection(Of tr_register_pinjaman) = New HashSet(Of tr_register_pinjaman)
    Public Overridable Property tr_spk_lembur_line As ICollection(Of tr_spk_lembur_line) = New HashSet(Of tr_spk_lembur_line)
    Public Overridable Property tr_tunjangan_onetime As ICollection(Of tr_tunjangan_onetime) = New HashSet(Of tr_tunjangan_onetime)
    Public Overridable Property tm_area As tm_area
    Public Overridable Property tm_bonus_thr As ICollection(Of tm_bonus_thr) = New HashSet(Of tm_bonus_thr)
    Public Overridable Property tr_thr_line As ICollection(Of tr_thr_line) = New HashSet(Of tr_thr_line)

End Class
