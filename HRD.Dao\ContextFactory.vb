﻿Imports System.Threading
Imports System.Web
Imports HRD.Domain

Public Class ContextFactory
    Implements IContextFactory

    Public Function GetContextPerRequest() As HRDEntities Implements IContextFactory.GetContextPerRequest
        Dim httpContext As HttpContext = httpContext.Current
        If httpContext Is Nothing Then
            Return New HRDEntities
        Else
            Dim contextId As Integer = Thread.CurrentContext.ContextID
            Dim hashCode As Integer = httpContext.GetHashCode()
            Dim key As String = String.Concat(hashCode, contextId)

            Dim context As HRDEntities = TryCast(httpContext.Items(key), HRDEntities)
            If context Is Nothing Then
                context = New HRDEntities
                httpContext.Items(key) = context
            End If

            Return context
        End If
    End Function
    Private Shared instance_Renamed As IContextFactory
    Private Shared syncRoot As New Object()
    Public Shared Property Instance() As IContextFactory
        Get
            SyncLock syncRoot
                If instance_Renamed Is Nothing Then
                    instance_Renamed = New ContextFactory()
                End If
            End SyncLock

            Return instance_Renamed
        End Get
        Set(ByVal value As IContextFactory)
            SyncLock syncRoot
                If value IsNot Nothing AndAlso instance_Renamed IsNot value Then
                    instance_Renamed = value
                End If
            End SyncLock
        End Set
    End Property

End Class
Public Interface IContextFactory
    Function GetContextPerRequest() As HRDEntities
End Interface